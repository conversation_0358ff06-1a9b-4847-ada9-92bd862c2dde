package fm.lizhi.ocean.wave.im.core.config;


import fm.lizhi.live.trade.services.RoomVipSpringService;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import fm.lizhi.xm.common.flow.services.security.ReviewChatSpringService;
import fm.lizhi.xm.security.chat.api.ReviewChatService;
import fm.lizhi.xm.social.chat.api.PpChatRelService;
import org.springframework.context.annotation.Configuration;

@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.xm.social.chat.api.PpChatService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = xm.fm.lizhi.live.pp.user.api.PpUserListInviteRoomService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = RoomVipSpringService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpChatRelService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = ReviewChatService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = ReviewChatSpringService.class),
})
public class XmImConfigApolloProvider {




}
