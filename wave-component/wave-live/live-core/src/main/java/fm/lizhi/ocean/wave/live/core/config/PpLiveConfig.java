package fm.lizhi.ocean.wave.live.core.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PpLiveConfig implements CommonLiveConfig {

    /**
     * 业务方进房发送公屏的模块标识
     */
    private int comeSource = 6;

    /**
     * 默认房间公告
     */
    private String defaultAnnouncement = "欢迎来到我的房间";

    private int liveTitleMinLength = 5;

    private int liveTitleMaxLength = 12;

    private int liveAnnouncementMaxLength = 4000;

    private String liveRoomBlockText = "由于你违反了《PP约玩个人直播服务协议》中的有关规定，我们暂时封禁了你的直播权限";

    private boolean pubLiveSendReviewSwitch = true;

    /**
     * 设备行为消息
     */
    private String deviceBehaviorMessageTopic = "pp_topic_enter_live_room";

    /**
     * 主播行为消息
     */
    private String anchorBehaviorMessageTopic = "lz_pp_topic_live_room_anchor_behavior_message";

    private String liveStateMessageTopic = "lz_pp_online_state_user_state";

    /**
     * 用户行为消息
     */
    private String userBehaviorMessageTopic = "lz_pp_topic_user_behavior_message";

    public boolean forbidEnterOthersRoom = true;


    /**
     * 管理员头像
     */
    private String roomManagerIconUrl = "https://cdn.lizhi.fm/studio/live_manager_new.png";
    /**
     * 房主头像
     */
    private String roomOwnerIconUrl = "https://cdn.lizhi.fm/studio/live_nj_new.png";

    private float userRoleImageBadgeAspect = 1;

    /**
     * 进房公告开关
     * mainConfig
     */
    private Boolean enterNoticeSwitch = true;

    /**
     * 座驾进房公告队列最大值
     * <p>
     * 默认 2000
     * app-hy-live mainConfig(apollo未找到)
     */
    public int vehicleEnterNoticeQueueMaxSize = 2000;

    /**
     * 座驾进房公告每次达多少数量可发送
     * <p>
     * 默认 50
     * app-hy-live mainConfig(apollo未找到)
     */
    public int vehicleEnterNoticeQueueSzieToSend = 50;

    /**
     * 进房通知，用户昵称最大长度限制
     * 配置未找到
     */
    private int enterNoticeUserMaxLen = 9;

    /**
     * 搜索白名单（userId），逗号分隔
     */
    private List<Long> searchWhitelist = new ArrayList<>();

    /**
     * 是否禁用搜索
     */
    private boolean disableSearch = false;

    /**
     * 禁用搜索文案
     */
    private String disableSearchMsg = "暂时无法搜索到该直播间";

    /**
     * 抽取搜索内容中的数字
     */
    private boolean searchExtractDigit = true;

    /**
     * 最大的搜索内容长度
     */
    private int searchMaxLen = 20;

    /**
     * 最小的搜索内容长度
     */
    private int searchMinLen = 1;

    /**
     * 超过最大搜索长度的文案
     */
    private String exceedMaxLenMsg = "输入内容过长，请重新输入";

    /**
     * 最小搜索长度的文案
     */
    private String exceedMinLenMsg = "输入内容过少，请重新输入";

    /**
     * 黑叶对接doremi appKey
     * 关联原配置 lz_hy_room -> call_config -> lizhiAppKey
     */
    private String normalVoiceAppKey = "Lizhi_PP_20191010";

    /**
     * 房间欢迎语默认文案
     */
    public String defaultLiveWelcomeMsg = "";

    /**
     * 手机开播是否实名认证，线上强制开启
     * appConfig
     */
    private boolean pubLiveCheckPermissionSwitch = true;

    /**
     * PP首页热门分类Id
     */
    private long hotTabCategoryId = 10009;

    private String wealthImgUrlIndex = "web_res";

    /**
     * 写作业推荐列表推荐直播间配置
     */
    private List<Long> recommendLiveIds = new ArrayList<>();

    private boolean openRecommendConfig = true;

    /**
     * 直播间分享链接
     */
    private String liveShareUrl;

    /**
     * 是否做麦位检查
     */
    private boolean checkOnMice = true;

    /**
     * 最小弹窗优化-显示品类下的直播数量
     */
    private int minDialogOptLive = 20;

    /**
     * 是否发送直播间信息统计开关
     */
    private boolean sendLiveStateMsgSwitch = true;

    /**
     * 官方频道列表
     */
    private List<Long> officialRoomNjIdList = new ArrayList<>();

    private String defaultStreamAppKey = "Lizhi_PP_20191010";

    /**
     * 是否启用容灾接口
     */
    private boolean homeLiveDisaster = false;

    /**
     * 发送进房消息的线程数
     */
    private int sendEnterNoticeThreadCount = 1;

    /**
     * 厅收入查询配置
     */
    private String roomIncomeConfig = "[{\"settleConfigCode\":\"hallHallIncomeTotalAmount\",\"name\":\"厅收礼收入\",\"weightType\":2,\"index\":1},{\"settleConfigCode\":\"hallNobilityRoyaltyIncomeTotalAmount\",\"name\":\"贵族提成\",\"weightType\":1,\"index\":2},{\"settleConfigCode\":\"hallIndividualIncomeTotalAmount\",\"name\":\"个播收入\",\"weightType\":1,\"index\":3}]";


    /**
     * 超管图标
     */
    private String roomSuperManagerIconUrl = "https://cdn.gzlzfm.com/studio/2022/01/10/2917480759303228982.png";

    /**
     * 热推ID
     */
    private String hotRecommendId = "10009";

    /**
     * 推荐卡名称列表
     */
    private String recommendCardPositionNameConfig = "热一A,热一B,热二A,热二B,热三A,热三B,热四A,热四B";

    /**
     * 首页推荐主播开关
     */
    private Boolean homeRecAnchorSwitch = true;


    /**
     * 首页推荐主播配置，json数组格式，数组中每个元素配置了主播推荐位的位置、页码、生效开始时间
     * 注意：
     * 1、根据生效开始时间判断哪一个元素是生效中的，生效开始时间为每个半小时周期的开始时间戳，比如11：00、11：30
     * 2、必须有一个元素是当前生效中的
     * 3、每个元素必须按照生效开始时间降序排序
     */
    private String homeRecAnchorConfig = "[{\"position\":5,\"pageNo\":1,\"startTime\":1719401400000}]";
}
