package fm.lizhi.ocean.wave.user.core.manager;

import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.assistant.api.FriendshipNotesService;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.bean.Live;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomRoleIcon;
import fm.lizhi.ocean.wave.api.live.bean.LiveStatusEnum;
import fm.lizhi.ocean.wave.api.live.bean.UserLiveRoomStatus;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.api.live.result.GetUsersLatestLiveResult;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.remote.AntiCheatServiceRemote;
import fm.lizhi.ocean.wave.common.util.Base64Utils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.context.Header;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import fm.lizhi.ocean.wave.user.constant.BanStatus;
import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import fm.lizhi.ocean.wave.user.constant.UserGenderEnum;
import fm.lizhi.ocean.wave.user.core.config.CommonUserConfig;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.constant.NjAndUserRelationEnum;
import fm.lizhi.ocean.wave.user.core.constant.UserMsgCodes;
import fm.lizhi.ocean.wave.user.core.constant.VerifyStatusConstant;
import fm.lizhi.ocean.wave.user.core.extension.attention.AttentionUserProcessor;
import fm.lizhi.ocean.wave.user.core.extension.report.IUserReportProccess;
import fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.ReportHeartBeatProcessor;
import fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.bean.ReportHeartBeatPostBean;
import fm.lizhi.ocean.wave.user.core.extension.sample.GetUserSampleProcessor;
import fm.lizhi.ocean.wave.user.core.extension.searchUser.ISearchUserProcessor;
import fm.lizhi.ocean.wave.user.core.extension.userdetail.GetUserDetailProcessor;
import fm.lizhi.ocean.wave.user.core.manager.strategy.UserTabGetContext;
import fm.lizhi.ocean.wave.user.core.manager.strategy.UserTabGetStrategy;
import fm.lizhi.ocean.wave.user.core.model.param.*;
import fm.lizhi.ocean.wave.user.core.model.vo.*;
import fm.lizhi.ocean.wave.user.core.remote.bean.ReportRecordVo;
import fm.lizhi.ocean.wave.user.core.remote.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.core.remote.bean.TailEffectVo;
import fm.lizhi.ocean.wave.user.core.remote.bean.UserFamilyInfo;
import fm.lizhi.ocean.wave.user.core.remote.param.*;
import fm.lizhi.ocean.wave.user.core.remote.result.*;
import fm.lizhi.ocean.wave.user.core.remote.service.*;
import fm.lizhi.ocean.wave.user.core.util.UserConverterUtils;
import fm.lizhi.ocean.wave.user.export.api.model.UserInfo;
import fm.lizhi.ocean.wave.user.export.api.param.QueryUserParam;
import fm.lizhi.ocean.wave.user.export.api.result.GetByAuthAccountIdResult;
import fm.lizhi.ocean.wave.user.export.api.result.QueryUserByParamResult;
import fm.lizhi.ocean.wave.user.export.api.service.UserService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserManager {

    private final ExecutorService executor = ThreadUtils.getTtlExecutors(getClass().getSimpleName());
    private final ListeningExecutorService refreshLocalCachePool = MoreExecutors.listeningDecorator(executor);

    @Autowired
    UserProviderConfig userProviderConfig;

    @Autowired
    CommonProviderConfig commonProviderConfig;

    @Autowired
    AntiCheatServiceRemote antiCheatServiceRemote;

    @MyAutowired
    IHotWordsServiceRemote hotWordsServiceRemote;

    @Autowired
    private FamilyService familyService;

    @MyAutowired
    private VipPrivilegeServiceRemote vipPrivilegeServiceRemote;

    @Autowired
    private ProcessorV2Factory factory;

    @Autowired
    private UserOnlineStatusManager userOnlineStatusManager;

    @Autowired
    private UserService userService;

    @MyAutowired
    private IUserServiceRemote userServiceRemote;
    @Autowired
    private ProcessorFactory processorFactory;

    @MyAutowired
    private IUserAssetsServiceRemote userAssetsServiceRemote;

    @MyAutowired
    private IUserFamilyServiceRemote userFamilyServiceRemote;

    @MyAutowired
    private IMedalServiceRemote medalServiceRemote;

    @Autowired
    private LiveService liveService;

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Autowired
    private List<UserTabGetStrategy> userTabGetStrategies;

    @Autowired
    private DressUpManager dressUpManager;

    @MyAutowired
    private IUserReportRemote userReportRemote;

    @Autowired
    private FriendshipNotesService friendshipNotesService;

    @Autowired
    private UserFamilyManager userFamilyManager;


    @Autowired
    private fm.lizhi.ocean.wave.user.api.UserService waveUserService;

    private final LoadingCache<String, Optional<GetSimpleUserResponse>> USER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10_000)
            .refreshAfterWrite(30, TimeUnit.SECONDS)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<GetSimpleUserResponse>>() {
                @Override
                public Optional<GetSimpleUserResponse> load(String key) {
                    String[] params = key.split("_");
                    if (params.length < 2) {
                        return Optional.empty();
                    }
                    return getSimpleUser(SimpleUserParam.builder()
                            .userId(Long.valueOf(params[0]))
                            .tgtUserId(Long.valueOf(params[1]))
                            .build());
                }

                @Override
                public ListenableFuture<Optional<GetSimpleUserResponse>> reload(String key, Optional<GetSimpleUserResponse> oldValue) throws Exception {
                    String[] params = key.split("_");
                    return refreshLocalCachePool.submit(() -> getSimpleUser(SimpleUserParam.builder()
                            .userId(Long.valueOf(params[0]))
                            .tgtUserId(Long.valueOf(params[1]))
                            .build()));
                }
            });

    /**
     * 查询资料详情页的动态tab列表
     *
     * @param targetUserId
     * @return
     */
    public List<String> tabList(long targetUserId) {
        List<String> tabList = new ArrayList<>();
        UserTabGetContext context = UserTabGetContext.builder()
                .targetUserId(targetUserId)
                .build();
        for (UserTabGetStrategy strategy : userTabGetStrategies) {
            if (strategy.isSupport(context)) {
                tabList.add(strategy.getTabKey());
            }
        }
        return tabList;
    }

    /**
     * 根据用户id获取用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    public UserInfo getUserById(Long userId) {
        // 查询用户信息
        QueryUserParam param = new QueryUserParam();
        param.setUserId(userId);
        Result<QueryUserByParamResult> result = userService.queryUserByParam(param);
        if (result.rCode() != 0) {
            log.error("queryUserByParam fail. rCode={}`userId={}", result.rCode(), userId);
            return null;
        }
        return result.target().getUserInfo();
    }

    /**
     * 根据鉴权账户id获取用户信息
     *
     * @param authAccountId 鉴权账户id
     * @return 用户信息
     */
    public UserInfo getUserByAuthAccountId(Long authAccountId) {
        Result<GetByAuthAccountIdResult> result = userService.getByAuthAccountId(authAccountId);
        if (result.rCode() != 0) {
            log.error("getByAuthAccountId fail. rCode={}`accountId={}", result.rCode(), authAccountId);
            return null;
        }

        return result.target().getUserInfo();
    }

    /**
     * 根据业务token获取用户id
     *
     * @param token 业务token
     * @return 用户id
     */
    public Long getUserIdByBusinessToken(String token) {
        // 根据业务token获取用户信息
        Result<Long> result = userServiceRemote.getUserIdByToken(token);
        if (result.rCode() != IUserServiceRemote.GET_H5_USER_ID_BY_TOKEN_SUCCESS) {
            // 根据业务token获取用户id失败
            log.error("getUserIdByToken fail. rCode={}`appToken={}", result.rCode(), token);
            return null;
        }

        return result.target();
    }

    /**
     * 检查用户封禁状态
     *
     * @param userId 用户id
     * @return true：被封禁，false：正常
     */
    public boolean checkUserBanStatus(Long userId) {
        // 检查封禁状态
        Result<GetUserBanStatusResult> result = userServiceRemote.getUserBanStatus(userId);
        if (result.rCode() != 0) {
            log.error("getUserBanStatus fail. rCode={}`userId={}", result.rCode(), userId);
            return true;
        }
        return !Objects.equals(result.target().getStatus(), BanStatus.NORMAL.getValue());
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    public ResultVO<UserVo> getUserVo(long userId) {
        GetSimpleUserRequest param = GetSimpleUserRequest.builder()
                .userId(userId)
                .build();
        Result<GetSimpleUserResponse> resp = userServiceRemote.getSimpleUser(param);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(UserMsgCodes.GET_USER_INFO_FAIL);
        }
        SimpleUser simpleUser = resp.target().getSimpleUser();
        UserVo userVo = new UserVo();
        userVo.setUserId(simpleUser.getUserId());
        userVo.setNickName(simpleUser.getNickName());
        userVo.setAvatar(simpleUser.getAvatar());
        userVo.setGender(simpleUser.getGender().getValue());
        userVo.setBand(simpleUser.getBand());
        return ResultVO.success(userVo);
    }

    /**
     * 批量获取用户信息
     *
     * @param userIds
     * @return
     */
    public ResultVO<List<UserVo>> batchGetUserVo(List<Long> userIds) {
        int limit = userProviderConfig.getBatchGetUserMaxSize();
        List<SimpleUser> simpleUserList = new ArrayList<>(userIds.size());
        if (userIds.size() <= limit) {
            Result<BatchGetSimpleUserResult> resp = userServiceRemote.batchGetSimpleUserByCache(BatchGetSimpleUserRequest.builder()
                    .userIdList(userIds)
                    .build());
            if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                return ResultVO.failure(UserMsgCodes.GET_USER_INFO_FAIL);
            }
            simpleUserList.addAll(resp.target().getSimpleUserList());
        } else {
            int numLists = (userIds.size() + limit - 1) / limit;
            for (int i = 0; i < numLists; i++) {
                int start = i * limit;
                int end = Math.min(start + limit, userIds.size());
                List<Long> subList = userIds.subList(start, end);
                Result<BatchGetSimpleUserResult> resp = userServiceRemote.batchGetSimpleUserByCache(BatchGetSimpleUserRequest.builder()
                        .userIdList(subList)
                        .build());
                if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                    return ResultVO.failure(UserMsgCodes.GET_USER_INFO_FAIL);
                }
                simpleUserList.addAll(resp.target().getSimpleUserList());
            }
        }

        //批量获取用户状态
        Result<List<GetUserOnlineStatusResult>> userOnlineRes = userOnlineStatusManager.batchGetUserOnlineStatus(userIds);
        if (userOnlineRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || userOnlineRes.target() == null) {
            log.info("batchGetUserVo.batchGetUserOnlineStatus fail, rCode={}, userId={}", userOnlineRes.rCode(), userIds);
            return ResultVO.failure(UserMsgCodes.GET_USER_STATUS_FAIL);
        }

        Map<Long, GetUserOnlineStatusResult> userOnlineMap = userOnlineRes.target().stream().collect(Collectors.toMap(GetUserOnlineStatusResult::getUserId, Function.identity(), (x, y) -> y));
        List<UserVo> res = new ArrayList<>(simpleUserList.size());
        for (SimpleUser simpleUser : simpleUserList) {
            GetUserOnlineStatusResult statusResult = userOnlineMap.get(simpleUser.getUserId());
            UserVo userVo = UserVo.builder()
                    .userId(simpleUser.getUserId())
                    .nickName(simpleUser.getNickName())
                    .gender(simpleUser.getGender() == null ? UserGenderEnum.UNKNOWN.getValue() : simpleUser.getGender().getValue())
                    .band(simpleUser.getBand())
                    .avatar(simpleUser.getAvatar())
                    .build();

            int onlineStatusCode = statusResult == null ? OnlineStatusEnum.OFFLINE.getValue() : statusResult.getOnlineStatusEnum().getValue();
            String onlineStatusDesc = statusResult == null ? OnlineStatusEnum.OFFLINE.getName() : statusResult.getOnlineStatusDesc();
            userVo.setOnlineStatusCode(onlineStatusCode);
            userVo.setOnlineStatusDesc(onlineStatusDesc);
            res.add(userVo);
        }
        return ResultVO.success(res);
    }

    /**
     * IM模块专用接口，获取当前用户与target用户的关联信息
     * <p>
     * 获取用户简单信息
     *
     * @return
     */
    public ResultVO<SimpleUserVO> getSimpleUserCache(Long userId, Long tgtUserId) {
        String key = Joiner.on("_").join(userId, tgtUserId);
        Optional<GetSimpleUserResponse> result = USER_CACHE.getUnchecked(key);
        if (!result.isPresent()) {
            log.info("getSimpleUserCache fail. userId={}`tgtUserId={}", userId, tgtUserId);
            return ResultVO.failure(UserMsgCodes.GET_USER_INFO_FAIL);
        }

        SimpleUserVO simpleUserVo = new SimpleUserVO();
        // 查询对方在线状态、是否在直播；是否关注对方，亲密值
        getTgtUserInfo(simpleUserVo, userId, tgtUserId);

        // 加载用户装扮  头像框 气泡 尾灯
        Long dressUserId = tgtUserId > 0 ? tgtUserId : userId; //兼容默认的tgtUserId为0的情况
        UserAvatarWidget avatarWidget = dressUpManager.getAvatarWidgetUsing(dressUserId);
        simpleUserVo.setAvatarWidget(avatarWidget);

        UserBubbleInfoVo userBubble = dressUpManager.getUserBubbleUsing(dressUserId);
        simpleUserVo.setUserBubble(userBubble);

        GetUserTailEffectResponse tailEffect = dressUpManager.getTailEffectUsing(dressUserId);
        simpleUserVo.setTailEffect(buildTailEffectVo(tailEffect));

        String exclusiveNickname = Optional.ofNullable(friendshipNotesService.getFriendshipNotesByExclusiveNicknameByCache(userId, tgtUserId))
                .map(Result::target).orElse(null);
        simpleUserVo.setExclusiveNickname(exclusiveNickname);

        SimpleUser simpleUser = result.get().getSimpleUser();
        simpleUserVo.setUserId(simpleUser.getUserId());
        simpleUserVo.setBand(simpleUser.getBand());
        simpleUserVo.setNickName(simpleUser.getNickName());
        simpleUserVo.setAvatar(simpleUser.getAvatar());
        simpleUserVo.setGender(simpleUser.getGender().getValue());
        simpleUserVo.setOfficial(simpleUser.isOfficial());
        simpleUserVo.setRoom(simpleUser.isRoom());
        simpleUserVo.setFamily(simpleUser.isFamily());

        return ResultVO.success(simpleUserVo);
    }

    private TailEffectVo buildTailEffectVo(GetUserTailEffectResponse tailEffect) {
        if (tailEffect == null) {
            return null;
        }
        return TailEffectVo.builder()
                .effectUrl(tailEffect.getEffectUrl())
                .id(tailEffect.getId())
                .build();
    }


    public Optional<GetSimpleUserResponse> getSimpleUser(SimpleUserParam param) {
        long userId = param.getUserId();
        long tgtUserId = param.getTgtUserId();
        Result<GetSimpleUserResponse> simpleUserResp;
        // tgtUserId有值，表示查对方的个人信息
        if (tgtUserId > 0) {
            simpleUserResp = userServiceRemote.getSimpleUser(GetSimpleUserRequest.builder()
                    .userId(tgtUserId).build());
        } else {
            // 查本人的信息
            simpleUserResp = userServiceRemote.getSimpleUser(GetSimpleUserRequest.builder()
                    .userId(userId).build());
        }

        if (simpleUserResp.rCode() != 0) {
            log.warn("getSimpleUser fail. rCode={}`userId={}", simpleUserResp.rCode(), userId);
            return Optional.empty();
        }

        GetSimpleUserResponse target = simpleUserResp.target();
        SimpleUser simpleUser = target.getSimpleUser();
        if (tgtUserId > 0 && simpleUser != null) {
            fillUserAuthentication(userId, tgtUserId, simpleUser);
        }
        return Optional.of(target);
    }

    /**
     * 查询用户认证信息
     *
     * @param simpleUser
     */
    private void fillUserAuthentication(long userId, long tgtUserId, SimpleUser simpleUser) {
        try {
            //认证信息不影响主流程
            GetUserSampleProcessor processor = factory.getProcessor(GetUserSampleProcessor.class);
            processor.fillOfficialInfo(simpleUser);
            if (simpleUser.isOfficial()) {
                return;
            }

            //查询tgtUserId用户的角色
            Result<UserFamilyInfo> tgFamilyInfo = userFamilyManager.getUserFamilyInfoByCache(tgtUserId);
            if (RpcResult.isFail(tgFamilyInfo)) {
                return;
            }

            //查询userId的角色
            Result<UserFamilyInfo> userFamilyInfo = userFamilyManager.getUserFamilyInfoByCache(userId);
            if (RpcResult.isFail(userFamilyInfo)) {
                return;
            }

            UserFamilyInfo tgFamily = tgFamilyInfo.target();
            UserFamilyInfo userFamily = userFamilyInfo.target();

            if (tgFamily.isFamily()) {
                //tgtUserId是家族长，判断是否是同一个家族
                simpleUser.setFamily(Objects.equals(tgFamily.getFamilyId(), userFamily.getFamilyId()));
            } else if (tgFamily.isRoom()) {
                //user为家族长
                if (userFamily.isFamily()) {
                    Result<List<Long>> familySignNjIds = userFamilyServiceRemote.getFamilySignNjIds(userId, 0L);
                    if (RpcResult.isSuccess(familySignNjIds) && !CollectionUtils.isEmpty(familySignNjIds.target())) {
                        simpleUser.setRoom(familySignNjIds.target().contains(tgtUserId));
                    }
                }

                //user为陪玩
                if (userFamily.isPlayer()) {
                    // 判断当前用户的厅主是否为目标用户
                    simpleUser.setRoom(Objects.equals(userFamily.getNjId(), tgtUserId));
                }
            }
        } catch (Exception e) {
            log.warn("fillUserAuthentication userId={} tgtUserId={} error:", userId, tgtUserId, e);
        }
    }

    private void getTgtUserInfo(SimpleUserVO vo, long userId, long tgtUserId) {
        if (tgtUserId <= 0) {
            return;
        }

        // 查询对方在线状态
        Result<GetUserOnlineStatusResult> userOnlineStatusResp = userOnlineStatusManager.getUserOnlineStatus(GetUserOnlineStatusParam.builder()
                .userId(tgtUserId).build());
        if (userOnlineStatusResp.rCode() != 0) {
            log.warn("getUserOnlineStatus fail. rCode={}`userId={}`tgtUserId={}", userOnlineStatusResp.rCode(), userId, tgtUserId);
            return;
        }

        GetUserOnlineStatusResult userOnlineStatus = userOnlineStatusResp.target();
        OnlineStatusEnum onlineStatusEnum = userOnlineStatus.getOnlineStatusEnum();

        // 查询对方是否在直播
        Result<GetUserBehaviorResult> userBehaviorResp = userServiceRemote.getUserBehaviors(GetUserBehaviorsParam.builder()
                .userId(tgtUserId).build());
        if (userBehaviorResp.rCode() != 0) {
            log.error("getUserBehaviors fail. rCode={}`userId={}`tgtUserId={}", userBehaviorResp.rCode(), userId, tgtUserId);
            return;
        }

        if (userBehaviorResp.target() != null && userOnlineStatus.getOnlineStatusEnum() == OnlineStatusEnum.ONLINE) {
            onlineStatusEnum = userBehaviorResp.target().getOnlineStatus()
                    .stream()
                    .filter(status -> status == OnlineStatusEnum.LIVING || status == OnlineStatusEnum.ON_MIC || status == OnlineStatusEnum.LISTEN)
                    .findFirst().orElse(onlineStatusEnum);
        }

        // 是否关注对方
        Result<GetFollowUserStatusResult> followStatusResp = userServiceRemote.getFollowUserStatus(GetFollowUserStatusParam.builder()
                .userId(userId)
                .tgtUserId(tgtUserId)
                .build());
        if (followStatusResp.rCode() != IUserServiceRemote.GET_FOLLOW_USER_STATUS_SUCCESS) {
            log.error("getFollowUserStatus fail. rCode={}`userId={}`tgtUserId={}", followStatusResp.rCode(), userId, tgtUserId);
            return;
        }

        GetFollowUserStatusResult followStatus = followStatusResp.target();

        //获取双方亲密值
        Result<Long> userIntimacyResp = userServiceRemote.getUserIntimacy(userId, tgtUserId);
        if (userIntimacyResp.rCode() != IUserServiceRemote.GET_USER_INTIMACY_SUCCESS) {
            log.error("get userIntimacy error rCode={}`userId={}`tgtUserId={}", userIntimacyResp.rCode(), userId, tgtUserId);
            return;
        }
        vo.setIntimacy(userIntimacyResp.target());
        vo.setOnlineStatusCode(onlineStatusEnum.getValue());
        vo.setOnlineStatusDesc(userOnlineStatus.getOnlineStatusDesc());
        vo.setFollowStatus(followStatus.getFollowUserStatusEnum().getRelation());
    }

    public ResultVO<Void> reportHeartbeat(boolean isFirst) {
        ServiceContext context = ContextUtils.getContext();
        Integer appId = context.getHeader().getAppId();
        ReportHeartBeatProcessor reportHeartBeatProcessor = processorFactory.getProcessor(appId, ReportHeartBeatProcessor.class);
        // 后置处理
        ReportHeartBeatPostBean reportHeartBeatPostBean = new ReportHeartBeatPostBean();
        reportHeartBeatPostBean.setFirst(isFirst);
        reportHeartBeatProcessor.postprocessor(reportHeartBeatPostBean);
        return ResultVO.success();
    }

    public ResultVO<Void> attentionUser(AttentionUserParam param) {
        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        Long tgtUserId = param.getTgtUserId();
        Integer operation = param.getOperation();

        AttentionUserRemoteRequest request = AttentionUserRemoteRequest.builder()
                .userId(userId)
                .tgtUserId(tgtUserId)
                .operation(operation)
                .build();

        AttentionUserProcessor processor = factory.getProcessor(AttentionUserProcessor.class);
        ResultVO<Void> checkAttentionStatusResult = processor.checkAttentionStatus(request);
        if (!checkAttentionStatusResult.isOK()) {
            return checkAttentionStatusResult;
        }
        Result<AttentionUserResult> attentionUserResp = userServiceRemote.attentionUser(request);

        if (attentionUserResp.rCode() != IUserServiceRemote.ATTENTION_USER_SUCCESS) {
            log.error("attentionUser fail. rCode={}`userId={}`tgtUserId={}`operation={}",
                    attentionUserResp.rCode(), userId, tgtUserId, operation);
            return ResultVO.failure(UserMsgCodes.OP_ATTENTION_USER_FAIL);
        }

        return ResultVO.success();
    }

    /**
     * 检查用户是否实名制
     *
     * @param userId 用户id
     * @param appId  应用id
     * @return true:实名制 false:非实名制
     */
    public boolean checkUserRealNameAuthStatus(long userId, int appId) {
        GetUserVerifyDataRequest param = GetUserVerifyDataRequest.builder()
                .userId(userId)
                .appId(appId)
                .build();
        //检查是否实名制
        Result<GetUserVerifyDataResult> result = userServiceRemote.getUserVerifyDataResult(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getUserRealNameAuthStatus, userId={}", userId);
            return false;
        }

        GetUserVerifyDataResult dataResult = result.target();
        return dataResult.getVerifyStatus() == VerifyStatusConstant.VERIFY_PASS;
    }

    /**
     * 获取用户详细信息
     *
     * @param param 参数
     * @return 用户信息
     */
    public UserDetailVO getUserDetail(UserDetailParam param) {
        // 基本信息
        UserDetailVO userDetail = getUserInfo(param);
        if (userDetail == null) {
            return null;
        }

        //差异化处理器
        GetUserDetailProcessor processor = factory.getProcessor(GetUserDetailProcessor.class);

        //隐藏用户注册天数
        if (processor.isHideRegisterDays(param.getTargetUserId())) {
            userDetail.setRegDays(0);
        }

        userDetail.setShowImRead(processor.isHideImRead(param.getUserId()) ? GetUserDetailProcessor.showImRead : GetUserDetailProcessor.hideImRead);
        //补充用户在线状态信息
        fillUserOnlineStatus(userDetail);
        //补充用户粉丝信息
        fillUserFansInfo(userDetail);
        //补充用户家族信息
        fillUserFamilyInfo(userDetail);
        //补充用户各种等级，财富等级、主播等级、用户等级
        fillUserLevelInfo(userDetail, param.getUserId());
        //补充用户勋章列表
        fillUserMedal(userDetail);
        //补充用户在房信息
        fillUserInRoomStatus(userDetail, processor);
        //补充用户在房角色信息
        fillUserRoomRoleInfo(userDetail, param.getLiveRoomId());
        //补充用户音色信息
        fillUserVoiceLineInfo(userDetail);
        //设置用户VIP信息
        userDetail.setUserVipInfo(processor.getUserVipInfo(param));
        // 头像框
        UserAvatarWidget avatarWidget = dressUpManager.getAvatarWidgetUsing(param.getTargetUserId());
        // 专属昵称
        String exclusiveNickname = Optional.ofNullable(friendshipNotesService.getFriendshipNotesByExclusiveNicknameByCache(param.getUserId(), param.getTargetUserId()))
                .map(Result::target).orElse(null);
        userDetail.setExclusiveNickname(exclusiveNickname);

        userDetail.setAvatarWidget(avatarWidget);
        //设置官方认证信息
        userDetail.setOfficialCertifiedVOList(processor.getOfficialCertifiedVO(param));
        // 设置用户个人页背景
        userDetail.setUserPageBackgroundUrl(processor.getUserPageBackground(param));
        return userDetail;
    }

    /**
     * 获取业务用户token
     *
     * @param userId 用户ID
     * @return 结果
     */
    public String getBusinessUserTokenByUserId(long userId) {
        String token = null;
        try {
            Result<String> result = userServiceRemote.getUserToken(userId);
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                token = result.target();
            }
        } catch (Exception e) {
            log.error("获取用户token异常, userId:{}", userId, e);
        }
        return token;
    }

    /**
     * 搜索用户信息
     *
     * @param param 搜索用户内容
     * @return 用户信息
     */

    public ResultVO<SearchUserResultVO> searchUserInfo(UserInfoParam param) {
        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        Header header = context.getHeader();
        Integer appId = header.getAppId();
        String deviceId = header.getDeviceId();
        String ip = context.getHeader().getIp();
        ISearchUserProcessor processor = factory.getProcessor(ISearchUserProcessor.class);
        CommonUserConfig commonUserConfig = processor.getCommonUserConfig();
        if (!isInWhitelist(userId) && commonUserConfig.isDisableSearch()) {
            log.info("用户不再白名单内并且已经禁用搜索,userId:{}", userId);
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_FAIL);
        }
        //是否抽取搜索内容的数字

        if (commonUserConfig.isSearchExtractDigit()) {
            param.setSearchUserContent(CharMatcher.DIGIT.retainFrom(param.getSearchUserContent()));
            log.info("SearchUser after extract digit ,content={}", context);
        }
        String content = param.getSearchUserContent();
        content = content == null ? content : content.trim();
        if (Strings.isNullOrEmpty(content)) {
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_CONTENT_BLANK.getCode(), UserMsgCodes.SEARCH_USERINFO_CONTENT_BLANK.getMsg());
        }
        if (content.length() > commonUserConfig.getSearchMaxLen()) {
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_CONTENT_TOO_LONG.getCode(), commonUserConfig.getExceedMaxLenMsg());
        }
        if (content.length() < commonUserConfig.getSearchMinLen()) {
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_CONTENT_TOO_SHORT.getCode(), commonUserConfig.getExceedMinLenMsg());
        }
        //风控
        if (!antiCheatServiceRemote.userSearchAntiCheat(content, ip, deviceId, appId)) {
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_ANTI_CHEAT_FAIL.getCode(), UserMsgCodes.SEARCH_USERINFO_ANTI_CHEAT_FAIL.getMsg());
        }

        //更新热词
        hotWordsServiceRemote.updateHotWordsSearchCount(content);

        String performanceId = Base64Utils.decoder(param.getPerformanceId());
        Result<SearchUserInfoResponse> searchUserInfoResultResult = userServiceRemote.searchUserInfo(content, performanceId);
        if (searchUserInfoResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("rCode={},content={}", searchUserInfoResultResult.rCode(), content);
            return ResultVO.failure(UserMsgCodes.SEARCH_USERINFO_FAIL.getCode(), UserMsgCodes.SEARCH_USERINFO_FAIL.getMsg());
        }
        if (searchUserInfoResultResult.target().getUserInfoVOList().size() == 0) {
            return ResultVO.success(new SearchUserResultVO().setUserList(Collections.emptyList()));
        }
        //获取批量用户在线状态
        List<SearchUserInfoVO> userInfoVOList = searchUserInfoResultResult.target().getUserInfoVOList();
        List<Long> userIds = userInfoVOList.stream().map(SearchUserInfoVO::getUserId).collect(Collectors.toList());
        Result<List<GetUserOnlineStatusResult>> userOnlineStatusResults = userOnlineStatusManager.batchGetUserOnlineStatus(userIds);
        if (userOnlineStatusResults.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //填充用户在线状态
            List<GetUserOnlineStatusResult> onlineStatus = userOnlineStatusResults.target();
            userInfoVOList.forEach(userInfoVO -> {
                Long userId1 = userInfoVO.getUserId();
                Boolean online = onlineStatus.stream().filter(result -> result.getUserId() == userId1)
                        .map(GetUserOnlineStatusResult::isOnlineStatus)
                        .findFirst()
                        .orElse(false);
                userInfoVO.setOnline(online);
            });
        } else {
            log.error("batchGetUserOnlineStatus error ,userIds={}", userIds);
        }
        //处理头像url,
        for (SearchUserInfoVO searchUserInfoVO : userInfoVOList) {
            String avatarUrl = UrlUtils.getAvatarUrl(commonProviderConfig.getBusinessConfig(appId).getCdnHost(), searchUserInfoVO.getAvatar());
            searchUserInfoVO.setAvatar(avatarUrl);
        }

        return ResultVO.success(new SearchUserResultVO()
                .setPerformanceId(Base64Utils.encoder(searchUserInfoResultResult.target().getPerformanceId()))
                .setLastPage(searchUserInfoResultResult.target().isLastPage())
                .setUserList(userInfoVOList));
    }

    /**
     * @param userId 用户id
     * @return 是否在配置白名单
     */
    private boolean isInWhitelist(long userId) {
        if (userId <= 0) {
            return false;
        }
        ISearchUserProcessor processor = factory.getProcessor(ISearchUserProcessor.class);
        CommonUserConfig commonUserConfig = processor.getCommonUserConfig();
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(commonUserConfig.getSearchWhitelist());
        return list.contains(String.valueOf(userId));
    }

    /**
     * 获取用户邀请进房列表
     *
     * @param pageSize      每页显示参数
     * @param performanceId 刷新分页关键字，缺省为空，服务端下发，客户端回传
     * @return 用户邀请进房列表
     */

    public ResultVO<InviteUserInfoVO> getUserInviteList(Integer pageSize, Long performanceId) {
        long userId = ContextUtils.getContext().getUserId();
        List<InviteUserInfo> roomOwners = new ArrayList<>();
        //根据当前用户id，查询所在房间房主信息。
        InviteUserInfo roomOwnersByUserId = getRoomOwnersByUserId(userId);
        if (roomOwnersByUserId != null) {
            roomOwners.add(roomOwnersByUserId);
        }

        //查询关注且开播的用户信息
        ResultVO<FollowUserLiveInfoVO> userFollowLiveInfo = getUserFollowLiveInfo(userId, pageSize, performanceId);
        if (!userFollowLiveInfo.isOK()) {
            //失败了，直接返回
            return ResultVO.failure(userFollowLiveInfo.getRCode(), userFollowLiveInfo.getPrompt().getMsg());
        }

        FollowUserLiveInfoVO followUserLiveInfoVO = userFollowLiveInfo.getData();
        List<FollowUserInfo> followersList = followUserLiveInfoVO.getFollowers();

        //批量获取用户信息
        List<Long> userIds = followersList.stream().map(FollowUserInfo::getUserId).collect(Collectors.toList());
        BatchGetSimpleUserRequest request = BatchGetSimpleUserRequest.builder().userIdList(userIds).build();
        Result<BatchGetSimpleUserResult> batchGetSimpleUserResultResult = userServiceRemote.batchGetSimpleUserByCache(request);
        if (batchGetSimpleUserResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getOnlineFollowerRoomByUserId batchGetSimpleUser error,rCode={},ids={}", batchGetSimpleUserResultResult.rCode(), userIds);
            throw new RuntimeException("获取用户关注用户状态列表失败");
        }

        //拼接用户信息
        List<SimpleUser> simpleUserList = batchGetSimpleUserResultResult.target().getSimpleUserList();
        Map<Long, SimpleUser> userMap = simpleUserList.stream().collect(Collectors.toMap(SimpleUser::getUserId, Function.identity(), (x, y) -> y));
        List<InviteUserInfo> followers = new ArrayList<>(followersList.size());
        for (FollowUserInfo followUserInfo : followersList) {
            SimpleUser simpleUser = userMap.get(followUserInfo.getUserId());
            if (simpleUser == null) {
                log.warn("getOnlineFollowerRoomByUserId getSimpleUser error,userId={}", followUserInfo.getUserId());
                continue;
            }
            InviteUserInfo info = InviteUserInfo.builder()
                    .userId(simpleUser.getUserId())
                    .avatar(simpleUser.getAvatar())
                    .band(simpleUser.getBand())
                    .isInviter(followUserInfo.getUserId() == userId)
                    .nickName(followUserInfo.getNickName())
                    .liveId(followUserInfo.getLiveId())
                    .liveName(followUserInfo.getLiveName()).build();
            followers.add(info);
        }

        InviteUserInfoVO inviteUserInfoVO = new InviteUserInfoVO();
        inviteUserInfoVO.setFollowers(followers);
        inviteUserInfoVO.setRoomOwners(roomOwners);
        inviteUserInfoVO.setPerformanceId(followUserLiveInfoVO.getPerformanceId());
        inviteUserInfoVO.setLastPage(followUserLiveInfoVO.isLastPage());
        return ResultVO.success(inviteUserInfoVO);
    }

    /**
     * @param userId        用户id
     * @param pageSize      每页请求数
     * @param performanceId 刷新分页关键字，缺省为空，服务端下发，客户端回传
     * @return
     * @throws RuntimeException
     */
    private FollowUserLiveInfoVO getOnlineFollowerRoomByUserId(long userId, Integer pageSize, Long performanceId) throws RuntimeException {
        FollowUserLiveInfoVO followUserLiveInfoVO = new FollowUserLiveInfoVO();
        List<FollowUserInfo> followers = new ArrayList<>();
        //查找当前用户所关注的用户的id集合
        GetFollowUserListParam followUserListParam = GetFollowUserListParam.builder().userId(userId)
                .performanceId(performanceId)
                .pageSize(pageSize).build();
        Result<GetFollowUserListResult> followUserList = userServiceRemote.getFollowUserList(followUserListParam);
        if (followUserList.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getOnlineFollowerRoomByUserId getFollowUserList error,rCode={}", followUserList.rCode());
            throw new RuntimeException("获取用户关注用户状态列表失败");
        }
        List<Long> ids = followUserList.target().getResultList();

        Result<GetUsersLatestLiveResult> livesResult = liveService.getUsersLatestLive(ids);
        if (livesResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getOnlineFollowerRoomByUserId getUsersLatestLive error,rCode={}", livesResult.rCode());
            throw new RuntimeException("查询用户直播信息失败");
        }

        List<Live> lives = livesResult.target().getLives();
        for (Live live : lives) {
            //判断该live是否正在直播
            if (live.getStatus() != LiveStatusEnum.ON_AIR.getValue()) {
                continue;
            }
            FollowUserInfo inviteUserInfo = FollowUserInfo.builder()
                    .userId(live.getUserId())
                    .liveId(live.getId())
                    .liveName(live.getName()).build();
            followers.add(inviteUserInfo);
        }
        followUserLiveInfoVO.setFollowers(followers);
        followUserLiveInfoVO.setPerformanceId(followUserList.target().getPerformanceId());
        followUserLiveInfoVO.setLastPage(ids.size() < pageSize);
        return followUserLiveInfoVO;
    }

    /**
     * 根据当前用户id，返回当前用户所在房间的房主信息
     *
     * @param userId 当前用户id
     * @return 如果查询成功，返回房主信息，查询失败返回null
     */
    private InviteUserInfo getRoomOwnersByUserId(long userId) {
        //查看当前用户是否在某个房间

        Result<UserLiveRoomStatus> userLiveRoomStatus = liveService.getUserLiveRoomStatus(userId, true);
        if (userLiveRoomStatus.rCode() == LiveService.GET_USER_LIVE_ROOM_STATUS_ERROR || userLiveRoomStatus.target() == null) {
            //查询失败或者当前用户没有所在房间
            return null;
        }

        long liveId = userLiveRoomStatus.target().getLiveId();
        //根据liveId查询最近一场直播，使用本地缓存，live每小时改动频率低于10，缓存一段时间问题不大
        Result<GetLiveResult> liveResult = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("Get live fail,liveId={}", liveId);
            return null;
        }
        Live live = liveResult.target().getLive();
        Long userIdByLive = live.getUserId();
        Result<GetSimpleUserResponse> simpleUserResult = userServiceRemote.getSimpleUserByCache(userIdByLive);
        if (simpleUserResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("Get simpleUser fail,userId={}", userIdByLive);
            return null;
        }
        SimpleUser simpleUser = simpleUserResult.target().getSimpleUser();
        return InviteUserInfo.builder().userId(simpleUser.getUserId())
                .avatar(simpleUser.getAvatar())
                .band(simpleUser.getBand())
                .nickName(simpleUser.getNickName())
                .isInviter(userIdByLive.equals(userId))
                .liveId(liveId)
                .liveName(live.getName()).build();
    }

    /**
     * 查询厅主和用户关系
     *
     * @param userId 用户ID
     * @param njId   厅主ID
     * @return 结果
     */
    public ResultVO<UserSignRelationVO> getUserSignRelation(long userId, long njId) {
        // 是否为厅主
        if (userId == njId) {
            return ResultVO.success(UserSignRelationVO.builder().status(NjAndUserRelationEnum.SELF.getStatus()).build());
        }
        // 是否为该厅的家族长
        Result<List<Long>> familySignNjIdsRes = familyService.getFamilySignNjIds(userId, 0L);
        if (familySignNjIdsRes.rCode() != 0) {
            return ResultVO.failure(UserMsgCodes.USER_SIGN_RELATION_FAIL.getCode(), UserMsgCodes.USER_SIGN_RELATION_FAIL.getMsg());
        }
        List<Long> signNjIds = familySignNjIdsRes.target();
        if (!CollectionUtils.isEmpty(signNjIds) && signNjIds.contains(njId)) {
            return ResultVO.success(UserSignRelationVO.builder().status(NjAndUserRelationEnum.FAMILY_RELATED.getStatus()).build());
        }

        // 是否为该厅陪玩
        Result<Long> curSignNjIdRes = familyService.playerCurSignNj(userId);
        if (curSignNjIdRes.rCode() != 0) {
            return ResultVO.failure(UserMsgCodes.USER_SIGN_RELATION_FAIL.getCode(), UserMsgCodes.USER_SIGN_RELATION_FAIL.getMsg());
        }
        if (curSignNjIdRes.target() == njId) {
            return ResultVO.success(UserSignRelationVO.builder().status(NjAndUserRelationEnum.PLAYER_RELATED.getStatus()).build());
        }

        return ResultVO.success(UserSignRelationVO.builder().status(NjAndUserRelationEnum.UN_RELATED.getStatus()).build());
    }

    /**
     * 查询关注的且开播的用户列表
     *
     * @param userId        用户ID
     * @param pageSize      每页条数
     * @param performanceId 分页参数
     * @return 结果
     */
    public ResultVO<FollowUserLiveInfoVO> getUserFollowLiveInfo(long userId, Integer pageSize, Long performanceId) {
        List<FollowUserInfo> followers = new ArrayList<>();
        boolean continueQuery = true;
        FollowUserLiveInfoVO onlineFollowerRoomByUserId = null;
        int multiple = userProviderConfig.getInviteListMultiple();
        while (continueQuery) {
            //查询当前用户所关注的正在直播的用户信息
            onlineFollowerRoomByUserId = getOnlineFollowerRoomByUserId(userId, pageSize * multiple, performanceId);
            followers.addAll(onlineFollowerRoomByUserId.getFollowers());
            performanceId = onlineFollowerRoomByUserId.getPerformanceId();
            //若正在直播的用户小于pageSize并且没有查到最后一页，则继续查，直到正在直播的用户大于pageSize或者查到了最后一页
            continueQuery = followers.size() < pageSize && !onlineFollowerRoomByUserId.isLastPage();
            log.info("getOnlineFollowerRoomByUserId userId={},pageSize={},performanceId={}", userId, pageSize, performanceId);

        }
        FollowUserLiveInfoVO followUserLiveInfoVO = new FollowUserLiveInfoVO();
        followUserLiveInfoVO.setFollowers(followers);
        followUserLiveInfoVO.setPerformanceId(performanceId);
        followUserLiveInfoVO.setLastPage(onlineFollowerRoomByUserId.isLastPage());
        return ResultVO.success(followUserLiveInfoVO);
    }

    /**
     * 根据波段号查询用户简单信息
     *
     * @param band 波段号
     * @return 结果
     */
    public ResultVO<BaseUserInfoVO> getUserBaseInfoByBand(String band) {
        if (StringUtils.isEmpty(band)) {
            return ResultVO.failure(UserMsgCodes.GET_USER_BASE_INFO_PARAM_ERROR);
        }
        Result<UserBaseInfoResponse> result = userServiceRemote.getUserBaseInfoByBand(band);
        if (RpcResult.isFail(result)) {
            if (result.rCode() == IUserServiceRemote.GET_USER_BASE_INFO_USER_NO_EXIST) {
                return ResultVO.failure(UserMsgCodes.GET_USER_BASE_INFO_USER_NO_EXIST);
            }
            return ResultVO.failure(UserMsgCodes.GET_USER_BASE_INFO_FAIL);
        }

        String cdnHost = commonProviderConfig.getBusinessConfig(ContextUtils.getBusinessEvnEnum().appId()).getCdnHost();
        UserBaseInfoResponse response = result.target();
        BaseUserInfoVO baseUserInfoVO = new BaseUserInfoVO().setBand(band).setUserId(response.getUserId())
                .setNickName(response.getName())
                .setAvatar(UrlUtils.getAvatarUrl(cdnHost, response.getAvatar()));
        return ResultVO.success(baseUserInfoVO);
    }

    /**
     * 获取用户基本信息
     *
     * @param param 参数
     * @return 结果
     */
    private UserDetailVO getUserInfo(UserDetailParam param) {
        Result<GetSimpleUserResponse> simpleUserResp = userServiceRemote.getSimpleUserByCache(param.getTargetUserId());

        if (simpleUserResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS
                || Objects.isNull(simpleUserResp.target())
                || Objects.isNull(simpleUserResp.target().getSimpleUser())) {
            log.error("getUserDetail fail|获取用户信息失败. rCode={}`userId={}", simpleUserResp.rCode(), param.getTargetUserId());
            return null;
        }
        // 基本信息
        return UserConverterUtils.coverterSimpleUserResultToUserDetailVO(simpleUserResp.target());
    }

    /**
     * 补充用户在线状态
     *
     * @param userDetail 用户详情
     */
    private void fillUserOnlineStatus(UserDetailVO userDetail) {
        try {
            // 查询对方在线状态
            Result<GetUserOnlineStatusResult> userOnlineStatusResp = userOnlineStatusManager.getUserOnlineStatus(GetUserOnlineStatusParam.builder()
                    .userId(userDetail.getUserId()).build());
            if (userOnlineStatusResp.rCode() == 0) {
                //校验是否需要隐藏在线状态，是的话，就直接返回空隐藏后的状态
                GetUserOnlineStatusResult userOnlineStatus = userOnlineStatusResp.target();
                userDetail.setOnlineStatusCode(userOnlineStatus.getOnlineStatusEnum().getValue());
                userDetail.setOnlineStatusDesc(userOnlineStatus.getOnlineStatusDesc());
            }
        } catch (Exception e) {
            log.error("获取用户在线状态异常, targetUid:{}", userDetail.getUserId(), e);
        }
    }

    /**
     * 补充用户粉丝信息
     *
     * @param userDetail 用户详细信息
     */
    private void fillUserFansInfo(UserDetailVO userDetail) {
        try {
            // 粉丝数
            Result<Integer> result = userServiceRemote.getUserFansCount(userDetail.getUserId());
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                userDetail.setFansCount(result.target());
            } else {
                userDetail.setFansCount(0);
            }
        } catch (Exception e) {
            log.error("获取粉丝数异常, targetUid:{}", userDetail.getUserId(), e);
            userDetail.setFansCount(0);
        }
    }

    /**
     * 补偿用户家族信息
     *
     * @param userDetail 用户详情
     */
    private void fillUserFamilyInfo(UserDetailVO userDetail) {
        try {
            //TODO 临时屏蔽PP家族信息
            if (ContextUtils.getContext().getBusinessEvnEnum() == BusinessEvnEnum.PP && !userProviderConfig.getPp().isUserDetailShowFamily()) {
                //如果业务是PP业务,且有开关,不开启查询家族信息,就跳过
                return;
            }

            // 家族(公会)信息
            Result<UserFamilyInfo> userFamilyInfo = userFamilyManager.getUserFamilyInfoByCache(userDetail.getUserId());
            if (userFamilyInfo.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(userFamilyInfo.target())) {
                UserFamilyInfoVO userFamilyInfoVO = new UserFamilyInfoVO();
                userFamilyInfoVO.setFamilyName(userFamilyInfo.target().getFamilyName());
                userFamilyInfoVO.setRoleDesc(userFamilyInfo.target().getRoleDesc());
                userFamilyInfoVO.setIconUrl(userFamilyInfo.target().getIconUrl());
                userFamilyInfoVO.setCardFamilyName(String.format("%s • %s", userFamilyInfo.target().getFamilyName(), userFamilyInfo.target().getRoleDesc()));
                userDetail.setFamilyInfo(userFamilyInfoVO);
            }
        } catch (Exception e) {
            log.error("获取家族信息异常, targetUid:{}", userDetail.getUserId(), e);
        }
    }

    /**
     * 补充用户各种等级信息
     *
     * @param userDetail 用户详情
     * @param userId     用户ID
     */
    private void fillUserLevelInfo(UserDetailVO userDetail, long userId) {
        Long targetUserId = userDetail.getUserId();
        try {
            // 财富等级
            Result<UserLevelVO> wealthResult = userAssetsServiceRemote.getWealth(targetUserId);
            UserLevelVO wealth = wealthResult.target();
            if (wealthResult.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(wealth)) {
                userDetail.setWealthLevel(wealth);
            }
        } catch (Exception e) {
            log.error("获取财富等级异常, targetUid:{}", targetUserId, e);
        }

        // 用户等级
        try {
            Result<UserLevelVO> userLevelResult = userAssetsServiceRemote.getUserLevel(targetUserId);
            if (userLevelResult.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                userDetail.setUserLevel(userLevelResult.target());
            }
        } catch (Exception e) {
            log.error("获取用户等级异常, targetUid:{}", targetUserId, e);
        }

        // 主播等级
        try {
            // 1点单技能 2娱乐厅陪玩 3娱乐厅主持
            Result<UserAnchorLevelVO> result = userAssetsServiceRemote.getUserAnchorLevel(userId, targetUserId);
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                userDetail.setAnchorLevel(result.target());
            }
        } catch (Exception e) {
            log.error("获取主播等级异常, targetUid:{}", targetUserId, e);
        }
    }

    /**
     * 补充用户勋章
     *
     * @param userDetail 用户详细信息
     */
    private void fillUserMedal(UserDetailVO userDetail) {
        // 用户勋章
        try {
            List<UserMedalVO> userMedalVOS = new ArrayList<>();
            Result<List<UserMedalVO>> result = medalServiceRemote.medalUserUseList(userDetail.getUserId());
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && !CollectionUtils.isEmpty(result.target())) {
                userMedalVOS.addAll(result.target());
            }
            //补充新人信息
            Result<FreshUserResult> freshUser = waveUserService.isFreshUserByCache(userDetail.getUserId());
            if (freshUser.rCode() == 0 && freshUser.target() != null && freshUser.target().isIs()) {
                FreshUserResult userResult = freshUser.target();
                UserMedalVO FreshUserMedal = UserMedalVO.builder().medalInfoId(-1L).medalGroupId(-1L).medalName("新人").medalDesc("新人").medalImageUrl(userResult.getUrl()).aspect((double) userResult.getAspect()).build();
                userMedalVOS.add(FreshUserMedal);
            }
            userDetail.setMedalList(userMedalVOS);
        } catch (Exception e) {
            log.error("获取用户勋章异常, targetUid:{}", userDetail.getUserId(), e);
        }
    }

    /**
     * 补充用户在房状态信息
     *
     * @param userDetail 用户详情
     */
    private void fillUserInRoomStatus(UserDetailVO userDetail, GetUserDetailProcessor processor) {
        Long userId = userDetail.getUserId();
        try {
            // 在房状态
            if (!userDetail.getOnlineStatusCode().equals(OnlineStatusEnum.OFFLINE.getValue())) {
                //判断用户是否开启了在玩状态隐身
                boolean hideUserPrivilege = processor.isHideUserBehavior(userId);
                if (hideUserPrivilege) {
                    //如果用户开启了隐身且在别人的直播间玩刷，就隐藏在玩状态
                    return;
                }
                Result<UserLiveRoomStatus> result = liveService.getUserLiveRoomStatus(userId, true);
                if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(result.target())) {
                    UserLiveRoomStatus target = result.target();
                    userDetail.setLiveRoomStatus(UserLiveRoomStatusVO.builder()
                            .liveId(target.getLiveId())
                            .roomName(target.getRoomName())
                            .roomUserId(target.getRoomUserId())
                            .build());

                } else {
                    log.info("获取用户在房状态异常或者不在房间内, targetUid:{}, rCode:{}", userId, result.rCode());
                }
            }
        } catch (Exception e) {
            log.error("获取用户在房状态异常, targetUid:{}", userId, e);
        }
    }

    /**
     * 补充用户房间角色
     *
     * @param userDetail 用户详细信息
     * @param liveRoomId 直播房间ID
     */
    private void fillUserRoomRoleInfo(UserDetailVO userDetail, Long liveRoomId) {
        // 房间角色
        try {
            if (Objects.nonNull(liveRoomId)) {
                Result<LiveRoomRoleIcon> result = liveRoomRoleService.getLiveRoomRoleIcon(liveRoomId, userDetail.getUserId());
                if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(result.target())) {
                    UserDetailVO.LiveRoomRoleIconVO iconVO = new UserDetailVO.LiveRoomRoleIconVO();
                    iconVO.setRole(result.target().getRole());
                    iconVO.setAspect(result.target().getAspect());
                    iconVO.setIconUrl(result.target().getIconUrl());
                    userDetail.setLiveRoomRoleIcon(iconVO);

                }
            }
        } catch (Exception e) {
            log.error("获取用户在房角色异常, targetUid:{}", userDetail.getUserId(), e);
        }
    }

    /**
     * 补充用户音色信息到用户详情中
     *
     * @param userDetail 用户详细信息
     */
    private void fillUserVoiceLineInfo(UserDetailVO userDetail) {
        Long userId = userDetail.getUserId();
        try {
            Result<GetUserVoiceLineResponse> result = userServiceRemote.getUserVoiceLineByCache(userId);
            if (result.rCode() == IUserServiceRemote.GET_USER_VOICE_LINE_BY_CACHE_NOT_EXIST) {
                log.debug("用户音色不存在, userId:{}", userId);
                return;
            }
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.debug("获取用户音色失败, userId:{}", userId);
                return;
            }
            GetUserVoiceLineResponse response = result.target();
            VoiceLineVO voiceLine = VoiceLineVO.builder()
                    .name(response.getName())
                    .color(response.getColor())
                    .build();
            userDetail.setVoiceLine(voiceLine);
        } catch (RuntimeException e) {
            log.error("获取用户音色异常, userId:{}", userId, e);
        }
    }

    /**
     * 是否在其他主播直播间收听直播
     *
     * @param userId 用户ID
     * @return true: 是，false: 否
     */
    private boolean isListeningLive(long userId) {
        Result<GetUserBehaviorResult> userBehaviors = userServiceRemote.getUserBehaviors(GetUserBehaviorsParam.builder().userId(userId).build());
        if (userBehaviors.rCode() != IUserServiceRemote.GET_USER_BEHAVIORS_SUCCESS) {
            log.warn("getUserBehaviors fail. rCode={}`userId={}", userBehaviors.rCode(), userId);
            return false;
        }
        GetUserBehaviorResult userBehaviorResult = userBehaviors.target();
        List<OnlineStatusEnum> onlineStatus = userBehaviorResult.getOnlineStatus();
        for (OnlineStatusEnum status : onlineStatus) {
            //在麦上和在收听，都隐藏
            if (status == OnlineStatusEnum.LISTEN || status == OnlineStatusEnum.ON_MIC) {
                return true;
            }
        }
        return false;
    }


    /**
     * 用户举报
     *
     * @param userId
     * @param param
     * @return
     */
    public ResultVO<Void> userReport(Long userId, UserReportParam param) {

        UserReportRequest reportRequest = UserReportRequest.builder()
                .details(param.getDetails())
                .reason(param.getReason())
                .source(1)
                .recJobHoppingLiveBand(param.getRecJobHoppingLiveBand())
                .screenshots(param.getScreenshots())
                .targetUserId(param.getTargetUserId())
                .userId(userId)
                .build();

        IUserReportProccess processor = factory.getProcessor(IUserReportProccess.class);

        ResultVO<Void> checkParamResult = processor.checkParam(reportRequest);
        if (!checkParamResult.isOK()) {
            return checkParamResult;
        }

        Result<GetSimpleUserResponse> simpleUserResp = userServiceRemote.getSimpleUser(GetSimpleUserRequest.builder()
                .userId(param.getTargetUserId()).build());

        if (simpleUserResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS
                || Objects.isNull(simpleUserResp.target())
                || Objects.isNull(simpleUserResp.target().getSimpleUser())) {
            log.error("userReport fail 获取用户信息失败. rCode={}`userId={}", simpleUserResp.rCode(), param.getTargetUserId());
            return ResultVO.failure("没有该被举报人的数据");
        }

        ReportRecordVo reportRecordVo = userReportRemote.getReportRecordVo(GetReportRecordRequest
                .builder()
                .simpleUser(simpleUserResp.target().getSimpleUser())
                .userId(userId)
                .build()
        );

        if (reportRecordVo == null) {
            return ResultVO.failure("获取举报相关信息错误！");
        }

        Result<String> userReportResult = userReportRemote.userReport(reportRequest, reportRecordVo);
        if (userReportResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(userReportResult.rCode(), userReportResult.target());
        }
        return ResultVO.success();
    }


}
