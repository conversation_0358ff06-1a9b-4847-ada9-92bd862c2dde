package fm.lizhi.ocean.wave.user.core.facade;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.lizhi.commons.service.client.pojo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.trade.api.UserAvatarService;
import xm.fm.lizhi.live.trade.constant.AvatarReturnEnum;
import xm.fm.lizhi.live.trade.dto.avatar.req.GetAvatarReqDto;
import xm.fm.lizhi.live.trade.dto.avatar.resp.GetAvatarRespDto;
import xm.fm.lizhi.live.trade.protocol.UserAvatarProto;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用户虚拟形象
 */
@Slf4j
@Component
public class UserAvatarServiceFacade {

    @Autowired
    private UserAvatarService userAvatarService;

    /**
     * 用户虚拟形象缓存
     */
    private final LoadingCache<String, Optional<GetAvatarRespDto>> USER_AVATAR_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<GetAvatarRespDto>>() {
                @Override
                public Optional<GetAvatarRespDto> load(String key) {
                    String[] split = key.split(",");
                    if (split.length < 3) {
                        return Optional.empty();
                    }
                    int scene = Integer.parseInt(split[0]);
                    long userId = Long.parseLong(split[1]);
                    Integer returnCode = Integer.parseInt(split[2]);
                    GetAvatarRespDto userAvatar = getUserAvatar(scene, userId, returnCode);
                    if (userAvatar == null) {
                        return Optional.empty();
                    }
                    return Optional.of(userAvatar);
                }
            });

    /**
     * 获取用户虚拟形象
     *
     * @param userId 用户ID
     * @return 结果
     */
    public GetAvatarRespDto getUserAvatar(int scene, long userId, Integer returnCode) {
        GetAvatarReqDto reqDto = new GetAvatarReqDto()
                .setUserId(-1L)
                .setTargetUserId(userId)
                .setScene(scene)
                .setReturnEnum(AvatarReturnEnum.getEnum(ObjectUtils.defaultIfNull(returnCode, 0)));
        Result<UserAvatarProto.ResponseGetAvatar> result = userAvatarService.getAvatar(JSON.toJSONString(reqDto));
        if (result.rCode() != 0) {
            log.info("getUserAvatar fail. userId={},rCode={}", userId, result.rCode());
            return null;
        }
        return JSON.parseObject(result.target().getGetAvatarRespDto(), GetAvatarRespDto.class);
    }

    /**
     * 从缓存中获取用户虚拟形象
     *
     * @param scene      场景
     * @param userId     用户ID
     * @param returnCode 返回码
     * @return 结果
     */
    public GetAvatarRespDto getUserAvatarByCache(int scene, long userId, Integer returnCode) {
        String key = scene + "," + userId + "," + returnCode;
        Optional<GetAvatarRespDto> optional = USER_AVATAR_CACHE.getUnchecked(key);
        return optional.orElse(null);
    }

}
