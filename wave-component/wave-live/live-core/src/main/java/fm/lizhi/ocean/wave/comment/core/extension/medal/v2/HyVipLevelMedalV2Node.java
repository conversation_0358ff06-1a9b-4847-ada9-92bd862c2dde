package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.vip.constant.noble.NobleStatusEnum;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.config.HyCommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.api.VipPrivilegeService;
import fm.lizhi.ocean.wave.user.result.VipPrivilegeMedalResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA;

/**
 * vip等级勋章节点
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyVipLevelMedalV2Node implements ICommentV2MedalNode {


    @Autowired
    private VipPrivilegeService vipPrivilegeService;

    @Autowired
    private CommonProviderConfig commonConfig;

    @Autowired
    private CommentConfig commentConfig;

    /**
     * 获取贵族勋章
     *
     * @param userId 用户🆔
     * @return 结果
     */
    public VipPrivilegeMedalResult getUserVipPrivilegeMedal(long userId) {
        Result<VipPrivilegeMedalResult> result = vipPrivilegeService.getVipInfoFromCache(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy getUserVipInfo userId={},rCode={}", userId, result.rCode());
            return null;
        }
        return result.target();
    }


    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {

        HyCommentConfig configHy = commentConfig.getHy();
        int medalShowArea = context.getMedalShowArea();

        long userId;
        if (medalShowArea == COMMENT_AREA.getArea()) {
            userId = context.getComment().getUserId();
        } else {
            userId = context.getEnterNoticeEntry().getUserId();
        }
        // 贵族勋章
        VipPrivilegeMedalResult vipPrivilegeMedalBean = getUserVipPrivilegeMedal(userId);
        if (vipPrivilegeMedalBean == null) {
            return Optional.empty();
        }
        if (NobleStatusEnum.UN_EFFECTIVE.getCode().equals(vipPrivilegeMedalBean.getStatus())) {
            return Optional.empty();
        }
        BusinessConfig businessConfig = commonConfig.getBusinessConfig(BusinessEvnEnum.HEI_YE.getAppId());
        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), vipPrivilegeMedalBean.getIcon()));
        badgeImageVO.setBadgeAspect(configHy.getUserVipAspect());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
