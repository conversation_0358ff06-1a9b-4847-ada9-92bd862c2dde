package fm.lizhi.ocean.wave.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * 默认JSON序列化工具类
 */
public final class JsonUtils {

    /**
     * 默认的ObjectMapper对象
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .registerModule(new ParameterNamesModule())
            .registerModule(new Jdk8Module())
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(MapperFeature.DEFAULT_VIEW_INCLUSION, false);

    private JsonUtils() {
    }

    /**
     * 将对象按默认的规则序列化为JSON字符串
     *
     * @param value 要序列化的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object value) {
        try {
            return OBJECT_MAPPER.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象
     *
     * @param content JSON字符串
     * @param clazz   要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T fromJsonString(String content, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(content, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象, 基于java.lang.reflect.Type
     *
     * @param content JSON字符串
     * @param type    Java中的类型描述
     * @param <T>     反序列化的类型泛型
     * @return 反序列化的对象
     */
    public static <T> T fromJsonString(String content, Type type) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructType(type);
        try {
            return OBJECT_MAPPER.readValue(content, javaType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将对象按默认的规则序列化转换为指定的类型对象
     *
     * @param fromValue   要转换的来源对象
     * @param toValueType 要转换的目标类型
     * @param <T>         转换类型泛型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return OBJECT_MAPPER.convertValue(fromValue, toValueType);
    }

    /**
     * 反序列化json，如果有异常返回null
     *
     * @param content 要反序列化的字符串
     * @param clazz   反序列化的类型
     * @param <T>     泛型类型
     * @return 反序列化的对象
     */
    public static <T> T readValue(String content, TypeReference<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(content, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }
}
