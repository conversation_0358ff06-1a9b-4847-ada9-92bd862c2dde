package fm.lizhi.ocean.wave.activity.core.remote.adapter.xm;

import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import org.springframework.stereotype.Component;

@Component
public class XmOnlineStatusAdapter {

    /**
     * 在线状态适配
     *
     * @param status 状态
     * @return 平台状态
     */
    public OnlineStatusEnum statusAdapter(int status) {
        switch (status) {
            case 1:
                return OnlineStatusEnum.LIVING;
            case 2:
                return OnlineStatusEnum.ON_MIC;
            case 3:
                return OnlineStatusEnum.LISTEN;
            case 4:
                return OnlineStatusEnum.ONLINE;
            default:
                return OnlineStatusEnum.OFFLINE;
        }
    }

}
