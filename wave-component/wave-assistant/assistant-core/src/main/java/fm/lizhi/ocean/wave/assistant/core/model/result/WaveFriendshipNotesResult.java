package fm.lizhi.ocean.wave.assistant.core.model.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.assistant.core.model.vo.WaveFriendshipNotesTabVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.WaveGlobalFriendshipNotesVo;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveFriendshipNotesTabBean;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveGlobalFriendshipNotesBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 交友笔记结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WaveFriendshipNotesResult {


    /**
     * tab 列表
     */
    private WaveFriendshipNotesTabVo tab;

    /**
     * 全局笔记字段
     */
    private List<WaveGlobalFriendshipNotesVo> globalNoteList;

    /**
     * 用户 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 对方用户 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetUserId;

}
