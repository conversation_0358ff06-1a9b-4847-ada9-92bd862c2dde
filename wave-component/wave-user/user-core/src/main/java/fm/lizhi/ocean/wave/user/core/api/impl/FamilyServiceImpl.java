package fm.lizhi.ocean.wave.user.core.api.impl;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.bean.FamilyInfoBean;
import fm.lizhi.ocean.wave.user.core.manager.UserFamilyManager;
import fm.lizhi.ocean.wave.user.core.remote.bean.FamilyInfo;
import fm.lizhi.ocean.wave.user.core.remote.bean.UserFamilyInfo;
import fm.lizhi.ocean.wave.user.core.remote.service.IUserFamilyServiceRemote;
import fm.lizhi.ocean.wave.user.result.UserFamilyInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 家族服务
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Slf4j
@Component
public class FamilyServiceImpl implements FamilyService {

    @MyAutowired
    private IUserFamilyServiceRemote userFamilyServiceRemote;

    @Autowired
    private UserFamilyManager userFamilyManager;

    @Override
    public Result<List<Long>> getFamilySignNjIds(long userId, long familyId) {
        if (userId <= 0 && familyId <= 0) {
            return new Result<>(FamilyService.GET_FAMILY_SIGN_NJ_IDS_PARAM_ERROR, null);
        }
        Result<List<Long>> ret = userFamilyServiceRemote.getFamilySignNjIds(userId, familyId);
        if (ret.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("FamilyServiceImpl getFamilySignNjIds fail, userId={}`familyId={}`rCode={}", userId, familyId, ret.rCode());
            return new Result<>(FamilyService.GET_FAMILY_SIGN_NJ_IDS_FAIL, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ret.target());
    }

    @Override
    public Result<Long> playerCurSignNj(long userId) {
        if (userId <= 0) {
            return new Result<>(FamilyService.PLAYER_CUR_SIGN_NJ_PARAM_ERROR, null);
        }
        Result<Long> ret = userFamilyServiceRemote.playerCurSignNj(userId);
        if (ret.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("FamilyServiceImpl playerCurSignNj fail, userId={}`rCode={}", userId, ret.rCode());
            return new Result<>(FamilyService.PLAYER_CUR_SIGN_NJ_FAIL, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ret.target());
    }

    @Override
    public Result<FamilyInfoBean> getFamilyInfo(long familyId, long userId) {
        if (familyId <= 0 && userId <= 0) {
            return new Result<>(GET_FAMILY_INFO_PARAM_ERROR, null);
        }
        Result<FamilyInfo> ret
                = userFamilyServiceRemote.getFamilyInfo(familyId, userId);
        // 家族不存在
        if (ret.rCode() == GET_FAMILY_INFO_NOT_FOUND) {
            log.warn("FamilyServiceImpl getFamilyInfo not exists, familyId={}`userId={}`rCode={}", familyId, userId, ret.rCode());
            return new Result<>(GET_FAMILY_INFO_NOT_FOUND, null);
        }
        if (ret.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("FamilyServiceImpl getFamilyInfo fail, familyId={}`userId={}`rCode={}", familyId, userId, ret.rCode());
            return new Result<>(GET_FAMILY_INFO_FAIL, null);
        }

        FamilyInfoBean familyInfoBean = FamilyInfoBean.builder().id(ret.target().getId())
                .familyName(ret.target().getFamilyName())
                .familyIconUrl(ret.target().getFamilyIconUrl())
                .familyType(ret.target().getFamilyType())
                .familyNote(ret.target().getFamilyNote())
                .status(ret.target().getStatus())
                .userId(ret.target().getUserId())
                .build();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, familyInfoBean);
    }

    @Override
    public Result<UserFamilyInfoResult> getUserFamily(long userId, boolean onlyHighest) {
        if (userId <= 0) {
            return new Result<>(GET_USER_FAMILY_INFO_ILLEGAL_PARAM, null);
        }

        Result<UserFamilyInfo> userFamilyInfoRes = userFamilyServiceRemote.getUserFamilyInfo(userId);
        if (userFamilyInfoRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("FamilyServiceImpl getUserFamily fail, userId={}`rCode={}", userId, userFamilyInfoRes.rCode());
            if (userFamilyInfoRes.rCode() == IUserFamilyServiceRemote.GET_USER_FAMILY_INFO_NOT_FOUND) {
                return new Result<>(GET_USER_FAMILY_INFO_NOT_JOIN_FAMILY, null);
            }
            if (userFamilyInfoRes.rCode() == IUserFamilyServiceRemote.GET_USER_FAMILY_INFO_IN_APPLY) {
                return new Result<>(GET_USER_FAMILY_INFO_APPLYING_FAMILY, null);
            }
            return new Result<>(GET_USER_FAMILY_INFO_FAIL, null);
        }

        UserFamilyInfo userFamilyInfo = userFamilyInfoRes.target();
        UserFamilyInfoResult result = UserFamilyInfoResult.builder()
                .familyId(userFamilyInfo.getFamilyId())
                .familyName(userFamilyInfo.getFamilyName())
                .isFamily(userFamilyInfo.isFamily())
                .isPlayer(userFamilyInfo.isPlayer())
                .isRoom(userFamilyInfo.isRoom())
                .userId(userFamilyInfo.getUserId())
                .njId(userFamilyInfo.getNjId())
                .familyType(userFamilyInfo.getFamilyType())
                .roleDesc(userFamilyInfo.getRoleDesc())
                .build();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
    }

    @Override
    public Result<UserFamilyInfoResult> getUserFamilyByCache(long userId, boolean onlyHighest) {
        if (userId <= 0) {
            return new Result<>(GET_USER_FAMILY_INFO_ILLEGAL_PARAM, null);
        }

        Result<UserFamilyInfo> userFamilyInfoRes = userFamilyManager.getUserFamilyInfoByCache(userId);
        if (userFamilyInfoRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("FamilyServiceImpl getUserFamilyByCache fail, userId={}`rCode={}", userId, userFamilyInfoRes.rCode());
            if (userFamilyInfoRes.rCode() == IUserFamilyServiceRemote.GET_USER_FAMILY_INFO_NOT_FOUND) {
                return new Result<>(GET_USER_FAMILY_INFO_NOT_JOIN_FAMILY, null);
            }
            if (userFamilyInfoRes.rCode() == IUserFamilyServiceRemote.GET_USER_FAMILY_INFO_IN_APPLY) {
                return new Result<>(GET_USER_FAMILY_INFO_APPLYING_FAMILY, null);
            }
            return new Result<>(GET_USER_FAMILY_INFO_FAIL, null);
        }

        UserFamilyInfo userFamilyInfo = userFamilyInfoRes.target();
        UserFamilyInfoResult result = UserFamilyInfoResult.builder()
                .familyId(userFamilyInfo.getFamilyId())
                .familyName(userFamilyInfo.getFamilyName())
                .isFamily(userFamilyInfo.isFamily())
                .isPlayer(userFamilyInfo.isPlayer())
                .isRoom(userFamilyInfo.isRoom())
                .userId(userFamilyInfo.getUserId())
                .njId(userFamilyInfo.getNjId())
                .familyType(userFamilyInfo.getFamilyType())
                .roleDesc(userFamilyInfo.getRoleDesc())
                .build();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
    }

    @Override
    public Result<FamilyInfoBean> getUserInFamily(long userId) {
        Result<FamilyInfo> result = userFamilyServiceRemote.getUserInFamily(userId);
        if (RpcResult.isFail(result)){
            if (result.rCode() == IUserFamilyServiceRemote.GET_USER_IN_FAMILY_NO_DATA){
                return new Result<>(GET_USER_IN_FAMILY_NO_DATA, null);
            }
            return new Result<>(GET_USER_IN_FAMILY_PARAM_FAIL, null);
        }
        FamilyInfo familyInfo = result.target();
        FamilyInfoBean infoBean = FamilyInfoBean.builder()
                .familyType(familyInfo.getFamilyType())
                .id(familyInfo.getId())
                .userId(familyInfo.getUserId())
                .status(familyInfo.getStatus())
                .familyNote(familyInfo.getFamilyNote())
                .familyIconUrl(familyInfo.getFamilyIconUrl())
                .familyName(familyInfo.getFamilyName())
                .build();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, infoBean);
    }

    @Override
    public Result<Boolean> checkUserInPGC(long userId) {
        if (userId <= 0) {
            return new Result<>(CHECK_USER_IN_PGC_PARAM_FAIL, null);
        }

        Result<Boolean> result = userFamilyServiceRemote.checkUserInPGC(userId);
        if (RpcResult.isFail(result)) {
            return new Result<>(CHECK_USER_IN_PGC_FAIL, null);
        }
        return result;
    }

    @Override
    public Result<List<Long>> getSignSucNjIdsByFamilyId(long familyId) {
        Result<List<Long>> result = userFamilyServiceRemote.getSignSucNjIdsByFamilyId(familyId);
        if (RpcResult.isFail(result)) {
            return new Result<>(result.rCode(), null);
        }
        return result;
    }

    @Override
    public Result<List<Long>> getPlayerSign(List<Long> userIds) {
        Result<List<Long>> result = userFamilyServiceRemote.getPlayerSign(userIds);
        if (RpcResult.isFail(result)) {
            return new Result<>(result.rCode(), null);
        }
        return result;
    }

    @Override
    public Result<List<Long>> getSameSocietyFamily(long userId) {
        Result<List<Long>> result = userFamilyServiceRemote.getSameSocietyFamily(userId);
        if (RpcResult.isFail(result)) {
            return new Result<>(result.rCode(), null);
        }
        return result;
    }
}
