package fm.lizhi.ocean.wave.live.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;
import fm.lizhi.ocean.wave.live.core.model.vo.RecommendCategoryVO;
import fm.lizhi.ocean.wave.live.core.remote.param.recommendcard.GetUserRecommendedInfoRequest;
import fm.lizhi.ocean.wave.live.core.remote.param.recommendcard.UseRecommendCardRequest;
import fm.lizhi.ocean.wave.live.core.remote.result.recommendcard.GetUserRecommendedInfoResponse;
import fm.lizhi.ocean.wave.live.core.remote.result.recommendcard.RecommendCardConfigResponse;
import fm.lizhi.ocean.wave.live.core.remote.result.recommendcard.RecommendCardUseRecordResponse;
import org.joda.time.DateTime;

import java.util.List;

public interface IRecommendCardServiceRemote extends IBaseRemoteServiceInvoker {

    /**
     * 获取推荐卡配置
     * @param userId
     * @return
     */
    Result<RecommendCardConfigResponse> getRecommendCardConfig(Long userId);


    /**
     * 使用推荐卡
     * @param request
     * @return
     */
    Result<String> useRecommendCard(UseRecommendCardRequest request);

    /**
     * 获取推荐卡位置列表
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    Result<List<RecommendCategoryVO>> getPositions(Long userId, DateTime startTime, DateTime endTime);

    /**
     * 获取推荐卡使用记录
     * @param userIds
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Result<RecommendCardUseRecordResponse> getRecommendCardUseRecord(List<Long> userIds, int pageNumber, int pageSize);

    /**
     * 查询用户被推荐位置信息
     * @param request
     * @return
     */
    Result<GetUserRecommendedInfoResponse> getUserRecommendedInfo(GetUserRecommendedInfoRequest request);

    int GET_RECOMMEND_CARD_CONFIG_ERROR = 1;

    int USE_RECOMMEND_CARD_ERROR = 2;

    int USE_RECOMMEND_CARD_NOT_SUCCESS = 3;

    int GET_POSITIONS_FREE_SPACE_ERROR = 4;

    int GET_POSITIONS_RECOMMENDATION_CARD_CONFIG_ERROR = 5;

    int GET_POSITIONS_ERROR = 6;

    int GET_RECOMMEND_CARD_USE_RECORD_ERROR = 7;

    int GET_USER_RECOMMEND_CARD_STOCK_ERROR = 8;

    int GET_USER_RECOMMENDED_INFO_NOT_FOUND = 9;

    int GET_USER_RECOMMENDED_INFO_ERROR = 10;
}
