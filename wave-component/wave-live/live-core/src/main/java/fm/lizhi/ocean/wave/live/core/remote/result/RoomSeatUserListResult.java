package fm.lizhi.ocean.wave.live.core.remote.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoomSeatUserListResult {


    private List<RoomSeatUserResult> roomUserList;

    /**
     * 总人数
     */
    private int totalNum;

    /**
     * 是否最后一页
     */
    private boolean isLastPage ;

    /**
     * 分页查询
     */
    private String performanceId;
}
