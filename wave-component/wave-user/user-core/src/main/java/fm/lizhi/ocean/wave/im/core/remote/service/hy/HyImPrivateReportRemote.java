package fm.lizhi.ocean.wave.im.core.remote.service.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.security.chat.api.HyReviewChatService;
import fm.lizhi.hy.security.chat.protocol.HyReviewChatProto;
import fm.lizhi.hy.security.chat.protocol.HyReviewChatRequestProto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.im.core.remote.service.ImPrivateReportRemote;
import fm.lizhi.ocean.wave.im.core.remote.service.bean.PrivateReportParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HyImPrivateReportRemote implements ImPrivateReportRemote {


    @Autowired
    private HyReviewChatService hyReviewChatService;

    @Override
    public Result<Void> imPrivateReport(PrivateReportParam param) {
        Integer msgType = param.getMsgType();
        String content = param.getContent();

        if (StringUtils.contains(content, "rongcloud-image")) {
            if (msgType == 2) {
                msgType = 1;
            }
        } else {
            if (msgType == 1) {
                msgType = 2;
            }
        }

        Long userId = param.getUserId();
        HyReviewChatRequestProto.SubmitChatReportRequest.Builder builder = HyReviewChatRequestProto.SubmitChatReportRequest.newBuilder();
        builder.setUid(userId);
        builder.setTargetUid(param.getTargetUserId());
        builder.setMsgType(msgType);
        builder.setContent(param.getContent());
        builder.setMsgId(param.getMsgId());
        Result<HyReviewChatProto.ResponseSubmitChatReport> resp = hyReviewChatService.submitChatReport(builder.build());
        if (resp.rCode() != 0) {
            return new Result<>(PRIVATE_REPORT_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }
}
