package fm.lizhi.ocean.wave.common.config;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.EnvUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "wave-common")
public class CommonProviderConfig {

    /**
     * 连接数量
     */
    private int dcProxyConnectionCount = 10;

    /**
     * DC请求超时毫秒数
     */
    private int dcProxyTimeoutMillis = 5000;

    /**
     * 配置使用pojo的配置参数
     */
    private String usePojoProviderConfig;

    /**
     * 业务不实现的标准接口配置
     */
    private String unImplementStandardAPIProviders;

    /**
     * 业务不实现的标准接口的方法。此配置优先级高于unImplementStandardAPIProviders这个配置。
     * 如果此配置配置了类中的方法，上述的参数不会对类其他方法起效。此方法配置为 {${业务线}_${类简单名}:[${methodName},${methodName1}]}
     */
    private String unImplementStandardAPIProviderMethods;

    /**
     * 业务信息配置
     */
    private String businessConfig = "";


    /**
     * 记录操作日志的uri列表
     */
    private String recordLogsUriList = "/live/startLive";


    /**
     * 房间管理图标
     */
    private String roomManagerIconUrl = "https://cdn.lizhilive.com/studio/2021/09/13/2895423241680170038_108x48.png";

    /**
     * 房主图片
     */
    private String roomOwnerIconUrl = "https://cdn.lizhilive.com/studio/2021/09/13/2895423306104679990_108x48.png";

    /**
     * 房主、管理图标高宽比
     */
    private float userRoleImageBadgeAspect = 0.44444f;

    /**
     * pp项目上传的代理的key
     */
    private String ppUploadFileProxyKey = "dc_proxy_office";

    /**
     * pp上传得公有得cdn
     */
    private String ppCommonCdn = "http://cdnoffice.lizhi.fm/";

    /**
     *
     */
    private int ppUploadFileConnectionCount = 10;

    /**
     * 健康检查开关
     */
    private boolean checkupLogSwitch = true;

    /**
     * 业务公共配置，主要配置一些业务配置给前端用于展示/业务逻辑使用
     */
    private String bizCommonConfig;

    /**
     * 验证码redis地址
     */
    private String smsCodeRedisHost = EnvUtils.isOffice() ? "redis.ops.lizhi.fm" : EnvUtils.isPre() ? "***************" : "";

    /**
     * 验证码redis端口
     */
    private int smsCodeRedisPort = EnvUtils.isOffice() ? 6379 : EnvUtils.isPre() ? 6415 : 0;

    /**
     * 最大的客户端版本号
     */
    private int maxClientVersion = 1000000;

    /**
     * 请求成功的状态码
     */
    private List<Integer> reqSuccessCodeWhiteList = Lists.newArrayList(0);

    /**
     * 问题大全配置信息
     */
    private String questionConfigList = "[{\"id\":1,\"content\":\"情感分享\"},{\"id\":2,\"content\":\"幽默风格\"},{\"id\":3,\"content\":\"悬疑风格\"},{\"id\":4,\"content\":\"随意闲聊\"},{\"id\":5,\"content\":\"心理测试\"},{\"id\":6,\"content\":\"挑战性问题\"}]";

    /**
     * 预设prompt
     */
    private String preSetQuestionPrompt = "请生成2个风格属于%S的问题，并结合问题再给出2个可能的追问，每个问题和追问都不得超过60字。";

    /**
     * miniMax的appKey
     */
    private String miniMaxAppKey = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    /**
     * miniMax的groupId
     */
    private String miniMaxGroupId = "1729339214534808258";

    /**
     * 地址
     */
    private String miniMaxApiUrl = "https://api.minimax.chat/v1/text/chatcompletion_pro";

    /**
     * 最大请求次数
     */
    private int okHttpMaxRequestCount = 5;

    /**
     * miniMax大模型预设配置
     */
    private String miniMaxRequestConfig = "{\"botSetting\":[{\"botName\":\"AI话题工具助手\",\"content\":\"AI话题工具助手专注于话题工具的研发，为用户提供更好的话题工具服务。\"}],\"messages\":[{\"sender_name\":\"用户\",\"sender_type\":\"USER\"}],\"model\":\"abab5.5-chat\",\"replyConstraints\":{\"senderName\":\"AI话题工具助手\",\"senderType\":\"BOT\"}}";

    /**
     * 最小问题结果阈值，小于等于该值时，需要重新生成了
     */
    private int minQuestionResLen = 5;

    /**
     * 问题结果队列中最大值，单个问题类型至少存储6个
     */
    private int maxQuestionResLen = 10;

    /**
     * 用户每分钟最大生成问题次数
     */
    private int maxFrequencyLimit = 3;

    /**
     * 功能支持的最低版本号
     * key=功能标识
     *
     * @see fm.lizhi.ocean.wave.common.constant.FunctionConstant
     * value=最低版本号（请求头的clientVersion）
     */
    private Map<String, String> functionVersion = new HashMap<>();

    /**
     * 是否打印请求日志
     */
    private boolean printRpcLog = true;

    /**
     * 强制使用荔枝泛化调用的服务 json字符串
     * key=服务名，支持前缀匹配
     * value=方法名,多个用英文逗号分隔，*表示全部
     */
    private String useLzGenericService = "{\"key\":\"value\"}";

    /**
     * AI润色文本最大长度
     */
    private int polishTextMaxLength = 20;

    /**
     * AI润色mock数据开关
     */
    private boolean polishMockSwitch;

    /**
     * 用户每日最大润色次数
     */
    private int userDayMaxPolishCount = 100;

    /**
     * mock数据
     */
    private String mockPolishResult = "[{\"index\":0,\"end\":false,\"result\":\"1.\",\"promptTokens\":31,\"completionTokens\":0},{\"index\":1,\"end\":false,\"result\":\" 哼，晚上好，你可真是让我等了好久呢。\",\"promptTokens\":31,\"completionTokens\":0},{\"index\":2,\"end\":false,\"result\":\"\\n2. 晚上好，你这才出现，真是让我心焦。\",\"promptTokens\":31,\"completionTokens\":0},{\"index\":3,\"end\":false,\"result\":\"\\n3. 嗯，晚上好，我就知道你会来的，只是时间问题。\",\"promptTokens\":31,\"completionTokens\":0},{\"index\":4,\"end\":false,\"result\":\"\\n4. 晚上好，你可真是让我心心念念啊，终于来了。\",\"promptTokens\":31,\"completionTokens\":0},{\"index\":5,\"end\":false,\"result\":\"\\n5. 哼，晚上好，我可是等了你好久，你可要好好补偿我。\",\"promptTokens\":31,\"completionTokens\":0}]";

    /**
     * mock风格数据
     */
    private String mockPolishStyle = "[{\"id\":1,\"name\":\"温柔\"},{\"id\":2,\"name\":\"呆萌\"},{\"id\":3,\"name\":\"霸总\"},{\"id\":4,\"name\":\"傲娇\"},{\"id\":5,\"name\":\"幽默\"},{\"id\":6,\"name\":\"阳光\"},{\"id\":7,\"name\":\"暧昧\"}]";

    /**
     * 是否开启灰度发布功能
     */
    private boolean enablePushConfig = false;

    /**
     * 全量安装包url兜底值
     */
    private String fullInstallerUrlFallback = "https://wave-download.lzpscn1.com/static/win_x64/%E5%BC%80%E6%92%AD%E5%8A%A9%E6%89%8B-setup.zip";

    /**
     * 添加名单的最大用户id数
     */
    private int maxAddNameListNumber = 300;

    /**
     * 灰度策略配置
     */
    private String grayscaleConfig = "[]";

    /**
     * 图片大小限制,单位MB
     */
    private Integer imageSizeLimit = 5;

    private String noPrintLogArgUrl = "/common/upload/img";

    /**
     * 进入直播间校验语音最低版本号
     */
    private Integer enterLiveCheckVoiceMinVersion = 1;

    /**
     * apollo域名
     */
    private String apolloHost = "https://configportalinoffice.lizhi.fm";

    /**
     * apollo的token
     */
    private String apolloToken = "f3787557ddcc4963d3b7628b48180c6f29b46201";

    /**
     * 打印日志时间阈值
     */
    private Integer printLogTimeLimit = 10000;


    /**
     * 小于该版本，心跳事件继续使用轮询进行上报
     */
    private Integer minHeartbeatClientVersion = 0;

    /**
     * 根据应用id获取配置信息
     *
     * @param appId 应用id
     * @return 业务配置信息
     */
    public BusinessConfig getBusinessConfig(Integer appId) {
        List<BusinessConfig> businessConfigs = JSONObject.parseArray(businessConfig, BusinessConfig.class);
        for (BusinessConfig config : businessConfigs) {
            if (config.getAppId().equals(appId)) {
                return config;
            }
        }
        return null;
    }

    /**
     * 获取配置信息列表
     *
     * @return 配置信息列表
     */
    public List<BusinessConfig> listBusinessConfig() {
        return JSONObject.parseArray(businessConfig, BusinessConfig.class);
    }
}
