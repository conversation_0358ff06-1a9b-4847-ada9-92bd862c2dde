package fm.lizhi.ocean.wave.common.auto.route.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * 业务注解。需要注册对应的业务接口，可以使用此注解。
 * 请注意，此注解需要放在spring注入类上。就是存在@Configuration、@Component 这类的注解
 *
 * 此类注解真正执行时，会进行class的全路径类名去重。
 * @ScanBusinessProviderAPI(values = {@ScanStandardAPI.RegisterProvider(interfaceClass = UserSessionHelper.class, timeout = 1000)
 *                 , @ScanStandardAPI.RegisterProviderBO(interfaceClass = UnknownError.class, timeout = 4000)})
 */
@Target(ElementType.TYPE)
@Retention(RUNTIME)
public @interface ScanBusinessProviderAPI {
    /**
     * 具体的类
     * @return
     */
    RegisterProvider[] values() default {};

    @interface RegisterProvider{
        Class<?> interfaceClass();
        int timeout() default 5000;
        boolean simpleName() default false;

        /**
         * 强制使用DubboClient进行请求, 而非默认的泛化调用. 该属性是临时过渡方案, 后续将推动各业务方修改proto格式, 请不要过多使用该属性.
         *
         * @return 是否强制使用DubboClient进行请求
         */
//        boolean useDubboClient() default false;
    }
}
