package fm.lizhi.ocean.wave.user.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/22
 */
public enum UserGenderEnum {

    /**
     * 男
     */
    MALE(0),

    /**
     * 女
     */
    FEMALE(1),

    /**
     * 未知
     */
    UNKNOWN(99);

    private final int value;


    private static final Map<Integer, UserGenderEnum> MAP = new HashMap<>(8);

    static {
        for (UserGenderEnum item : UserGenderEnum.values()) {
            MAP.put(item.getValue(), item);
        }
    }

    UserGenderEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static UserGenderEnum from(int value) {
        return MAP.getOrDefault(value, UNKNOWN);
    }

}
