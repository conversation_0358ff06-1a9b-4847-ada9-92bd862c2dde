package fm.lizhi.ocean.wave.amusement.core.remote.result;

import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallBizTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: 创建通话返回信息
 * @author: guoyibin
 * @create: 2024/03/01 15:55
 */
@Data
@Accessors(chain = true)
public class ResponseCreateVoiceCall {

    private String appKey;

    private long channelId;

    /**
     * 接收者者语音通话ID
     */
    private long calleeVoiceId;

    private long callerVoiceId;

    /**
     * 通话类型
     */
    private VoiceCallBizTypeEnum voiceCallBizType;

    /**
     * 通话状态
     */
    private VoiceCallStatusEnum voiceCallWaveStatus;

    /**
     * 语音通话创建后唯一ID
     */
    private long callId;

}
