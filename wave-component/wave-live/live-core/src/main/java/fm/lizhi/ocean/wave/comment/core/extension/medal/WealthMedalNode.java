package fm.lizhi.ocean.wave.comment.core.extension.medal;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 财富等级勋章节点
 */
@Component
public class WealthMedalNode implements ICommentMedalNode {

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoContext context) {
        int medalShowArea = context.getMedalShowArea();
        WealthLevelDTO wealthLevel = null;
        BusinessConfig businessConfig = commonProviderConfig.getBusinessConfig(context.getAppId());
        if (medalShowArea == GenMedalInfoContext.MedalShowArea.COMMENT_AREA.getArea()) {
            wealthLevel = context.getComment().getWealthLevel();
        } else if (medalShowArea == GenMedalInfoContext.MedalShowArea.ENTER_NOTICE_AREA.getArea()) {
            wealthLevel = context.getEnterNoticeEntry().getWealthInfo();
        }

        //财富等级为0或者财富等级信息不存在，不展示财富等级勋章
        if (wealthLevel == null || wealthLevel.getLevel() <= 0 || StringUtils.isBlank(wealthLevel.getCover())) {
            return Optional.empty();
        }

        String wealthLevelCdn = context.getCommentConfig().getWealthLevelCdn();
        if (StringUtils.isBlank(wealthLevelCdn)) {
            wealthLevelCdn = businessConfig.getCdnHost();
        }

        // 财富等级
        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeUrl(UrlUtils.addCdnHost(wealthLevelCdn, wealthLevel.getCover()));
        badgeImageVO.setBadgeAspect(wealthLevel.getAspect());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
