package fm.lizhi.ocean.wave.api.live.constants.hy;

import java.util.HashMap;
import java.util.Map;

/**
 * 房间类型
 * <AUTHOR>
 * @date 2023/5/17
 */
public enum HyLiveRoomType {
    /**
     * 派对类型
     */
    DEFAULT(0),
    /**
     * 交友类型
     */
    BLIND_DATE(1),
    /**
     * 点唱类型
     */
    SING(2),
    /**
     * 个播类型
     */
    PERSON(4)
    ;
    private int value;

    private static Map<Integer, HyLiveRoomType> map = new HashMap<>();

    static {
        for (HyLiveRoomType object : HyLiveRoomType.values()) {
            map.put(object.getValue(), object);
        }
    }

    HyLiveRoomType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    /**
     * 根据值类型找枚举
     */
    public static HyLiveRoomType from(int value) {
        return map.get(value);
    }
}