package fm.lizhi.ocean.wave.live.core.extension.searchlive.biz.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.searchlive.ISearchLiveProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/20 15:48
 */
@Component
@Slf4j
public class XmSearchLiveProcessor implements ISearchLiveProcessor {

    @Autowired
    private LiveConfig liveConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public CommonLiveConfig getLiveConfig() {
        return liveConfig.getXm();
    }
}
