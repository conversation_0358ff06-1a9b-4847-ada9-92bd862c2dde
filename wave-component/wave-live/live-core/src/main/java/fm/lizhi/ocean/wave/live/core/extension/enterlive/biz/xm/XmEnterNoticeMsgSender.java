package fm.lizhi.ocean.wave.live.core.extension.enterlive.biz.xm;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementService;
import fm.lizhi.ocean.wave.api.amusement.bean.SeatInfoBeanInfo;
import fm.lizhi.ocean.wave.api.amusement.result.GetAllSeatsInfoResult;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.bean.EnterInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.biz.AbstractEnterNoticeMsgSender;
import fm.lizhi.ocean.wave.live.core.remote.result.mount.GetUserUsingMountResponse;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description: 西米进房公告发送类
 * @author: chenzihan
 */
@Slf4j
@Component
public class XmEnterNoticeMsgSender extends AbstractEnterNoticeMsgSender {

    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private UserService userService;
    @Autowired
    private FamilyService familyService;
    @Autowired
    private AmusementService amusementService;

    /**
     * 控制最大消息队列长度
     */
    private AtomicInteger rc = new AtomicInteger(0);
    /**
     * 控制最大消息队列长度
     */
    private AtomicInteger rcVehicle = new AtomicInteger(0);
    private ConcurrentLinkedQueue<String> xmNoticeQ;
    private ConcurrentLinkedQueue<String> xmVehicleNoticeQ;
    private ExecutorService executorService;
    private ExecutorService vehicleExecutorService;

    @PostConstruct
    public void init() {
        /**
         * 初始化：1、缓存服务接口；2、初始化公告模板
         */

        xmNoticeQ = new ConcurrentLinkedQueue<>();
        executorService = ThreadUtils.getTtlExecutors("xm-save-enter-notice-worker");
        for (int count = 0; count < liveConfig.getXm().getSendEnterNoticeThreadCount(); ++count) {
            executorService.execute(RunnableWrapper.of(this::getNoticeAndSend));
        }


        //座驾的队列
        xmVehicleNoticeQ = new ConcurrentLinkedQueue<>();
        vehicleExecutorService = ThreadUtils.getTtlExecutors("xm-vehicle-save-enter-notice-worker");
        for (int count = 0; count < liveConfig.getXm().getSendEnterNoticeThreadCount(); ++count) {
            vehicleExecutorService.execute(RunnableWrapper.of(this::getVehicleNoticeAndSend));
        }
    }


    @Override
    protected void offerNoticeMsgToQueue(GetUserUsingMountResponse mount, String notice) {
        if (mount != null) {
            //座驾
            int index = rcVehicle.getAndIncrement();//加一，做最大限制

            if (index < liveConfig.getXm().getVehicleEnterNoticeQueueMaxSize()) {
                xmVehicleNoticeQ.offer(notice);
            }
        } else {

            int index = rc.getAndIncrement();//加一，做最大限制
            if (index < liveConfig.getXm().getEnterNoticeUserMaxLen()) {
                xmNoticeQ.offer(notice);
            }
        }
    }

    @Override
    protected boolean isEnterNoticeSwitchOn() {
        return liveConfig.getXm().getEnterNoticeSwitch();
    }

    @Override
    protected String getVehicleNoticeFromQueue() {
        String notice = null;
        try {
            notice = xmVehicleNoticeQ.poll();
            rcVehicle.getAndDecrement();//减一，作为最大容量统计
        } catch (Exception e) {
            log.error("pp getVehicleNoticeAndSend error:", e);
        }
        return notice;
    }

    @Override
    protected String getNoticeFromQueue() {
        String notice = null;
        try {
            notice = xmNoticeQ.poll();
            rc.getAndDecrement();//减一，作为最大容量统计
        } catch (Exception e) {
            log.error("pp getNoticeAndSend error:", e);
        }
        return notice;
    }

    /**
     * 完善一下进房公告
     * @param enterVo
     */
    @Override
    protected void fillEnterNoticeInfo(EnterInfoBean enterVo) {
        super.fillEnterNoticeInfo(enterVo);

        // 使用基础能力升级版本接口
        enterVo.setBasicAbilityUpgradeVersion(true);
        Result<FreshUserResult> freshUserResultResult = userService.isFreshUserByCache(enterVo.getUserId());
        if (freshUserResultResult.rCode() != 0) {
            return;
        }
        // 设置是否新用户
        boolean isFreshUser = freshUserResultResult.target().isIs();
        enterVo.setFreshUser(isFreshUser);
        if (!isFreshUser) {
            return;
        }

        // 获取麦上用户ids
        Result<GetAllSeatsInfoResult> seatsInfoResultResult = amusementService.getAllSeatsInfo(enterVo.getLiveId());
        if (seatsInfoResultResult.rCode() != 0) {
            return;
        }
        List<Long> seatUserIds = seatsInfoResultResult.target().getSeatInfoList().stream()
                .map(SeatInfoBeanInfo::getUserId)
                .filter(userId -> userId != null && userId > 0)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(seatUserIds)) {
            return;
        }

        // 查询麦上的签约用户
        Result<List<Long>> getPlayerSignResult = familyService.getPlayerSign(seatUserIds);
        if (getPlayerSignResult.rCode() != 0) {
            return;
        }

        List<Long> playerSignNjIds = getPlayerSignResult.target();
        if (CollUtil.isNotEmpty(playerSignNjIds)) {
            enterVo.setPlayerIdList(playerSignNjIds);
        }
    }

    @Override
    protected BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
