package fm.lizhi.ocean.wave.amusement.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum WaveManagerOperationTypeEnum {

    UNKNOWN(-1),

    /**
     * 主播接受嘉宾上麦
     */
    AGREE_HOLD_MICROPHONE(1),

    /**
     * 主播踢嘉宾下麦
     */
    KICK_OUT_MICROPHONE(2),

    /**
     * 主播对嘉宾闭麦
     */
    CLOSE_MICROPHONE(3),

    /**
     * 主播对嘉宾开麦
     */
    OPEN_MICROPHONE(4);

    private int operation;

    public static WaveManagerOperationTypeEnum getOperation(int operation) {
        for (WaveManagerOperationTypeEnum value : WaveManagerOperationTypeEnum.values()) {
            if (value.operation == operation) {
                return value;
            }
        }
        return UNKNOWN;
    }

}
