package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import lombok.Data;

import java.util.List;

/**
 * 用户信息VO
 * <AUTHOR>
 */
@Data
public class UserVO {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户头像
     */
    private String userAvatar;
    /**
     * 用户icon列表
     */
    private List<BadgeImageVO> userIcons;

    /**
     * 用户的头像框
     */
    private UserAvatarWidget userAvatarWidget;

    /**
     * 贵宾卡标识（西米独有）
     */
    private List<String> roomVipUrls;

    /**
     * 贵宾 昵称的颜色 （西米独有）
     */
    private List<Long> nameColorsList;

    /**    ximi独有
     *     MONTH_ROOM_VIP(1, "月贵宾"),
     *     YEAR_ROOM_VIP(2, "年贵宾"),
     */
    private Integer userRoomVipStatus;
}
