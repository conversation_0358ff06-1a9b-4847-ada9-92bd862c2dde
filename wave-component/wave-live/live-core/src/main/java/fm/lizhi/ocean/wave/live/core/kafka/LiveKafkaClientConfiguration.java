package fm.lizhi.ocean.wave.live.core.kafka;

import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Configuration
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wave.live.core.kafka.consumer")
public class LiveKafkaClientConfiguration {

    @Bean("hyKafkaTemplate")
    @ConditionalOnMissingBean(name = "hyKafkaTemplate")
    public KafkaTemplate hyKafkaTemplate() {
        return new KafkaTemplate("hy-kafka250-bootstrap-server");
    }

    @Bean("ppKafkaTemplate")
    @ConditionalOnMissingBean(name = "ppKafkaTemplate")
    public KafkaTemplate ppKafkaTemplate() {
        return new KafkaTemplate("pp-kafka250-bootstrap-server");
    }

    @Bean("xmKafkaTemplate")
    @ConditionalOnMissingBean(name = "xmKafkaTemplate")
    public KafkaTemplate xmKafkaTemplate() {
        return new KafkaTemplate("xm-kafka250-bootstrap-server");
    }



}
