package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.ocean.wave.comment.core.extension.comment.ISyncCommentProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SyncCommentPreBean;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import org.springframework.stereotype.Component;

@Component
public class XmSyncCommentProcessor implements ISyncCommentProcessor {

    @Override
    public ResultVO<Void> preprocessor(SyncCommentPreBean data) {

        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> postprocessor(Void data) {
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }
}
