package fm.lizhi.ocean.wave.activity.core.constant;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.Getter;

/**
 * 平台榜单类型映射
 *
 * <AUTHOR>
 */
@Getter
public enum GlobalRankTypeMapping {
    //===================== hy平台榜单类型 ===================
    /**
     * 平台贡献榜
     */
    HY_GLOBAL_COIN_RANK(RankType.GLOBAL_COIN, BusinessEvnEnum.HEI_YE, 1),
    /**
     * 平台魅力榜
     */
    HY_GLOBAL_MOODS_RANK(RankType.GLOBAL_MOODS, BusinessEvnEnum.HEI_YE, 2),
    /**
     * 平台房间榜
     */
    HY_GLOBAL_ROOM_RANK(RankType.GLOBAL_ROOM, BusinessEvnEnum.HEI_YE, 3),
    /**
     * 平台家族榜
     */
    HY_GLOBAL_FAMILY_RANK(RankType.GLOBAL_FAMILY, BusinessEvnEnum.HEI_YE, 4),

    /**
     * 点唱平台榜-歌手榜
     */
    HY_SING_GLOBAL_SINGER_RANK(RankType.SING_GLOBAL_SINGER, BusinessEvnEnum.HEI_YE, 5),
    /**
     * 点唱平台榜-房间榜
     */
    HY_SING_GLOBAL_ROOM_RANK(RankType.SING_GLOBAL_ROOM, BusinessEvnEnum.HEI_YE, 6),

    // ====================== pp平台榜单类型 ===================

    // ====================== xm平台榜单类型 ===================
    /**
     * 平台贡献榜
     */
    XM_GLOBAL_COIN_RANK(RankType.GLOBAL_COIN, BusinessEvnEnum.XIMI, 1),
    /**
     * 平台魅力榜
     */
    XM_GLOBAL_MOODS_RANK(RankType.GLOBAL_MOODS, BusinessEvnEnum.XIMI, 2),
    /**
     * 平台房间榜
     */
    XM_GLOBAL_ROOM_RANK(RankType.GLOBAL_ROOM, BusinessEvnEnum.XIMI, 3),
    /**
     * 平台家族榜
     */
    XM_GLOBAL_FAMILY_RANK(RankType.GLOBAL_FAMILY, BusinessEvnEnum.XIMI, 4),

    ;

    /**
     * 平台榜单类型
     */
    private final int waveRankType;
    /**
     * 业务环境类型
     */
    private final BusinessEvnEnum businessEvn;
    /**
     * 业务榜单类型
     */
    private final int bizRankType;

    GlobalRankTypeMapping(int waveRankType, BusinessEvnEnum businessEvn, int bizRankType) {
        this.waveRankType = waveRankType;
        this.businessEvn = businessEvn;
        this.bizRankType = bizRankType;
    }

    /**
     * 业务方榜单类型转平台榜单类型
     *
     * @param bizRankType 业务榜单类型
     * @param businessEvn 环境
     * @return 平台评论类型
     */
    public static int getWaveRankType(int bizRankType, BusinessEvnEnum businessEvn) {
        for (GlobalRankTypeMapping typeMapping : values()) {
            if (typeMapping.businessEvn != businessEvn) {
                //不是同业务的，过滤掉
                continue;
            }

            if (typeMapping.getBizRankType() == bizRankType) {
                return typeMapping.getWaveRankType();
            }
        }
        //找不到对应的类型，抛出异常
        throw new RuntimeException("找不到合适的平台榜单类型");
    }

    /**
     * 业务方榜单类型转平台榜单类型
     *
     * @param waveRankType 平台业务榜单类型
     * @param businessEvn  环境
     * @return 业务评论类型
     */
    public static int getBizRankType(int waveRankType, BusinessEvnEnum businessEvn) {
        for (GlobalRankTypeMapping typeMapping : values()) {
            if (typeMapping.businessEvn != businessEvn) {
                //不是同业务的，过滤掉
                continue;
            }

            if (typeMapping.getWaveRankType() == waveRankType) {
                return typeMapping.getBizRankType();
            }
        }
        //找不到对应的类型，抛出异常
        throw new RuntimeException("找不到合适的业务榜单类型");
    }
}
