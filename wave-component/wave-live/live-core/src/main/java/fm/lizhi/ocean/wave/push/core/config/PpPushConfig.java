package fm.lizhi.ocean.wave.push.core.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * PP推送配置
 */
@Data
public class PpPushConfig implements CommonPushConfig{

    /**
     * 飘屏开关，默认开
     */
    public boolean isNoticeSwitch = true;

    /**
     * 主播可以看到飘屏开关，默认关
     */
    public boolean noticeGiftAnchorSwitch = true;

    /**
     * 直播间飘屏、全服飘屏白名单Uid。多个用,分隔
     */
    public List<Long> broadcastGlobalWhiteUid = new ArrayList<>();

    /**
     * 大主播限流开关
     */
    public boolean bigStarLimitSwitch = false;

    /**
     * 大主播直播间直播ID列表
     */
    public List<Long> bigStarLiveLiveIds = new ArrayList<>();

    /**
     * 大主播UID列表
     */
    public List<Long> bigStarUserIds = new ArrayList<>();

    /**
     * 大主播直播间限流频率
     */
    public Integer bigStarGetCommentFrequency;

    /**
     * 查询飘屏最大条数
     */
    public Integer noticeMaxCount = 100;

    /**
     * 获取飘屏请求间隔,单位s
     */
    public Integer broadcastRequestInterval = 2;

}
