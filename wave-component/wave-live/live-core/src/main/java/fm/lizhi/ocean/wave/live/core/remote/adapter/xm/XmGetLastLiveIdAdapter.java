package fm.lizhi.ocean.wave.live.core.remote.adapter.xm;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.xm.api.LiveNewService;
import fm.lizhi.live.room.xm.protocol.LiveNewProto;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLatestLiveIdRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/13 11:04
 */
@Slf4j
@Component
public class XmGetLastLiveIdAdapter implements IRemoteMethodParamAndResultAdapter<GetLatestLiveIdRemoteParam
        , LiveNewProto.GetUpToDateLiveIdParams
        , Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache>
        , Result<Long>
        > {

    @Override
    public LiveNewProto.GetUpToDateLiveIdParams convertParam(GetLatestLiveIdRemoteParam args) {
        LiveNewProto.GetUpToDateLiveIdParams.Builder builder = LiveNewProto.GetUpToDateLiveIdParams.newBuilder();
        if (args.getLiveRoomId() != null) {
            builder.setLiveRoomId(args.getLiveRoomId());
        }
        if (args.getUserId() != null) {
            builder.setUserId(args.getUserId());
        }
        builder.setAppId(BusinessEvnEnum.XIMI.getAppId());
        return builder.build();
    }

    @Override
    public Result<Long> convertResult(Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache> result) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target().getLiveId());
    }

    @Override
    public Result<Long> convertResult(Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache> result, GetLatestLiveIdRemoteParam args) {
        int rCode = result.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmGetLastLiveIdAdapter,convertResult,rCode={},args={}", rCode, JsonUtil.dumps(args));
            if (rCode == LiveNewService.GET_UP_TO_DATE_LIVE_ID_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.GET_LAST_LIVE_ID_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.GET_LAST_LIVE_ID_ERROR, null);
        }
        return convertResult(result);
    }
}
