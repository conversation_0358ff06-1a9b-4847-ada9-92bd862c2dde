package fm.lizhi.ocean.wave.amusement.core.remote.service.xm;

import api.activity.api.LivePkService;
import api.activity.bean.pk.PkData;
import api.activity.bean.pk.PkLiveData;
import api.activity.constants.WaveLivePkStatusEnum;
import api.activity.param.PkInfoParam;
import api.activity.result.PkInfoResult;
import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.amusement.core.constant.*;
import fm.lizhi.ocean.wave.amusement.core.model.vo.VocalRoomStageSeat;
import fm.lizhi.ocean.wave.amusement.core.remote.bean.*;
import fm.lizhi.ocean.wave.amusement.core.remote.param.*;
import fm.lizhi.ocean.wave.amusement.core.remote.result.*;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IAmusementServiceRemote;
import fm.lizhi.ocean.wave.api.amusement.bean.xm.XmAmusementExtra;
import fm.lizhi.ocean.wave.api.amusement.constants.ApplyModeEnum;
import fm.lizhi.ocean.wave.api.amusement.constants.UserPlayStatusEnum;
import fm.lizhi.ocean.wave.api.amusement.constants.xm.XmFunModeType;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.amusement.pp.api.AmusementUserService;
import xm.fm.lizhi.live.amusement.pp.api.PPNewLiveAmusementService;
import xm.fm.lizhi.live.amusement.pp.api.PpLiveAmusementService;
import xm.fm.lizhi.live.amusement.pp.dto.ReqGetAllSeatsInfoDto;
import xm.fm.lizhi.live.amusement.pp.dto.RespGetAllSeatsInfoDto;
import xm.fm.lizhi.live.amusement.pp.dto.vocalroom.VocalRoomStageSeatDTO;
import xm.fm.lizhi.live.amusement.pp.protocol.AmusementUserProto;
import xm.fm.lizhi.live.amusement.pp.protocol.LiveAmusementProto;
import xm.fm.lizhi.live.amusement.services.VocalRoomSpringService;
import xm.fm.lizhi.live.data.api.PpUserPlayStateService;
import xm.fm.lizhi.live.data.protocol.UserPlayStateProto;
import xm.fm.lizhi.live.operation.pp.api.OperationMessageService;
import xm.fm.lizhi.live.operation.pp.protocol.OperationMessageProto;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/11 18:14
 */

@Slf4j
@Component
public class XmAmusementServiceRemote extends RemoteServiceInvokeFacade implements IAmusementServiceRemote {

    @Autowired
    private PPNewLiveAmusementService newLiveAmusementService;
    @Autowired
    private LiveService liveService;
    @Autowired
    private OperationMessageService operationMessageService;
    @Autowired
    private PpLiveAmusementService ppLiveAmusementService;
    @Autowired
    private VocalRoomSpringService vocalRoomSpringService;

    @Autowired
    private PpUserPlayStateService ppUserPlayStateService;

    @Autowired
    private LivePkService livePkService;

    @Autowired
    private AmusementUserService amusementUserService;


    private Map<Integer, String> MANAGEGUEST_ERRORCODEMAP = new HashMap<>();
    private Map<Integer, String> GUESTOPERATION_RESPONSECODES = new HashMap<>();

    @PostConstruct
    public void intiAttr() {
        this.MANAGEGUEST_ERRORCODEMAP.putAll(AmusementConstant.MANAGEGUEST_ERRORCODEMAP);
        this.MANAGEGUEST_ERRORCODEMAP.put(6, "用户没有申请排麦");
        this.GUESTOPERATION_RESPONSECODES.putAll(AmusementConstant.GUESTOPERATION_RESPONSECODES);
    }

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }



    @Override
    public Result<WaitingUsersResult> getWaitingUsers(WaitingUsersParams params) {
        Result<LiveAmusementProto.ResponseWaitingUsers> result = newLiveAmusementService.waitingUsers(params.getLiveId(), 0);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmWaitingUserAdapter,convertResult,rCode={},liveId={}", result.rCode(), params.getLiveId());
            return new Result<>(IAmusementServiceRemote.GET_WAITING_USERS_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, WaitingUsersResult.builder()
                .lastModifiedTime(result.target().getLastModifyTime())
                .waitingUserIds(result.target().getUserIdsList())
                .build());
    }

    @Override
    public Result<ResponseGetAmusementInfoResult> liveAmusementInfo(RequestGetAmusementInfoParam param) {
        long liveId = param.getLiveId();

        // @what：查询娱乐模式信息 @why：
        Result<LiveAmusementProto.ResponseLiveAmusementInfo> liveInfoRes = newLiveAmusementService.liveAmusementInfo(liveId, Long.MIN_VALUE);
        if (liveInfoRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmAmusementServiceRemote,liveAmusementInfo,rCode={},liveId={}", liveInfoRes.rCode(), param.getLiveId());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_INFO_ERROR, null);
        }

        // @what：判断是否开启了娱乐模式 @why：
        LiveAmusementProto.LiveFunSwitch liveFunSwitch = liveInfoRes.target().getLiveFunSwitch();
        if (liveFunSwitch.getFunMode() == PPNewLiveAmusementService.LIVE_AMUSEMENT_MODE_CLOSE) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ResponseGetAmusementInfoResult.builder()
                    .amusementBaseInfoBean(AmusementBaseInfoBean.builder().amusementSwitch(false).build())
                    .build());
        }

        // 校验直播信息是否存在
        Result<GetLiveResult> liveRes = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        if (liveRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm get live info error, liveId: {}, rCode: {}", liveId, liveRes.rCode());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_INFO_ERROR, null);
        }

        // 娱乐基本信息
        XmFunModeType xmFunModeType = XmFunModeType.NORMAL;
        if (liveInfoRes.target().getLiveFunLikeMoment().getLikeMomentState() == 1) {
            xmFunModeType = XmFunModeType.LIKE_MOMENT;
        } else if (liveInfoRes.target().getLiveFunTeamWar().getState() == 1) {
            xmFunModeType = XmFunModeType.LIVE_FUN_TEAM;
        }
        AmusementBaseInfoBean.AmusementBaseInfoBeanBuilder baseInfoBuilder = AmusementBaseInfoBean.builder()
                .amusementSwitch(true)
                .voiceChannelId(liveFunSwitch.getChannelId())
                .njVoiceUserId(liveFunSwitch.getUniqueId())
                .appKey(liveFunSwitch.getAppKey())
                .applyMode(ApplyModeEnum.APPLY.getValue())
                .lastModifyTime(liveInfoRes.target().getLastModifyTime())
                .extraJson(JsonUtil.dumps(new XmAmusementExtra()
                        .setXmFunModeType(xmFunModeType.getValue())
                ));

        // 麦位信息
        List<LiveAmusementProto.LiveFunSeat> liveFunSeatsList = liveInfoRes.target().getLiveFunSeatsList();
        List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans = buildAmusementSeat(liveFunSeatsList);

        ResponseGetAmusementInfoResult.ResponseGetAmusementInfoResultBuilder resultBuilder = ResponseGetAmusementInfoResult.builder()
                .amusementBaseInfoBean(baseInfoBuilder.build())
                .amusementSeatBaseInfoBeans(amusementSeatBaseInfoBeans);
        try {
            // 舞台位信息
            Result<VocalRoomStageSeatDTO> stageSeatRes = vocalRoomSpringService.getStageSeat(liveId);
            if (stageSeatRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("xm.amusementServiceRemote,getStageSeat,liveId={},rCode={}", liveId, stageSeatRes.rCode());
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resultBuilder.build());
            }
            VocalRoomStageSeatDTO stageSeatDTO = stageSeatRes.target();
            VocalRoomStageSeat vr = new VocalRoomStageSeat();
            if (stageSeatDTO.getUserId() != 0) {
                vr.setUserId(String.valueOf(stageSeatDTO.getUserId()));
            }
            VRStageSeatStatusEnum vrStageSeatStatusEnum = VRStageSeatStatusEnum.bizValue2Wave(stageSeatDTO.getRole());
            if (vrStageSeatStatusEnum != null) {
                vr.setStatus(vrStageSeatStatusEnum.getValue());
            }
            resultBuilder.vocalRoomStageSeat(vr);

            // 获取团战信息
            TeamWarBean.TeamWarBeanBuilder teamWarBuilder = TeamWarBean.builder();
            fillTeamWarInfo(liveInfoRes.target(), teamWarBuilder);
            resultBuilder.teamWar(teamWarBuilder.build());

            // 获取Pk麦位信息
            LivePkSimpleInfoBean pkSimpleInfo = buildPkSimpleInfo(liveInfoRes.target().getLiveFunLivePk(), param);
            resultBuilder.pkSimpleInfo(pkSimpleInfo);
        }catch (Exception e){
            log.error("XmAmusementServiceRemote liveAmusementInfo error. liveId: {}", liveId, e);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resultBuilder.build());
    }

    private LivePkSimpleInfoBean buildPkSimpleInfo(LiveAmusementProto.LiveFunLivePk liveFunLivePk, RequestGetAmusementInfoParam param) {
        LivePkSimpleInfoBean simpleInfoBean = new LivePkSimpleInfoBean();
        if (liveFunLivePk == null || param.getUserId() <= 0){
            return null;
        }
        long matchId = liveFunLivePk.getMatchId();
        // 获取Pk麦位信息
        // liveFunLivePk.getMatchId()可能为0->当处于随机匹配中的时候
        Result<PkInfoResult> result = livePkService.pkInfo(ContextUtils.getContext().getUserId(), PkInfoParam.builder()
                .liveId(param.getLiveId())
                .matchId(matchId)
                .build());

        if (RpcResult.isFail(result)){
            return null;
        }

        PkData pkInfo = result.target().getPkData();
        PkLiveData targetPkLiveData = pkInfo == null ? null : pkInfo.getTargetPkLiveData();
        return simpleInfoBean
                .setMatchId(String.valueOf(matchId))
                .setTargetSeatList(buildAmusementSeat(liveFunLivePk.getTargetSeatsList()))
                .setStatus(pkInfo == null ? WaveLivePkStatusEnum.PK_NO_OPEN_STATUS.getStatus() : pkInfo.getStatus())
                .setLiveId(Optional.ofNullable(targetPkLiveData).map(target -> target.getLive() == null ? null : Long.parseLong(target.getLive().getId())).orElse(null));
    }



    /**
     * 构建麦位信息
     * @param liveFunSeatsList
     * @return
     */
    private static List<AmusementSeatBaseInfoBean> buildAmusementSeat(List<LiveAmusementProto.LiveFunSeat> liveFunSeatsList) {

        if (CollUtil.isEmpty(liveFunSeatsList)){
            return new ArrayList<>();
        }

        List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans = new ArrayList<>(liveFunSeatsList.size());
        for (LiveAmusementProto.LiveFunSeat liveFunSeat : liveFunSeatsList) {
            amusementSeatBaseInfoBeans.add(AmusementSeatBaseInfoBean.builder()
                    .seatNo(liveFunSeat.getSeat())
                    .seatStatus(liveFunSeat.getState())
                    .userId(liveFunSeat.getUserId())
                    .userState(liveFunSeat.getUserPlayState())
                    .speakState(liveFunSeat.getSpeakState() == 1)
                    .voiceUserId(liveFunSeat.getUniqueId())
                    .materialUrl(liveFunSeat.hasMaterialUrl() ? liveFunSeat.getMaterialUrl() : null)
                    .charm(new Double(liveFunSeat.getLiveFunGuestCharm()).intValue())
                    .teamWarMvp(liveFunSeat.getTeamWarMvp())
                    .build());
        }
        return amusementSeatBaseInfoBeans;
    }

    private void fillTeamWarInfo(LiveAmusementProto.ResponseLiveAmusementInfo target, TeamWarBean.TeamWarBeanBuilder teamWarBuilder) {
        LiveAmusementProto.LiveFunTeamWar liveFunTeamWar = target.getLiveFunTeamWar();
        teamWarBuilder.state(TeamWarStateEnum.bizValue2Wave(liveFunTeamWar.getState()).getValue())
                .remainingTime((int) liveFunTeamWar.getRemainingTime())
                .startTime(String.valueOf(liveFunTeamWar.getStartTime()))
                .teamWarTheme(liveFunTeamWar.getTeamWarTheme());

        LiveAmusementProto.LiveFunTeamWarTeamInfo aTeamInfo = liveFunTeamWar.getATeamInfo();
        TeamInfoBean.TeamInfoBeanBuilder aTeamBuilder = TeamInfoBean.builder();
        aTeamBuilder.teamLevel(aTeamInfo.getTeamLevel())
                .charmValue(aTeamInfo.getCharmValue())
                .nextFullCharm(aTeamInfo.getNextFullCharm())
                .currentBaseCharm(aTeamInfo.getCurrentBaseCharm());
        teamWarBuilder.aTeamInfo(aTeamBuilder.build());

        LiveAmusementProto.LiveFunTeamWarTeamInfo bTeamInfo = liveFunTeamWar.getBTeamInfo();
        TeamInfoBean.TeamInfoBeanBuilder bTeamBuilder = TeamInfoBean.builder();
        bTeamBuilder.teamLevel(bTeamInfo.getTeamLevel())
                .charmValue(bTeamInfo.getCharmValue())
                .nextFullCharm(bTeamInfo.getNextFullCharm())
                .currentBaseCharm(bTeamInfo.getCurrentBaseCharm());
        teamWarBuilder.bTeamInfo(bTeamBuilder.build());
    }

    @Override
    public Result<ResponseGetApplyModeResult> getApplyMode(RequestGetApplyModeParam param) {
        return null;
    }

    @Override
    public Result<Void> manageSeat(RequestManageSeatParam param) {
        long liveId = param.getLiveId();
        int operation = param.getOperation();
        int seat = param.getSeat();

        Result<Void> resp = newLiveAmusementService.manageSeat(liveId, operation, seat);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm manageSeat error, liveId: {}, operation: {}, seat: {}, rCode: {}", liveId, operation, seat, resp.rCode());
            return new Result<>(IAmusementServiceRemote.MANAGE_SEAT_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GetUserSeatResult> userLiveSeat(RequestGetUserSeatParam param) {
        long liveId = param.getLiveId();
        long userId = param.getUserId();

        Result<OperationMessageProto.ResponseUserLiveSeat> resp = operationMessageService.userLiveSeat(liveId, userId);

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm user live seat error, liveId: {}, userId: {}, rCode: {}", liveId, userId, resp.rCode());
            return new Result<>(IAmusementServiceRemote.GET_USER_SEAT_ERROR, null);
        }

        int seat = resp.target().getSeat();
        GetUserSeatResult getUserSeatResult = new GetUserSeatResult();
        getUserSeatResult.setSeat(seat);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, getUserSeatResult);
    }

    @Override
    public Result<Integer> manageGuest(RequestManageGuestParam param) {
        Result<LiveAmusementProto.ResponseManageGuest> resp = newLiveAmusementService.manageGuest(param.getLiveId()
                , param.getOperation()
                , param.getGuestUserId()
                , param.getOperateUserId());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm manage guest error, liveId: {}, operation: {}, guestUserId: {}, operateUserId: {}, rCode: {}",
                    param.getLiveId(), param.getOperation(), param.getGuestUserId(), param.getOperateUserId(), resp.rCode());

            String msg = MANAGEGUEST_ERRORCODEMAP.getOrDefault(resp.rCode(), "内部异常，请联系管理员");
            Result<Integer> result = new Result<>(IAmusementServiceRemote.MANAGE_GUEST_ERROR, null);
            result.setMessage(msg);
            return result;
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> guestOperation(RequestGuestOperationParam param) {
        long liveId = param.getLiveId();
        long guestUserId = param.getGuestUserId();
        WaveGuestOperationTypeEnum operation = param.getOperationEnum();
        int terminalType = param.getTerminalType();
        int waveOperationType = operation.getType();

        // @what：判断操作类型是否符合 @why：业务之间有差异
        if (operation == WaveGuestOperationTypeEnum.ON_SPEAKING
                || operation == WaveGuestOperationTypeEnum.SEAT_REPLACE
                || operation == WaveGuestOperationTypeEnum.ACCEPT_HOLD_MICROPHONE
                || operation == WaveGuestOperationTypeEnum.REFUSE_HOLD_MICROPHONE
        ) {
            log.info("【xm】不支持该类型的嘉宾操作！operation={}", operation);
            Result<Void> res = new Result<>();
            res.setRCode(IAmusementServiceRemote.GUEST_OPERATION_NO_SUPPORT);
            res.setMessage(GUESTOPERATION_RESPONSECODES.get(IAmusementServiceRemote.GUEST_OPERATION_NO_SUPPORT));
            return res;
        }

        int bizOperationType = GuestOperationTypeMapping.getBizOperationType(waveOperationType);
        Result<Void> resp = newLiveAmusementService.guestOperation(liveId, guestUserId, bizOperationType, terminalType);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm guest operation error1, liveId: {}, guestUserId: {}, operation: {} , rCode: {}",
                    liveId, guestUserId, operation, resp.rCode());
            String msg = GUESTOPERATION_RESPONSECODES.getOrDefault(resp.rCode(), "内部异常，请联系管理员");
            Result<Void> res = new Result<>();
            res.setRCode(IAmusementServiceRemote.GUEST_OPERATION_ERROR);
            res.setMessage(msg);
            return res;
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> changeApplyMode(RequestChangeApplyModeParam param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> removeUserAmusementInfo(RequestRemoveAmuseInfoParam removeAmusementInfoParam) {
        Result<Void> resp = newLiveAmusementService.removeUserAmusementInfo(removeAmusementInfoParam.getLiveId()
                , removeAmusementInfoParam.getUserId());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm remove user amusement info error, liveId: {}, userId: {}, rCode: {}",
                    removeAmusementInfoParam.getLiveId(), removeAmusementInfoParam.getUserId(), resp.rCode());
            return new Result<>(IAmusementServiceRemote.REMOVE_USER_AMUSEMENT_INFO_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> liveAmusementModeSwitch(LiveAmusementModeSwitchRequestParam param) {
        Result<Void> resp = newLiveAmusementService.liveAmusementModeSwitch(param.getLiveId()
                , param.getNjId()
                , param.getLiveRoomId()
                , param.getOperation()
                , param.getFunModeType());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm liveAmusementModeSwitch error modeSwitch param={},rCode={}", JsonUtil.dumps(param), resp.rCode());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_MODE_SWITCH_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> clearCharm(RequestClearCharmParams param) {
        Result<Void> clearResp = newLiveAmusementService.clearCharm(param.getLiveId());
        if (clearResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm clear charm value error, liveId: {}, rCode: {}", param.getLiveId(), clearResp.rCode());
            return new Result<>(clearResp.rCode(), null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> inviteOnCall(RequestInviteOnCallParam data) {
        // @what：西米不支持邀请上麦 @why：
        return new Result<>(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, null);
    }

    @Override
    public Result<Void> insertLitchiRank(RequestInsertLitchiRankParam rankParam) {
        Result<Void> insertRankResp = newLiveAmusementService.insertLitchiRank(rankParam.getLiveId());
        if (insertRankResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm insert litchi rank not success, liveId: {}, rCode: {}", rankParam.getLiveId(), insertRankResp.rCode());
            return new Result<>(IAmusementServiceRemote.INSERT_LITCHI_RANK_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> incrUserKey(long userId, String key, int value) {
        return RpcResult.fail(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND);
    }

    @Override
    public Result<Void> syncUserVerifyNo(long userId, String orderNo) {
        return RpcResult.fail(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND);
    }

    @Override
    public Result<ResponseGetAllSeatsInfo> getAllSeatsInfo(long liveId) {
        ReqGetAllSeatsInfoDto param = new ReqGetAllSeatsInfoDto();
        param.setLiveId(liveId);
        Result<AmusementUserProto.ResponseGetAllSeatsInfo> result = amusementUserService.getAllSeatsInfo(JsonUtils.toJsonString(param));
        if (RpcResult.isFail(result)) {
            return new Result<>(IAmusementServiceRemote.GET_ALL_SEATS_INFO_FAIL, null);
        }
        String json = result.target().getRespGetAllSeatsInfoDto();
        RespGetAllSeatsInfoDto seatsInfoDto = JsonUtils.fromJsonString(json, RespGetAllSeatsInfoDto.class);
        if (seatsInfoDto == null || seatsInfoDto.getSeatList() == null) {
            return new Result<>(IAmusementServiceRemote.GET_ALL_SEATS_INFO_FAIL, null);
        }
        List<SeatInfoBean> seatRes = seatsInfoDto.getSeatList().stream().map(info -> {
            return new SeatInfoBean().setSeatNo(info.getSeat()).setUserId(info.getUserId());
        }).collect(Collectors.toList());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, new ResponseGetAllSeatsInfo().setSeatInfoList(seatRes));
    }

    @Override
    public Result<Void> reportOnMicData(ReportOnMicDataRequest request) {
        return null;
    }

    @Override
    public Result<UserPlayStatusEnum> getUserPlaySate(long userId) {
        Result<UserPlayStateProto.ResponseGetUserPlayState> result = ppUserPlayStateService.getUserPlayState(userId);
        if (RpcResult.isFail(result)) {
            log.warn("xm.getUserPlayState not success, userId={}`rCode={}", userId, result.rCode());
            return new Result<>(IAmusementServiceRemote.GET_USER_PLAY_STATE_ERROR, null);
        }

        UserPlayStateProto.UserPlayState userPlayState = result.target().getUserPlayState();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, UserPlayStatusEnum.getUserPayState(userPlayState.getNumber()));
    }
}
