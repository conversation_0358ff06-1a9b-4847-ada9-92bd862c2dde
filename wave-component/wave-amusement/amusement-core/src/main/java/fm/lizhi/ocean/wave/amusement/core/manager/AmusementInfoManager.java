package fm.lizhi.ocean.wave.amusement.core.manager;

import api.activity.constants.WaveLivePkStatusEnum;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.hash.Hashing;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.amusement.core.constant.AmusementCodeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.AmusementMsgCodes;
import fm.lizhi.ocean.wave.amusement.core.constant.TeamWarSeatEnum;
import fm.lizhi.ocean.wave.amusement.core.dao.redis.AmusementRedisDao;
import fm.lizhi.ocean.wave.amusement.core.facade.AmusementLiveServiceFacade;
import fm.lizhi.ocean.wave.amusement.core.model.result.WaitingInfo;
import fm.lizhi.ocean.wave.amusement.core.model.result.WaitingUser;
import fm.lizhi.ocean.wave.amusement.core.model.vo.*;
import fm.lizhi.ocean.wave.amusement.core.remote.bean.*;
import fm.lizhi.ocean.wave.amusement.core.remote.param.ReportOnMicDataRequest;
import fm.lizhi.ocean.wave.amusement.core.remote.param.RequestGetAmusementInfoParam;
import fm.lizhi.ocean.wave.amusement.core.remote.param.WaitingUsersParams;
import fm.lizhi.ocean.wave.amusement.core.remote.result.ResponseGetAmusementInfoResult;
import fm.lizhi.ocean.wave.amusement.core.remote.result.WaitingUsersResult;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IAmusementServiceRemote;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IOperationMessageServiceRemote;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.bean.Live;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.constants.LiveStatusEnum;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/17
 */
@Component
@Slf4j
public class AmusementInfoManager {


    @Autowired
    private AmusementLiveServiceFacade amusementLiveServiceFacade;

    @MyAutowired
    private IAmusementServiceRemote amusementServiceRemote;
    @MyAutowired
    private IOperationMessageServiceRemote iOperationMessageServiceRemote;
    @Autowired
    private UserService userService;

    @Autowired
    private AmusementConfig amusementConfig;

    @Autowired
    private LiveService liveService;

    @Autowired
    private AmusementRedisDao amusementRedisDao;

    /**
     * 线程池
     */
    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors(
            "FansNotifyService", 5, 10, new LinkedBlockingQueue<Runnable>(1000));

    public ResultVO<AmusementInfoVo> pollAmusementInfo(long liveId, String performanceId) {
        Result<GetLiveResult> result = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());

        if (RpcResult.noBusinessData(result)) {
            log.info("获取娱乐模式信息|pollAmusementInfo live is null liveId:{}", liveId);
            return ResultVO.failure();
        }

        Live live = result.target().getLive();
        if (LiveStatusEnum.from(live.getStatus()) != LiveStatusEnum.ON_AIR) {
            log.info("获取娱乐模式信息|pollAmusementInfo live status is not on air liveId:{}", liveId);
            return ResultVO.success();
        }

        Result<ResponseGetAmusementInfoResult> resp =
                amusementServiceRemote.liveAmusementInfo(RequestGetAmusementInfoParam.builder()
                        .liveId(liveId).njId(live.getUserId())
                        .userId(ContextUtils.getContext().getUserId())
                        .build());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("获取娱乐模式信息|pollAmusementInfo liveAmusementInfo fail liveId:{} rCode:{} rMsg:{}", liveId, resp.rCode(), resp.getMessage());
            return ResultVO.failure();
        }

        // 基本信息
        AmusementBaseInfoBean amusementBaseInfoBean = resp.target().getAmusementBaseInfoBean();
        if (!amusementBaseInfoBean.isAmusementSwitch()) {
            log.info("获取娱乐模式信息|pollAmusementInfo amusementSwitch is false liveId:{}", liveId);
            return ResultVO.success();
        }

        // 麦位信息
        List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans = resp.target().getAmusementSeatBaseInfoBeans();
        ResultVO<List<AmusementSeatVo>> buildAmusementSeatVos = buildAmusementSeatVos(liveId, amusementSeatBaseInfoBeans);
        if (!buildAmusementSeatVos.isOK()) {
            return ResultVO.warn(AmusementMsgCodes.USER_QUERY_FAIL);
        }
        List<AmusementSeatVo> seatList = buildAmusementSeatVos.getData();

        // 获取liveMode
        LiveModeEnum liveModeEnum = amusementLiveServiceFacade.getLiveMode(liveId);

        // 结果封装
        AmusementInfoVo amusementInfoVo = AmusementInfoVo.builder()
                .applyMode(amusementBaseInfoBean.getApplyMode())
                .seatList(seatList)
                .appKey(amusementBaseInfoBean.getAppKey())
                .voiceChannelId(amusementBaseInfoBean.getVoiceChannelId())
                .njVoiceUserId(amusementBaseInfoBean.getNjVoiceUserId())
                .liveMode(liveModeEnum.getLiveMode())
                .roomType(liveModeEnum.getRoomType())
                .lastModifyTime(amusementBaseInfoBean.getLastModifyTime())
                .vocalRoomStageSeat(resp.target().getVocalRoomStageSeat())
                .build();

        // 团战信息
        TeamWarBean teamWar = resp.target().getTeamWar();
        if (teamWar != null) {
            TeamWarVo teamWarVo = new TeamWarVo()
                    .setState(teamWar.getState())
                    .setTeamWarTheme(teamWar.getTeamWarTheme())
                    .setStartTime(teamWar.getStartTime())
                    .setRemainingTime(teamWar.getRemainingTime());
            TeamInfoBean aTeamInfo = teamWar.getATeamInfo();
            if (aTeamInfo != null) {
                teamWarVo.setAteamInfo(new TeamInfoVo()
                        .setTeamLevel(aTeamInfo.getTeamLevel())
                        .setSeatNums(TeamWarSeatEnum.getATeamSeat(liveModeEnum))
                        .setCharmValue(aTeamInfo.getCharmValue())
                        .setNextFullCharm(aTeamInfo.getNextFullCharm())
                        .setCurrentBaseCharm(aTeamInfo.getCurrentBaseCharm())
                );
            }
            TeamInfoBean bTeamInfo = teamWar.getBTeamInfo();
            if (bTeamInfo != null) {
                teamWarVo.setBteamInfo(new TeamInfoVo()
                        .setTeamLevel(bTeamInfo.getTeamLevel())
                        .setSeatNums(TeamWarSeatEnum.getBTeamSeat(liveModeEnum))
                        .setCharmValue(bTeamInfo.getCharmValue())
                        .setNextFullCharm(bTeamInfo.getNextFullCharm())
                        .setCurrentBaseCharm(bTeamInfo.getCurrentBaseCharm())
                );
            }
            amusementInfoVo.setTeamWar(teamWarVo);
        }else {
            amusementInfoVo.setTeamWar(TeamWarVo.buildDefault(liveModeEnum));
        }

        // PK麦位信息
        LivePkSimpleInfoBean pkSimpleInfo = resp.target().getPkSimpleInfo();
        if (null != pkSimpleInfo){
            ResultVO<List<AmusementSeatVo>> buildPkSeatResult = buildAmusementSeatVos(pkSimpleInfo.getLiveId(), pkSimpleInfo.getTargetSeatList());
            if (!buildPkSeatResult.isOK()) {
                return ResultVO.warn(AmusementMsgCodes.USER_QUERY_FAIL);
            }
            amusementInfoVo.setPkSimpleInfo(new LivePkSimpleInfoVo()
                    .setStatus(pkSimpleInfo.getStatus())
                    .setMatchId(pkSimpleInfo.getMatchId())
                    .setTargetSeatList(buildPkSeatResult.getData())
            );
        } else {
            amusementInfoVo.setPkSimpleInfo(new LivePkSimpleInfoVo().setStatus(WaveLivePkStatusEnum.PK_NO_OPEN_STATUS.getStatus()));
        }

        if (amusementConfig.isShowSeatInfo()) {
            log.info("amusementInfo detail:{}", JsonUtil.dumps(amusementInfoVo));
        }

        if (StringUtils.isBlank(performanceId)) {
            return ResultVO.success(amusementInfoVo);
        }

        String amusementInfoJson = JsonUtil.dumps(amusementInfoVo);
        String newMd5 = Hashing.md5().hashBytes(amusementInfoJson.getBytes()).toString();

        if (performanceId.equals(newMd5)) {
            return ResultVO.success();
        }
        amusementInfoVo.setPerformanceId(newMd5);
        return ResultVO.success(amusementInfoVo);
    }

    private ResultVO<List<AmusementSeatVo>> buildAmusementSeatVos(Long liveId, List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans) {

        if (liveId == null || CollUtil.isEmpty(amusementSeatBaseInfoBeans)){
            return ResultVO.success(new ArrayList<>());
        }

        List<AmusementSeatVo> seatList = new ArrayList<>(amusementSeatBaseInfoBeans.size());
        Map<Long, UserAvatarWidget> userAvatarWidgetMap = userService.batchGetUserAvatarWidget(amusementSeatBaseInfoBeans.stream().map(AmusementSeatBaseInfoBean::getUserId).collect(Collectors.toList())).target();
        Map<String, String> seatUserMap = new HashMap<>();
        for (AmusementSeatBaseInfoBean amusementSeatBaseInfoBean : amusementSeatBaseInfoBeans) {
            long userId = amusementSeatBaseInfoBean.getUserId();
            AmusementSeatVo.UserVo userVo = null;
            if (userId != 0) {
                //校验用户
                Result<GetSimpleUserResult> userRes = userService.getSimpleUserByCache(userId);
                if (userRes.rCode() != 0 || userRes.target().getSimpleUser() == null) {
                    log.info("pollAmusementInfo,用户不存在,liveId:{},userId:{}", liveId, userId);
                    return ResultVO.warn(AmusementMsgCodes.USER_QUERY_FAIL);
                }
                SimpleUser user = userRes.target().getSimpleUser();
//                渲染用户信息
                userVo = new AmusementSeatVo.UserVo();
                UserAvatarWidget userAvatarWidget = userAvatarWidgetMap.get(userId);
                if(userAvatarWidget != null) {
                    userVo.setUserAvatarWidget(userAvatarWidget);
                }
                userVo.setId(user.getUserId());
                userVo.setAvatar(user.getAvatar());
                userVo.setNickName(user.getNickName());
                userVo.setVoiceUserId(amusementSeatBaseInfoBean.getVoiceUserId());

                // 只处理自己，上报上麦时长
                if (userId == ContextUtils.getContext().getUserId()) {
                    asyncReportOnMicData(liveId, userId);
                }
                // 构建麦位用户映射
                seatUserMap.put(String.valueOf(amusementSeatBaseInfoBean.getSeatNo()), String.valueOf(amusementSeatBaseInfoBean.getUserId()));
            }

            seatList.add(AmusementSeatVo.builder()
                    .materialUrl(amusementSeatBaseInfoBean.getMaterialUrl())
                    .seatNo(amusementSeatBaseInfoBean.getSeatNo())
                    .seatStatus(amusementSeatBaseInfoBean.getSeatStatus())
                    .speakState(amusementSeatBaseInfoBean.isSpeakState())
                    .charm(amusementSeatBaseInfoBean.getCharm())
                    .coinNum(amusementSeatBaseInfoBean.getCoinNum())
                    .audioSource(amusementSeatBaseInfoBean.getAudioSource())
                    .user(userVo)
                    .userState(amusementSeatBaseInfoBean.getUserState())
                    .teamWarMvp(amusementSeatBaseInfoBean.isTeamWarMvp())
                    .build()
            );
        }

        // 批量存储麦位用户映射到Redis
        try {
            if (CollUtil.isNotEmpty(seatUserMap)) {
                amusementRedisDao.batchSetMicUserByLive(liveId, seatUserMap);
            }
        } catch (Exception e) {
            log.error("批量存储麦位用户映射到Redis失败, liveId:{}, error:{}", liveId, e.getMessage(), e);
        }

        return ResultVO.success(seatList);
    }


    /**
     * 获取排麦用户列表
     *
     * @param liveId
     * @return
     */
    public ResultVO<WaitingInfo> getWaitingUserList(long liveId) {


        Live live = amusementLiveServiceFacade.getLive(liveId);
        if (live == null) {
            log.info("获取排麦用户列表|getWaitingUserList|liveId:{}|live is null", liveId);
            return ResultVO.failure(AmusementCodeEnum.LIVE_NOT_EXIST);
        }

        if (live.getStatus() != LiveStatusEnum.ON_AIR.getValue()) {
            log.info("获取排麦用户列表|getWaitingUserList|liveId:{}|live is not on air", liveId);
            return ResultVO.success();
        }

        Result<WaitingUsersResult> waitingUsersResult = amusementServiceRemote.getWaitingUsers(WaitingUsersParams.builder().liveId(liveId).build());

        if (waitingUsersResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("获取排麦用户列表|getWaitingUserList|liveId:{}| remote rCode :{}", liveId, waitingUsersResult.rCode());
            return ResultVO.failure(AmusementCodeEnum.GET_WAITING_USERS_FAIL);
        }

        List<Long> waitingUserIds = waitingUsersResult.target().getWaitingUserIds();
        if (CollectionUtils.isEmpty(waitingUserIds)) {
            return ResultVO.success();
        }

        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(waitingUserIds).build();
        Result<BatchGetSimpleUserResult> batchUserRes = userService.batchGetSimpleUserByCache(userParam);
        if (batchUserRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("获取排麦用户列表|getWaitingUserList|liveId:{}| batchGetSimpleUserByCache rCode :{}", liveId, batchUserRes.rCode());
            return ResultVO.failure(AmusementCodeEnum.GET_WAITING_USERS_FAIL);
        }

        List<WaitingUser> waitingUsers = new ArrayList<>();
        for (SimpleUser user : batchUserRes.target().getUserList()) {
            waitingUsers.add(WaitingUser.builder()
                    .userId(user.getUserId())
                    .avatar(user.getAvatar())
                    .nickName(user.getNickName())
                    .build());
        }

        return ResultVO.success(WaitingInfo.builder()
                .waitingUsers(waitingUsers)
                .build());
    }

    /**
     * 异步上报上麦数据
     *
     * @param liveId 直播间id
     * @param userId 用户uid
     */
    public void asyncReportOnMicData(long liveId, long userId) {
        THREAD_POOL_EXECUTOR.submit(RunnableWrapper.of(new Runnable() {
            @Override
            public void run() {
                try {
                    amusementServiceRemote.reportOnMicData(ReportOnMicDataRequest.builder().liveId(liveId).userId(userId).build());
                } catch (Exception e) {
                    log.error(String.format("asyncReportOnMicData error, liveId=%s`userId={}`e=%s", liveId, userId, e));
                }
            }
        }));
    }


}
