package fm.lizhi.ocean.wave.user.core.controller;

import com.google.common.base.Splitter;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementHeartbeatService;
import fm.lizhi.ocean.wave.api.live.api.LiveHeartbeatService;
import fm.lizhi.ocean.wave.api.live.param.ReportHeartbeatParam;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.constant.UserMsgCodes;
import fm.lizhi.ocean.wave.user.core.convert.UserInfoConvert;
import fm.lizhi.ocean.wave.user.core.manager.ActiveTokenManager;
import fm.lizhi.ocean.wave.user.core.manager.RelatedUserManager;
import fm.lizhi.ocean.wave.user.core.manager.UserManager;
import fm.lizhi.ocean.wave.user.core.manager.UserOnlineStatusManager;
import fm.lizhi.ocean.wave.user.core.model.param.*;
import fm.lizhi.ocean.wave.user.core.model.vo.*;
import fm.lizhi.ocean.wave.user.core.remote.result.GetUserOnlineStatusResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/5/16
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserManager userManager;

    @Autowired
    private RelatedUserManager relatedUserManager;

    @Autowired
    private UserOnlineStatusManager userOnlineStatusManager;

    @Autowired
    private UserProviderConfig userConfig;

    @Autowired
    private ActiveTokenManager activeTokenManager;

    @Autowired
    private LiveHeartbeatService liveHeartbeatService;

    @Autowired
    private AmusementHeartbeatService amusementHeartbeatService;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    /**
     * IM模块专用接口，获取当前用户与target用户的关联信息
     * 获取用户简单信息
     *
     * @param param
     * @return
     */
    @GetMapping("/simple")
    @VerifyUserToken
    public ResultVO<SimpleUserVO> getSimpleUser(@Validated SimpleUserParam param) {
        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        Long tgtUserId = param.getTgtUserId();
        return userManager.getSimpleUserCache(userId, tgtUserId);
    }

    /**
     * 获取用户详细信息
     *
     * @param param
     * @return
     */
    @GetMapping("/detail")
    @VerifyUserToken
    public ResultVO<UserDetailVO> getUserDetail(@Validated UserDetailParam param) {
        if (param.getTargetUserId() <= 0) {
            return ResultVO.success();
        }

        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        param.setUserId(userId);

        return ResultVO.success(userManager.getUserDetail(param));
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    @GetMapping("{userId}")
    public ResultVO<UserVo> getUserInfo(@PathVariable("userId") String userId) {
        if (StringUtils.isBlank(userId)) {
            return ResultVO.success();
        }
        return userManager.getUserVo(Long.parseLong(userId));
    }

    /**
     * 批量获取用户信息
     *
     * @return
     */
    @GetMapping("list")
    public ResultVO<List<UserVo>> getUserInfoList(@RequestParam("userIds") String userIds) {
        if (StringUtils.isBlank(userIds)) {
            return ResultVO.success(new ArrayList<>());
        }
        List<String> userIdList = Splitter.on(",").omitEmptyStrings().splitToList(userIds);
        List<Long> uidList = new ArrayList<>(userIdList.size());
        for (String userId : userIdList) {
            uidList.add(Long.parseLong(userId));
        }
        return userManager.batchGetUserVo(uidList);
    }

    /**
     * 搜索用户
     *
     * @param param
     * @return
     */
    @GetMapping("/search")
    @VerifyUserToken
    public ResultVO<List<SearchUserInfoVO>> searchUserInfo(@Validated UserInfoParam param) {
        ResultVO<SearchUserResultVO> result = userManager.searchUserInfo(param);
        if (result.isOK()) {
            return ResultVO.success(result.getData().getUserList());
        }
        return ResultVO.failure(result.getRCode(), result.getPrompt());
    }

    /**
     * 搜索用户, 可分页
     *
     * @param param
     * @return
     */
    @GetMapping("/searchUser")
    @VerifyUserToken
    public ResultVO<SearchUserResultVO> searchUserInfoV2(@Validated UserInfoParam param) {
        return userManager.searchUserInfo(param);
    }


    /**
     * 上报在线状态
     *
     * @return
     */
    @GetMapping("/heartbeat")
    @VerifyUserToken
    public ResultVO<Void> heartbeat(UserHeartbeatParam param) {
        //上报活跃的token
        long userId = ContextUtils.getContext().getUserId();
        activeTokenManager.addActiveTokenMark(ContextUtils.getBusinessEvnEnum().getAppId(), userId);
        userManager.reportHeartbeat(param.isFirst());

        try {
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            boolean isOldVersion = clientVersion < commonProviderConfig.getMinHeartbeatClientVersion();

            // 旧版本，没有参数，继续使用之前的轮询进行上报即可
            if (isOldVersion) {
                if (log.isDebugEnabled()) {
                    log.debug("reportLiveHeartbeat old version, ignore. param:{}", param);
                }
                return ResultVO.success();
            }

            // 上报事件
            liveHeartbeatService.asyncReportHeartbeat(new ReportHeartbeatParam()
                    .setFirst(param.isFirst())
                    .setLiveId(param.getLiveId())
                    .setTimestamp(param.getTimestamp())
                    .setUserStatus(param.getUserStatus())
                    .setUserId(userId)
            );

            Result<Void> result = amusementHeartbeatService.asyncReportOnMicData(param.getLiveId(), userId);
            if (RpcResult.isFail(result)) {
                log.warn("reportLiveHeartbeat fail, param:{}", param);
            }

        } catch (Exception e) {
            log.error("report heartbeat error: ", e);
        }

        return ResultVO.success();
    }

    /**
     * 关注/取关
     *
     * @param attentionParam
     * @return
     */
    @PostMapping("/attention")
    @VerifyUserToken
    public ResultVO<Void> attentionUser(@RequestBody @Validated AttentionUserParam attentionParam) {
        return userManager.attentionUser(attentionParam);
    }

    /**
     * 粉丝/关注列表
     *
     * @param param
     * @return
     */
    @GetMapping("/relatedUserList")
    @VerifyUserToken
    public ResultVO<RelatedUserListVO> getRelatedUserList(@Validated RelatedUserListParam param) {
        return relatedUserManager.getRelatedUserList(param);
    }

    /**
     * 关注信息
     *
     * @return
     */
    @PostMapping("/relationInfo")
    @VerifyUserToken
    public ResultVO<RelationInfoVo> relationInfo() {
        return relatedUserManager.relationInfo();
    }

    /**
     * 密友数据
     *
     * @param param
     * @return
     */
    @GetMapping("/closeFriendInfo")
    @VerifyUserToken
    public ResultVO<List<CloseFriendRelationVO>> closeFriendInfo(@Validated UserCloseFriendParam param) {
        return relatedUserManager.getCloseFriendInfo(param.getUserId(),
                ContextUtils.getContext().getUserId(),
                param.getPageNum(),
                param.getPageSize());
    }

    /**
     * 获取业务用户token
     *
     * @return
     */
    @GetMapping("/biz/token")
    @VerifyUserToken
    public ResultVO<BusinessTokenVo> getBusinessUserToken() {
        String token = userManager.getBusinessUserTokenByUserId(ContextUtils.getContext().getUserId());
        if (StringUtils.isBlank(token)) {
            return ResultVO.failure();
        }
        return ResultVO.success(BusinessTokenVo.builder().token(token).build());
    }

    /**
     * 获取用户关注的主播的直播卡片
     *
     * @param performanceId 用来分页的id
     * @return
     */
    @GetMapping("/followList/pgcRoom")
    @VerifyUserToken
    public ResultVO<FollowUserPGCLiveCardVo> getRelationPGCLiveCard(
            @RequestParam(value = "performanceId", defaultValue = "0") Long performanceId) {
        return relatedUserManager.getRelationPGCLiveCard(performanceId);
    }

    /**
     * @param inviteEnterParam - pageSize 每页请求参数
     *                         - performanceId 刷新分页关键字，缺省为空，服务端下发，客户端回传
     * @return 邀请进房列表
     */

    @VerifyUserToken
    @GetMapping("/getUserInviteList")
    public ResultVO<InviteUserInfoVO> getUserInviteList(InviteEnterParam inviteEnterParam) {
        if (inviteEnterParam.getPerformanceId() == null) {
            inviteEnterParam.setPerformanceId(0L);
        }
        return userManager.getUserInviteList(inviteEnterParam.getPageSize(), inviteEnterParam.getPerformanceId());
    }

    /**
     * 获取用户签约关系
     *
     * @param njId njId
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getUserSignRelation")
    public ResultVO<UserSignRelationVO> getUserSignRelation(@RequestParam(value = "njId", defaultValue = "0") Long njId) {
        if (njId <= 0) {
            return ResultVO.failure(UserMsgCodes.USER_SIGN_RELATION_FAIL.getCode(), UserMsgCodes.USER_SIGN_RELATION_FAIL.getMsg());
        }
        return userManager.getUserSignRelation(ContextUtils.getContext().getUserId(), njId);
    }

    /**
     * 查询资料详情页的动态tab列表
     *
     * @param targetUserId
     * @return
     */
    @VerifyUserToken
    @GetMapping("/tabList")
    public ResultVO<List<String>> tabList(@RequestParam("targetUserId") long targetUserId) {
        return ResultVO.success(userManager.tabList(targetUserId));
    }

    /**
     * 批量获取用户在线状态
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/batchGetUserStatus")
    public ResultVO<List<GetUserOnlineStatusVO>> batchGetOnlineStatus(@Validated BatchGetUserStatusParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getUserIds()) || param.getUserIds().size() > userConfig.getBatchGetUserMaxSize()) {
            //参数校验
            return ResultVO.failure(UserMsgCodes.BATCH_GET_ONLINE_STATUS_PARAM_ERROR.getCode(), UserMsgCodes.BATCH_GET_ONLINE_STATUS_PARAM_ERROR.getMsg());
        }

        Result<List<GetUserOnlineStatusResult>> result = userOnlineStatusManager.batchGetUserOnlineStatus(param.getUserIds());
        if (RpcResult.noBusinessData(result)) {
            //失败了
            return ResultVO.failure(UserMsgCodes.BATCH_GET_ONLINE_STATUS_FAIL.getCode(), UserMsgCodes.BATCH_GET_ONLINE_STATUS_FAIL.getMsg());
        }

        //结果转换
        List<GetUserOnlineStatusVO> onlineStatusVOS = UserInfoConvert.I.statusResult2Vos(result.target());
        return ResultVO.success(onlineStatusVOS);
    }


    /**
     * 用户举报
     *
     * @return
     */
    @VerifyUserToken
    @PostMapping("/report")
    public ResultVO<Void> userReport(@Validated @RequestBody UserReportParam param) {
        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        return userManager.userReport(userId, param);
    }

    @GetMapping("/getUserBaseInfoByBand")
    @VerifyUserToken
    public ResultVO<BaseUserInfoVO> getUserBaseInfoByBand(String band) {
        return userManager.getUserBaseInfoByBand(band);
    }
}
