package fm.lizhi.ocean.wave.generic.pojo.bean;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21 16:14
 */
@Data
public class GenericParam {
    private String serviceName;
    private String methodName;
    private List<ParamInfo> paramInfos;

    @Getter
    @Builder
    public static class ParamInfo{
        private String paramName;
        private String valueStr;

        public static class ParamInfoBuilder{
            public ParamInfo build() {
                Assert.hasText(paramName, "paramName is null");
                return new ParamInfo(paramName, valueStr);
            }
        }
    }
}
