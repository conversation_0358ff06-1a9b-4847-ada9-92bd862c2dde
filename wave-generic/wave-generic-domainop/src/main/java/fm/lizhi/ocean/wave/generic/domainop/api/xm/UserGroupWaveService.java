package fm.lizhi.ocean.wave.generic.domainop.api.xm;

import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.generic.domainop.WaveDomainOpGenericService;
import fm.lizhi.ocean.wave.generic.domainop.protocol.RelocationProto;

/**
 * lz-xm-user-group
 * <AUTHOR>
 * @date 2024/2/26 11:18
 */
public interface UserGroupWaveService extends WaveDomainOpGenericService {
    @Override
    @Service(domain = 20019, op = 18, request = RelocationProto.InvokerRequest.class, response = RelocationProto.ResultPb.class)
    @Return(resultType = RelocationProto.ResultPb.class)
    Result<RelocationProto.ResultPb> invoke(@Attribute(name = "param") RelocationProto.ParamPb param);
}
