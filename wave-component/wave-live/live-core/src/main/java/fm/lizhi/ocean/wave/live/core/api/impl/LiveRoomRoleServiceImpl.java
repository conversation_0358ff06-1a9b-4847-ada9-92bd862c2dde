package fm.lizhi.ocean.wave.live.core.api.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomRoleIcon;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomRoleType;
import fm.lizhi.ocean.wave.comment.core.dao.redis.LiveRedisDao;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.param.CheckRoomHostParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.CheckSuperPermissionParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetLiveRoomUserAllRoleParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetLiveRoomUserRoleParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveRoomUserAllRoleResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveRoomUserRoleResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveRoomRoleServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.liveroomuserrole.biz.LiveRoomUserRoleProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LiveRoomRoleServiceImpl implements LiveRoomRoleService {


    @Resource
    private ProcessorFactory processorFactory;

    @MyAutowired
    private ILiveRoomRoleServiceRemote liveRoomRoleServiceRemote;

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private LiveRedisDao liveRedisDao;

    @Override
    public Result<Boolean> hasSuperPermission(long liveRoomId, long userId) {
        CheckSuperPermissionParam param = new CheckSuperPermissionParam();
        param.setLiveRoomId(liveRoomId);
        param.setUserId(userId);
        return liveRoomRoleServiceRemote.hasSuperPermission(param);
    }

    @Override
    public Result<Boolean> isRoomHost(long njUserId, long userId) {

        Result<Boolean> result = liveRoomRoleServiceRemote.isRoomHost(CheckRoomHostParam.builder()
                .njUserId(njUserId)
                .userId(userId).build());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomUserAllRole error - njUserId:{},userId:{},rCode:{}", njUserId, userId, result.rCode());
            return new Result<>(GET_LIVE_ROOM_HOST_ERROR, false);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target());
    }

    @Override
    public Result<List<LiveRoomUserRole>> getLiveRoomUserAllRole(long liveRoomId, long targetUserId) {
        GetLiveRoomUserAllRoleParam param = new GetLiveRoomUserAllRoleParam();
        param.setLiveRoomId(liveRoomId);
        param.setRoleUserId(targetUserId);
        Result<List<GetLiveRoomUserAllRoleResult>> result = liveRoomRoleServiceRemote.getLiveRoomUserAllRole(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomUserAllRole error - liveRoomId:{},targetUserId:{},rCode:{}", liveRoomId, targetUserId, result.rCode());
            return new Result<>(GET_LIVE_ROOM_USER_ALL_ROLE_ERROR, new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(result.target())) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, new ArrayList<>());
        }
        List<LiveRoomUserRole> list = result.target().stream().filter(p -> Objects.equals(p.getStatus(), 0)).map(p -> {
            LiveRoomUserRole liveRoomUserRole = new LiveRoomUserRole();
            liveRoomUserRole.setId(p.getId());
            liveRoomUserRole.setLiveRoomId(p.getLiveRoomId());
            liveRoomUserRole.setRole(p.getRole());
            liveRoomUserRole.setRoleUserId(p.getRoleUserId());
            liveRoomUserRole.setStatus(p.getStatus());
            return liveRoomUserRole;
        }).collect(Collectors.toList());

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, list);
    }

    @Override
    public Result<LiveRoomUserRole> getLiveRoomUserRole(long liveRoomId, long roleUserId, int role) {
        GetLiveRoomUserRoleParam param = new GetLiveRoomUserRoleParam();
        // 设置查询参数
        param.setLiveRoomId(liveRoomId);
        param.setRole(role);
        param.setRoleUserId(roleUserId);
        // 获取用户角色
        Result<GetLiveRoomUserRoleResult> result = liveRoomRoleServiceRemote.getLiveRoomUserRole(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomUserRole failed, param={}", param);
            return new Result<>(GET_LIVE_ROOM_USER_ROLE_ERROR, null);
        }
        GetLiveRoomUserRoleResult target = result.target();
        LiveRoomUserRole liveRoomUserRole = new LiveRoomUserRole();
        liveRoomUserRole.setId(target.getId());
        liveRoomUserRole.setLiveRoomId(target.getLiveRoomId());
        liveRoomUserRole.setRole(target.getRole());
        liveRoomUserRole.setRoleUserId(target.getRoleUserId());
        liveRoomUserRole.setStatus(target.getStatus());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, liveRoomUserRole);
    }

    @Override
    public Result<LiveRoomRoleIcon> getLiveRoomRoleIcon(long liveRoomId, long roleUserId) {

        Result<List<LiveRoomUserRole>> result = this.getLiveRoomUserAllRole(liveRoomId, roleUserId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomUserAllRole failed, rCode={}`liveRoomId={},roleUserId={}", result.rCode(), liveRoomId, roleUserId);
            return new Result<>(GET_LIVE_ROOM_ROLE_ICON_ERROR, null);
        }

        if (CollectionUtils.isEmpty(result.target())) {
            return new Result<>(GET_LIVE_ROOM_ROLE_ICON_NOT_FOUND, null);
        }

        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        LiveRoomUserRoleProcessor processor = processorFactory.getProcessor(appId, LiveRoomUserRoleProcessor.class);
        CommonLiveConfig liveCommonConfig = processor.getLiveCommonConfig();

        LiveRoomRoleIcon icon = new LiveRoomRoleIcon();
        icon.setLiveRoomId(liveRoomId);
        icon.setAspect(liveCommonConfig.getUserRoleImageBadgeAspect());
        if (result.target().stream().anyMatch(p -> Objects.equals(p.getRole(), LiveRoomRoleType.OWNER))) {
            icon.setRole(LiveRoomRoleType.OWNER);
            icon.setIconUrl(liveCommonConfig.getRoomOwnerIconUrl());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, icon);
        }

        if (result.target().stream().anyMatch(p -> Objects.equals(p.getRole(), LiveRoomRoleType.SUPER_MANAGER))) {
            icon.setRole(LiveRoomRoleType.SUPER_MANAGER);
            icon.setIconUrl(liveCommonConfig.getRoomSuperManagerIconUrl());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, icon);
        }

        if (result.target().stream().anyMatch(p -> Objects.equals(p.getRole(), LiveRoomRoleType.MANAGER))) {
            icon.setRole(LiveRoomRoleType.MANAGER);
            icon.setIconUrl(liveCommonConfig.getRoomManagerIconUrl());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, icon);
        }


        return new Result<>(GET_LIVE_ROOM_ROLE_ICON_NOT_FOUND, null);
    }

    @Override
    public Result<List<LiveRoomUserRole>> getLiveRoomUserAllRoleFromCache(long liveRoomId, long roleUserId) {
        //先冲缓存中获取
        Optional<List<LiveRoomUserRole>> roles = liveRedisDao.getLiveRoomUserRole(liveRoomId, roleUserId);
        //数据为空，就从下游接口获取
        if (!roles.isPresent()) {
            Result<List<LiveRoomUserRole>> result = this.getLiveRoomUserAllRole(liveRoomId, roleUserId);
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                return new Result<>(GET_LIVE_ROOM_USER_ALL_ROLE_ERROR, new ArrayList<>());
            }
            List<LiveRoomUserRole> roleList = result.target();
            liveRedisDao.saveLiveRoomUserRole(liveRoomId, roleUserId, roleList);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, roles.orElseGet(ArrayList::new));
    }
}
