package fm.lizhi.ocean.wave.live.core.remote.constants;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;

import java.util.Map;

@Getter
@ToString
@AllArgsConstructor
public enum HyBehaviorStatusEnum {

    /**
     * 无
     */
    DEFAULT(-1, "-1", -1, 0,""),
    /**
     * 预告
     */
    ADD_LIVE(2, "ffc8c8c8", 0, 0,"预告中"),
    /**
     * 听直播
     */
    LISTEN_LIVE(0, "ffff278e", 1, 2,"正在玩"),
    /**
     * 直播中
     */
    ON_AIR(1, "ff00c3ff", 2, 1, "直播中"),

    /**
     * 在麦上
     */
    SERVING_WHEAT(3,"ffff278e",0,1,"上麦");

    private static Map<Integer, HyBehaviorStatusEnum> map = Maps.newHashMap();

    static {
        for (HyBehaviorStatusEnum hyBehaviorStatusEnum : HyBehaviorStatusEnum.values()) {
            map.put(hyBehaviorStatusEnum.type, hyBehaviorStatusEnum);
        }
    }

    /**
     * 行为类型，参考LiveBehaviorProto.BehaviorType
     */
    private int type;
    /**
     * 状态颜色
     */
    private String color;
    /**
     * 优先级（越大越优先显示）
     */
    private int priority;

    /**
     * 状态码
     */
    private int statusCode;

    /**
     * 描述
     */
    private String desc;

    public static HyBehaviorStatusEnum getBehaviorStatusEnum(@NonNull Integer type) {
        return map.get(type);
    }

}
