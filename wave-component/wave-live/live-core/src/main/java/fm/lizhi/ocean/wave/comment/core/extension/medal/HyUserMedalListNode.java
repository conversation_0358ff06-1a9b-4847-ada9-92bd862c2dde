package fm.lizhi.ocean.wave.comment.core.extension.medal;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.user.api.MedalUserRecordService;
import fm.lizhi.ocean.wave.user.bean.MedalUserRecordBean;
import fm.lizhi.ocean.wave.user.param.MedalUserRecordUserListParam;
import fm.lizhi.ocean.wave.user.result.MedalUserRecordUserListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext.MedalShowArea.COMMENT_AREA;
import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext.MedalShowArea.ENTER_NOTICE_AREA;

/**
 * 用户拥有的勋章列表节点
 */
@Slf4j
@Component
public class HyUserMedalListNode implements ICommentMedalNode {

    @Autowired
    private MedalUserRecordService medalUserRecordService;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private CommonProviderConfig commonConfig;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoContext context) {
        int medalShowArea = context.getMedalShowArea();
        if (medalShowArea == COMMENT_AREA.getArea()) {
            List<Medal> medals = context.getComment().getMedal();
            if (CollectionUtils.isEmpty(medals)) {
                return Optional.empty();
            }

            // 勋章列表
            List<BadgeImageVO> medalList = new ArrayList<>();
            for (Medal medal : medals) {
                if (StringUtils.isBlank(medal.getCover())) {
                    continue;
                }
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(medal.getCover());
                badgeImageVO.setBadgeAspect(medal.getAspect());
                medalList.add(badgeImageVO);
            }
            return Optional.of(medalList);
        } else if (medalShowArea == ENTER_NOTICE_AREA.getArea()) {
            //根据不同的groupId, 查询用户勋章列表
            float defaultMedalIconAspect = context.getCommentConfig().getDefaultMedalIconAspect();
            List<Integer> medalShowSort = JSON.parseArray(commentConfig.getHy().getPayRateMedalShowSortJSONArr(), Integer.class);
            if (CollectionUtils.isEmpty(medalShowSort)) {
                log.warn("getBuildEnterNoticeMedalNodes medalShowSort is empty");
                return Optional.empty();
            }

            List<BadgeImageVO> list = new ArrayList<>();
            for (Integer medalSort : medalShowSort) {
                if (medalSort <= 2) {
                    continue;
                }
                long groupId = getMedalGroupId(medalSort);
                Optional<List<BadgeImageVO>> badgeImageVOList = getMedalUserRecord(groupId, context.getEnterNoticeEntry().getUserId(), context.getAppId(), defaultMedalIconAspect);
                badgeImageVOList.ifPresent(list::addAll);
            }

            return Optional.of(list);
        }
        return Optional.empty();
    }

    private Optional<List<BadgeImageVO>> getMedalUserRecord(long groupId, Long userId, int appId, float defaultAspect) {
        List<BadgeImageVO> medalList = new ArrayList<>();
        MedalUserRecordUserListParam medalUserRecordUserListParam = new MedalUserRecordUserListParam();
        medalUserRecordUserListParam.setUserId(userId);
        medalUserRecordUserListParam.setTypeId(groupId);
        Result<MedalUserRecordUserListResult> result = medalUserRecordService.medalUserRecordUserListFromCache(medalUserRecordUserListParam);
        if (result.rCode() == 0) {
            List<MedalUserRecordBean> medalUserRecords = result.target().getMedalUserRecords();
            if (!org.springframework.util.CollectionUtils.isEmpty(medalUserRecords)) {
                BusinessConfig businessConfig = commonConfig.getBusinessConfig(appId);
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), medalUserRecords.get(0).getMedalImageUrl()));
                badgeImageVO.setBadgeAspect(defaultAspect);
                medalList.add(badgeImageVO);
            }
        }
        return Optional.of(medalList);
    }

    /**
     * 获取用户勋章分组id
     *
     * @param medal 勋章id
     * @return 勋章分组id
     */
    private long getMedalGroupId(Integer medal) {
        long groupId = 0;
        switch (medal) {
            case 3:
                groupId = commentConfig.getHy().getPayRateMedalPinkDiamondGroupId();
                break;
            case 4:
                groupId = commentConfig.getHy().getPayRateMedalVipGroupId();
                break;
            case 5:
                groupId = commentConfig.getHy().getPayRateMedalComeBackGroupId();
                break;
            case 6:
                groupId = commentConfig.getHy().getPayRateMedalPlayerGroupId();
                break;
            default:
                break;
        }
        return groupId;
    }
}
