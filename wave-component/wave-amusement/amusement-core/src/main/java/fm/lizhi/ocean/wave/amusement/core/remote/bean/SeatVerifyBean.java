package fm.lizhi.ocean.wave.amusement.core.remote.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/9/22 11:14
 */
@Data
@Accessors(chain = true)
public class SeatVerifyBean {
    /**
     * 认证状态
     */
    private int verifyStatus;
    /**
     * 实名认证事务id
     */
    private long transactionId;
    /**
     * 用户剩余待认证时间
     */
    private long restWaitVerifyTime;
    /**
     * 系统配置待认证时间
     */
    private long waitVerifyTime;
    /**
     * 用户剩余禁止上麦时间
     */
    private long restBanOnSeatTime;
    /**
     * 系统配置禁止上麦时间
     */
    private long banOnSeatTime;
}
