package fm.lizhi.ocean.wave.live.core.extension.startlive.biz.pp;

import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.extension.startlive.IStartLiveProcessor;
import fm.lizhi.ocean.wave.live.core.extension.startlive.bean.StartLivePreBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/15
 */
@Slf4j
@Component
public class PpStartLiveProcessor implements IStartLiveProcessor {


    @Override
    public ResultVO<Void> preprocessor(StartLivePreBean data) {
        // 具体直播模式枚举 @see fm.lizhi.live.room.pp.enums.LiveModeEnum
        // PP本次需要支持所有类型的开播，所以这里不做额外校验，待确认
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.PP;
    }
}
