package fm.lizhi.ocean.wave.common.validation;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;

import java.util.List;

/**
 * Bean校验器
 */
@Component
public class BeanValidator {

    /**
     * spring的校验器, 使用该接口而不是javax的校验器
     */
    @Autowired
    private Validator validator;

    /**
     * 校验bean
     *
     * @param bean 待校验的bean
     * @return 校验结果
     */
    public ValidateResult validate(Object bean) {
        BeanPropertyBindingResult errors = new BeanPropertyBindingResult(bean, StringUtils.EMPTY);
        validator.validate(bean, errors);
        List<FieldError> fieldErrors = errors.getFieldErrors();
        if (CollectionUtils.isNotEmpty(fieldErrors)) {
            FieldError fieldError = fieldErrors.get(0);
            String message = "Field " + fieldError.getField() + ": " + fieldError.getDefaultMessage();
            return ValidateResult.failure(message);
        }
        List<ObjectError> globalErrors = errors.getGlobalErrors();
        if (CollectionUtils.isNotEmpty(globalErrors)) {
            ObjectError objectError = globalErrors.get(0);
            String message = "Object " + objectError.getObjectName() + ": " + objectError.getDefaultMessage();
            return ValidateResult.failure(message);
        }
        return ValidateResult.success();
    }
}
