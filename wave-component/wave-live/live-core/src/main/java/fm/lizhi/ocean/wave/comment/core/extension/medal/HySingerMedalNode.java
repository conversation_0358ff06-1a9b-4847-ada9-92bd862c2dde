package fm.lizhi.ocean.wave.comment.core.extension.medal;

import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.facade.HySingerFacade;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 黑叶歌手勋章
 *
 * <AUTHOR>
 * @date 2023/09/23
 */
@Component
@Slf4j
public class HySingerMedalNode implements ICommentMedalNode {
    @Autowired
    private HySingerFacade hySingerFacade;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoContext context) {
//        认证标签需求：不返回歌手勋章。2025-03-31
//        TransientComment comment = context.getComment();
//        if (comment == null) {
//            return Optional.empty();
//        }
//
//        Pair<String, Float> singerMedal = hySingerFacade.getSingerMedal(comment.getUserId());
//        if (singerMedal != null) {
//            BadgeImageVO badgeImageVO = new BadgeImageVO();
//            badgeImageVO.setBadgeUrl(singerMedal.getLeft());
//            badgeImageVO.setBadgeAspect(singerMedal.getRight());
//            return Optional.of(Arrays.asList(badgeImageVO));
//        }
        return Optional.empty();
    }
}
