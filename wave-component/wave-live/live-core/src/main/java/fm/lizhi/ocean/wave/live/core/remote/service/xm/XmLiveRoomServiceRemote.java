package fm.lizhi.ocean.wave.live.core.remote.service.xm;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.xm.api.LiveRoomService;
import fm.lizhi.live.room.xm.protocol.LiveRoomProto;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.api.live.constants.xm.XmLiveRoomType;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.remote.bean.CategoryBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.UserCategoryBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.xm.XmLiveRoomExtraBean;
import fm.lizhi.ocean.wave.live.core.remote.param.AddLiveRoomRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.BatchGetLiveRoomCategoryResponse;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomCategoryResponse;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetRoomWhitelistByUserIdResponse;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.pp.core.api.LivePpUserService;
import xm.fm.lizhi.live.pp.core.protocol.LivePpUserProto;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static fm.lizhi.live.room.pp.api.LiveRoomService.ADD_LIVE_ROOM_LIVE_ROOM_EXISTS;
import static fm.lizhi.live.room.pp.api.LiveRoomService.ADD_LIVE_ROOM_SUCCESS;

/**
 * <AUTHOR>
 * @date 2023/9/12 16:19
 */
@Slf4j
@Component
public class XmLiveRoomServiceRemote extends RemoteServiceInvokeFacade implements ILiveRoomServiceRemote {

    @Autowired
    private LiveRoomService liveRoomService;
    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private LivePpUserService livePpUserService;

    private final ExecutorService executor = ThreadUtils.getTtlExecutors(getClass().getSimpleName());
    private final ListeningExecutorService refreshLocalCachePool = MoreExecutors.listeningDecorator(executor);

    /**
     * 直播间分类缓存
     */
    private LoadingCache<Long, Result<GetLiveRoomCategoryResponse>> xmUserCateCache = CacheBuilder.newBuilder()
            .expireAfterWrite(3600, TimeUnit.SECONDS)
            .refreshAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(1000)
            .build(new CacheLoader<Long, Result<GetLiveRoomCategoryResponse>>() {
                @Override
                public Result<GetLiveRoomCategoryResponse> load(Long key) throws Exception {
                    try {
                        return getLiveRoomCategory(key);
                    } catch (Exception e) {
                        log.warn("xmUserCateCache error key={}", key, e);
                    }
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SERVER_BUSY, null);
                }

                @Override
                public ListenableFuture<Result<GetLiveRoomCategoryResponse>> reload(Long key, Result<GetLiveRoomCategoryResponse> oldValue) throws Exception {
                    return refreshLocalCachePool.submit(() -> this.load(key));
                }
            });

    /**
     * 直播间数据缓存
     */
    private LoadingCache<Long, Result<GetLiveRoomResult>> LIVE_ROOM_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .maximumSize(1000)
            .build(new CacheLoader<Long, Result<GetLiveRoomResult>>() {
                @Override
                public Result<GetLiveRoomResult> load(Long key) throws Exception {
                    try {
                        return getLiveRoomByUserId(key);
                    } catch (Exception e) {
                        log.warn("XM.LIVE_ROOM_CACHE error key={}", key, e);
                    }
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SERVER_BUSY, null);
                }
            });

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }

    @Override
    public Result<GetLiveRoomResult> getLiveRoomByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            log.warn("XmLiveRoomServiceRemote,getLiveRoomByUserId,userId is null");
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_ERROR, null);
        }

        Result<LiveRoomProto.ResponseGetLiveRoomByUserIdNoCache> result = liveRoomService.getLiveRoomByUserIdNoCache(userId);
        if (GeneralRCode.GENERAL_RCODE_SUCCESS != result.rCode()) {
            if (result.rCode() == LiveRoomService.GET_LIVE_ROOM_BY_RADIO_ID_ERROR_NOT_FOUND) {
                return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_NOT_FOUND, null);
            }
            log.warn("XmLiveRoomServiceRemote,getLiveRoomByUserId error,userId={}, appId={}, rCode={}", userId, BusinessEvnEnum.XIMI.appId(), result.rCode());
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_ERROR, null);
        }
        LiveRoomProto.LiveRoom liveRoom = result.target().getLiveRoom();

        return new Result<>(result.rCode(), GetLiveRoomResult.builder()
                .liveRoomBean(convert(liveRoom))
                .defaultWelcomeMsg(liveConfig.getXm().getDefaultLiveWelcomeMsg())
                .defaultAnnouncement(liveConfig.getXm().getDefaultAnnouncement())
                .build());
    }

    @Override
    public Result<GetLiveRoomResult> getLiveRoomByLocalCache(Long userId) {
        return LIVE_ROOM_CACHE.getUnchecked(userId);
    }

    @Override
    public Result<GetLiveRoomResult> getLiveRoomByUserIdCache(Long userId) {
        Result<LiveRoomProto.ResponseGetLiveRoomByUserId> result = liveRoomService.getLiveRoomByUserId(userId);
        if (GeneralRCode.GENERAL_RCODE_SUCCESS != result.rCode()) {
            if (result.rCode() == LiveRoomService.GET_LIVE_ROOM_BY_RADIO_ID_ERROR_NOT_FOUND) {
                return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_NOT_FOUND, null);
            }
            log.warn("xm getLiveRoomByUserId error,userId={}, rCode={}", userId, result.rCode());
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_ERROR, null);
        }
        LiveRoomProto.LiveRoom liveRoom = result.target().getLiveRoom();
        return new Result<>(result.rCode(), GetLiveRoomResult.builder()
                .liveRoomBean(convert(liveRoom))
                .build());
    }

    @Override
    public Result<GetLiveRoomResult> getLiveRoom(Long liveRoomId) {
        Result<LiveRoomProto.ResponseGetLiveRoomData> resp = liveRoomService.getLiveRoom(liveRoomId);

        int rCode = resp.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (rCode == LiveRoomService.GET_LIVE_ROOM_ERROR_NOT_FOUND) {
                return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_NOT_FOUND, null);
            }
            log.warn("xm getLiveRoom error,liveRoomId={}, rCode={}", liveRoomId, rCode);
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_ERROR, null);
        }

        LiveRoomProto.LiveRoom liveRoom = resp.target().getLiveRoom();
        return new Result<>(rCode, GetLiveRoomResult.builder()
                .liveRoomBean(convert(liveRoom))
                .build());
    }

    private LiveRoomBean convert(LiveRoomProto.LiveRoom liveRoom) {
        LiveRoomBean liveRoomBean = new LiveRoomBean();
        liveRoomBean.setId(liveRoom.getId());
        liveRoomBean.setRadioId(liveRoom.getRadioId());
        liveRoomBean.setUserId(liveRoom.getUserId());
        /**
         * LiveRoomStatus的枚举值,与{@link LiveConstants.LIVE_ROOM_STATUS_BLOCK}相同,直接复用,没有做转换
         */
        liveRoomBean.setStatus(LiveRoomStatusEnum.from(liveRoom.getStatus()));
        liveRoomBean.setStreamUrl(liveRoom.getStreamUrl());
        liveRoomBean.setExtraJson(JsonUtil.dumps(new XmLiveRoomExtraBean()
                .setRoomType(XmLiveRoomType.DEFAULT.getValue())
        ));
        liveRoomBean.setName(liveRoom.getName());
        return liveRoomBean;
    }

    @Override
    public Result<GetLiveRoomResult> getLiveRoomCache(long liveRoomId) {
        Result<LiveRoomProto.ResponseGetLiveRoomData> result = liveRoomService.getLiveRoom(liveRoomId);
        int rCode = result.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (rCode == LiveRoomService.GET_LIVE_ROOM_ERROR_NOT_FOUND) {
                return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_NOT_FOUND, null);
            }
            log.warn("xm getLiveRoom error,liveRoomId={}, rCode={}", liveRoomId, rCode);
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_ERROR, null);
        }

        LiveRoomProto.LiveRoom liveRoom = result.target().getLiveRoom();
        return new Result<>(rCode, GetLiveRoomResult.builder()
                .liveRoomBean(convert(liveRoom))
                .build());
    }

    @Override
    public Result<Void> addLiveRoom(AddLiveRoomRemoteParam param) {
        Result<Void> result = liveRoomService.addLiveRoom(param.getRadioId(), param.getUserId(), param.getUserName() + "的直播间");
        int rCode = result.rCode();
        log.info("xm addLiveRoom userId={},radioId={},name={},rCode={}", param.getUserId(), param.getRadioId(), param.getUserName(), rCode);
        switch (rCode) {
            case ADD_LIVE_ROOM_SUCCESS:
            case ADD_LIVE_ROOM_LIVE_ROOM_EXISTS:
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            default:
                return new Result<>(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, null);
        }
    }

    @Override
    public Result<GetLiveRoomCategoryResponse> getLiveRoomCategory(long userId) {
        Result<LivePpUserProto.ResponseGetWhitelistByUserId> result = livePpUserService.getWhitelistByUserId(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmLiveRoomServiceRemote getLiveRoomCategory error,userId={}, rCode={}", userId, result.rCode());
            return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_CATEGORY_ERROR, null);
        }
        LivePpUserProto.Whitelist whitelist = result.target().getWhitelist();

        // 组装列表
        List<GetLiveRoomCategoryResponse.Category> categoryList = Lists.newArrayList();
        categoryList.add(GetLiveRoomCategoryResponse.Category.builder()
                .id(userId)
                .name(whitelist.getCornerName())
                .build()
        );
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetLiveRoomCategoryResponse.builder()
                .categoryList(categoryList)
                .build());
    }

    @Override
    public Result<GetLiveRoomCategoryResponse> getLiveRoomCategoryByLocalCache(long userId) {
        try {
            return xmUserCateCache.get(userId);
        } catch (ExecutionException e) {
            log.warn("XmLiveRoomServiceRemote getLiveRoomCategoryByLocalCache error, userId={} ", userId, e);
        }
        return new Result<>(ILiveRoomServiceRemote.GET_LIVE_ROOM_CATEGORY_BY_LOCAL_CACHE_ERROR, null);
    }

    @Override
    public Result<BatchGetLiveRoomCategoryResponse> batchGetUserCategoryList(List<Long> userIds) {
        Result<LivePpUserProto.ResponseBatchGetWhitelistByUserIds> result = livePpUserService.batchGetWhitelistByUserIds(userIds);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmLiveRoomServiceRemote batchGetUserCategoryList fail, rCode={}", result.rCode());
            return new Result<>(ILiveRoomServiceRemote.BATCH_GET_LIVE_ROOM_CATEGORY_FAIL, null);
        }

        List<LivePpUserProto.Whitelist> whiteList = result.target().getWhitelistList();
        BatchGetLiveRoomCategoryResponse response = convertToBatchGetLiveRoomCategoryResponse(whiteList);
        return new Result<>(0, response);
    }


    private BatchGetLiveRoomCategoryResponse convertToBatchGetLiveRoomCategoryResponse(List<LivePpUserProto.Whitelist> whiteList) {
        if (CollectionUtils.isEmpty(whiteList)) {
            return BatchGetLiveRoomCategoryResponse.builder().userCategoryBeans(Collections.emptyList()).build();
        }

        List<UserCategoryBean> userCategoryBeans = new ArrayList<>();
        Map<Long, String> userTabNameMap = new HashMap<>();
        for (LivePpUserProto.Whitelist white : whiteList) {
            // 角标名称
            userTabNameMap.put(white.getUserId(), white.getCornerName());

            // 分类名称
            UserCategoryBean userCategoryBean = new UserCategoryBean();
            userCategoryBean.setUserId(white.getUserId());

            List<LivePpUserProto.WhitelistCategory> whitelistCategory = white.getCategoryList();
            List<CategoryBean> categoryList = new ArrayList<>();
            if (CollectionUtils.isEmpty(categoryList)) {
                continue;
            }
            for (LivePpUserProto.WhitelistCategory category : whitelistCategory) {
                CategoryBean categoryBean = new CategoryBean();
                categoryBean.setId(category.getId());
                categoryBean.setName(category.getName());
                categoryList.add(categoryBean);
            }

            userCategoryBean.setCategoryBeans(categoryList);
            userCategoryBeans.add(userCategoryBean);
        }
        return BatchGetLiveRoomCategoryResponse.builder()
                .userTabNameMap(userTabNameMap)
                .userCategoryBeans(userCategoryBeans)
                .build();
    }

    @Override
    public Result<GetRoomWhitelistByUserIdResponse> getRoomWhitelistByUserId(long userId) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, null);
    }

    @Override
    public Result<Boolean> getIsSignSingerRoom(long userId) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, null);
    }

    @Override
    public Result<Boolean> getIsSignSingerRoomCache(long userId) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, null);
    }

    @Override
    public Result<Boolean> isUserInRoomWhiteList(long njId) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, null);
    }
}
