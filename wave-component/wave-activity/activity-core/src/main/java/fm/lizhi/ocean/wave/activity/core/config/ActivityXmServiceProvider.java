package fm.lizhi.ocean.wave.activity.core.config;

import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import fm.lizhi.xm.content.api.BannerManagementService;
import fm.lizhi.xm.content.api.PendantService;
import org.springframework.context.annotation.Configuration;
import xm.fm.lizhi.pp.activity.services.livepk.LivePkSpringService;
import xm.fm.lizhi.pp.rank.api.LiveRoomRankingService;

@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PendantService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveRoomRankingService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LivePkSpringService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = BannerManagementService.class),
}
)
public class ActivityXmServiceProvider {
}
