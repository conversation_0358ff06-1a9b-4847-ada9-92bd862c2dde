package fm.lizhi.ocean.wave.common.blockhandler.fallback;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import fm.lizhi.ocean.wave.common.blockhandler.fallback.web.WebFallbackContext;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;

/**
 * <AUTHOR>
 * @date 2023/12/20 11:30
 */
public interface WebFallback extends WaveFallback<ResultVO<?>, WebFallbackContext> {

    @Override
    ResultVO<?> handle(WebFallbackContext context, BlockException be) throws Exception;

}
