package fm.lizhi.ocean.wave.live.core.extension.openlivepermission.biz.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.constant.LiveConstants;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.openlivepermission.bean.OpenLivePermissionPrePostBean;
import fm.lizhi.ocean.wave.live.core.extension.openlivepermission.biz.OpenLivePermissionProcessor;
import fm.lizhi.ocean.wave.live.core.remote.param.AddLiveRoomRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import fm.lizhi.trade.withdrawal.api.WithdrawService;
import fm.lizhi.trade.withdrawal.protocol.WithdrawServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class HyOpenLivePermissionProcessor implements OpenLivePermissionProcessor {

    @Autowired
    private WithdrawService withdrawService;

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private UserService userService;

    @MyAutowired
    private ILiveRoomServiceRemote liveRoomServiceRemote;

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public ResultVO<Void> preprocessor(OpenLivePermissionPrePostBean data) {
        //没有直播间则开通直播间
        Result<GetLiveRoomResult> roomResult = liveRoomServiceRemote.getLiveRoomByUserId(data.getUserId());
        if (!Objects.equals(roomResult.rCode(), (int) GeneralRCode.GENERAL_RCODE_SUCCESS)
                && !Objects.equals(roomResult.rCode(), LiveConstants.GET_LIVE_ROOM_BY_RADIO_ID_NO_LIVE_ROOM)) {
            log.error("HyOpenLivePermissionProcessor getLiveRoomByUserId error - userId:{}, appId:{}, rcode:{}", data.getUserId(), data.getAppId(), roomResult.rCode());
            return ResultVO.failure("服务繁忙,请稍后再试");
        }

        if (Objects.nonNull(roomResult.target()) && Objects.nonNull(roomResult.target().getLiveRoomBean())
                && Objects.nonNull(roomResult.target().getLiveRoomBean().getId())) {
            return ResultVO.success();
        }
        Result<GetSimpleUserResult> simpleUser = userService.getSimpleUserByCache(data.getUserId());
        if (!Objects.equals(simpleUser.rCode(), (int) GeneralRCode.GENERAL_RCODE_SUCCESS)) {
            log.error("HyOpenLivePermissionProcessor getSimpleUser error - userId:{}, appId:{}, rcode:{}", data.getUserId(), data.getAppId(), simpleUser.rCode());
            return ResultVO.failure("服务繁忙,请稍后再试");
        }
        SimpleUser user = simpleUser.target().getSimpleUser();
        long userId = user.getUserId();
        long radioId = user.getRadioId();
        String nickName = user.getNickName();
        Result<Void> addRoomResult = liveRoomServiceRemote.addLiveRoom(AddLiveRoomRemoteParam.builder()
                .userName(nickName)
                .userId(userId)
                .radioId(radioId)
                .build());
        if (!Objects.equals(addRoomResult.rCode(), (int) GeneralRCode.GENERAL_RCODE_SUCCESS)) {
            log.error("HyOpenLivePermissionProcessor addLiveRoom error - userId:{}, appId:{}, rcode:{}", data.getUserId(), data.getAppId(), addRoomResult.rCode());
            return ResultVO.failure("服务繁忙,请稍后再试");
        }
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> postprocessor(OpenLivePermissionPrePostBean data) {
        //判断是否签约
        if (liveConfig.getHy().isSignSwitch) {
            String tenantCode = liveConfig.getHy().getTenantCode();
            String channelCode = liveConfig.getHy().getChannelCode();
            Result<WithdrawServiceProto.ResponseGetUserContractStatus> signResult = withdrawService.getUserContractStatus(tenantCode, String.valueOf(data.getUserId()), channelCode);
            if (!Objects.equals(signResult.rCode(), (int) GeneralRCode.GENERAL_RCODE_SUCCESS) || Objects.equals(signResult.target().getStatus(), 1)) {
                log.error("HyOpenLivePermissionProcessor getUserContractStatus error - userId:{}, appId:{}, rcode:{}", data.getUserId(), data.getAppId(), signResult.rCode());
                return ResultVO.failure("请进行签约");
            }
        }
        return ResultVO.success();
    }
}
