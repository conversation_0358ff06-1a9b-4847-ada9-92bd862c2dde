package fm.lizhi.ocean.wave.amusement.core.extension.vocalroom.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpDownParam {

    /**
     * 操作人id
     */
    private Long operatorUserId;

    private long liveId;

    /**
     * 被操作的用户id
     */
    private Long targetUserId;
}
