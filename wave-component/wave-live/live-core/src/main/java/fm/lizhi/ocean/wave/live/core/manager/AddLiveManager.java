package fm.lizhi.ocean.wave.live.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementService;
import fm.lizhi.ocean.wave.api.amusement.constants.UserPlayStatusEnum;
import fm.lizhi.ocean.wave.api.live.constants.LiveStatusEnum;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.constants.LiveMsgCodes;
import fm.lizhi.ocean.wave.live.core.extension.addlive.AddLiveProcessor;
import fm.lizhi.ocean.wave.live.core.extension.addlive.bean.AddLivePostBean;
import fm.lizhi.ocean.wave.live.core.extension.addlive.bean.AddLivePreBean;
import fm.lizhi.ocean.wave.live.core.model.param.AddLiveParam;
import fm.lizhi.ocean.wave.live.core.model.result.AddLiveResult;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.param.AddLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLatestLiveIdRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Slf4j
@Component
public class AddLiveManager {
    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private OpenLivePermissionManager openLivePermissionManager;

    @MyAutowired
    private ILiveRoomServiceRemote liveRoomServiceRemote;
    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;
    @Autowired
    private AmusementService amusementService;


    public ResultVO<AddLiveResult> addLive(Integer appId, Long userId, String ip, AddLiveParam param) {

        boolean permission = checkLivePermission(userId, appId);
        if (!permission) {
            return ResultVO.failure(LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getCode(), LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getMsg());
        }

        //获取直播间 看是否改成获取带缓存的直接间
        Result<GetLiveRoomResult> liveRoomByUserIdResult = liveRoomServiceRemote.getLiveRoomByUserId(userId);
        if (GeneralRCode.GENERAL_RCODE_SUCCESS != liveRoomByUserIdResult.rCode()
                || liveRoomByUserIdResult.target().getLiveRoomBean() == null) {
            log.warn("AddLiveManager getLiveRoomByUserId error, userId:{}", userId);
            return ResultVO.failure(LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getCode(), LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getMsg());
        }
        LiveRoomBean liveRoomBean = liveRoomByUserIdResult.target().getLiveRoomBean();

        Long inPreviewLive = getInPreviewLive(userId, liveRoomBean.getId(), appId);
        if (inPreviewLive == null) {
            log.warn("AddLiveManager getInPreviewLive error, userId:{}", userId);
            return ResultVO.failure(LiveMsgCodes.ADD_LIVE_GET_LIVE_FAIL.getCode(), LiveMsgCodes.ADD_LIVE_GET_LIVE_FAIL.getMsg());
        }

        //校验是否在通话中
        ResultVO<Void> voiceCallingCheck = isVoiceCalling(userId);
        if (!voiceCallingCheck.isOK()) {
            return ResultVO.failure(voiceCallingCheck.getRCode(), voiceCallingCheck.getPrompt().getMsg());
        }

        if (inPreviewLive != 0) {
            AddLiveResult addLiveResult = new AddLiveResult();
            addLiveResult.setLiveId(inPreviewLive);
            return ResultVO.success(addLiveResult);
        }

        //进程内差异化拓展
        AddLiveProcessor addLiveProcessor = processorFactory.getProcessor(appId, AddLiveProcessor.class);

        CommonLiveConfig liveCommonConfig = addLiveProcessor.getLiveCommonConfig();
        ResultVO<AddLiveResult> baseCheckResult = liveBaseInfoCheck(param, addLiveProcessor, liveCommonConfig);
        if (!baseCheckResult.isOK()) {
            return baseCheckResult;
        }

        AddLivePreBean addLivePreBean = new AddLivePreBean().setUserId(userId)
                                           .setLiveName(param.getLiveName())
                                           .setAnnouncement(param.getAnnouncement())
                                           .setLiveRoomBean(liveRoomBean);
        //前置差异化
        ResultVO<Void> preprocessor = addLiveProcessor.preprocessor(addLivePreBean);
        if (preprocessor.getRCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("AddLiveManager preprocessor error, userId:{}, msg:{}", userId, preprocessor.getPrompt().getMsg());
            return ResultVO.failure(preprocessor.getPrompt().getMsg());
        }

        //直播名称处理
        param.setLiveName(addLiveProcessor.liveNamePreProcessor(param, liveRoomBean.getId()));
        //房间类型处理
        addLiveProcessor.setRoomType(addLivePreBean);

        //核心逻辑
        String coverUrl = UrlUtils.getUriFromUrl(param.getCover());
        AddLiveRemoteParam.AddLiveRemoteParamBuilder paramBuilder = AddLiveRemoteParam.builder()
                .userId(userId)
                .ip(ip)
                .name(param.getLiveName())
                .announcement(param.getAnnouncement())
                .cover(coverUrl)
                .liveRoomBean(liveRoomBean);
        Result<Long> addLiveResult = liveServiceRemote.addLive(paramBuilder.build());
        if (GeneralRCode.GENERAL_RCODE_SUCCESS != addLiveResult.rCode()) {
            if (addLiveResult.rCode() == ILiveServiceRemote.ADD_LIVE_NOT_OPEN_LIVE_ROOM) {
                return ResultVO.failure(LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getCode(), LiveMsgCodes.ADD_LIVE_NOT_ADD_LIVE_AUTH.getMsg());
            }
            if (addLiveResult.rCode() == ILiveServiceRemote.ADD_LIVE_ERR_LIVE_ROOM_BLOCK) {
                return ResultVO.failure(liveCommonConfig.getLiveRoomBlockText());
            }
            return ResultVO.failure();
        }

        Long liveId = addLiveResult.target();

        //后置差异化
        ResultVO<Void> postprocessor = addLiveProcessor.postprocessor(new AddLivePostBean()
                .setLiveId(liveId)
                .setUserId(userId)
                .setLiveName(param.getLiveName())
                .setCover(coverUrl)
                .setAnnouncement(param.getAnnouncement()));
        if(!postprocessor.isOK()) {
            return ResultVO.failure(postprocessor.getRCode(), postprocessor.getPrompt());
        }

        //再获取一次直播信息，主要是为了获取新的推拉流地址(开预告,目前只返回liveId)
        return ResultVO.success(new AddLiveResult().setLiveId(liveId));
    }

    private static ResultVO<AddLiveResult> liveBaseInfoCheck(AddLiveParam param, AddLiveProcessor addLiveProcessor, CommonLiveConfig liveCommonConfig) {
        //基础业务校验
        if (param.getLiveName().length() < liveCommonConfig.getLiveTitleMinLength()) {
            return ResultVO.failure("直播名称少于" + liveCommonConfig.getLiveTitleMinLength() + "个字符");
        }
        if (param.getLiveName().length() > addLiveProcessor.getLiveCommonConfig().getLiveTitleMaxLength()) {
            return ResultVO.failure("直播名称大于" + liveCommonConfig.getLiveTitleMaxLength() + "个字符");
        }
        addLiveProcessor.checkParam(param);
        return ResultVO.success();
    }


    /**
     * 获取预告中的直播
     *
     * @param userId
     * @return if null 代表查询失败
     * if 0 代表没有预告
     * if >0 代表预告中的直播id
     */
    private Long getInPreviewLive(long userId, long liveRoomId, int appId) {
        Result<Long> lastLiveResp = liveServiceRemote.getLastLiveIdByParam(GetLatestLiveIdRemoteParam.builder()
                .liveRoomId(liveRoomId)
                .userId(userId)
                .appId(appId)
                .build());

        if (lastLiveResp.rCode() == ILiveServiceRemote.GET_LAST_LIVE_ID_NOT_FOUND) {
            return 0L;
        }

        if (lastLiveResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getInPreviewLive error - userId:{}, liveRoomId:{}, appId:{}, rCode:{}", userId, liveRoomId, appId, lastLiveResp.rCode());
            return null;
        }

        Long lastLiveId = 0L;
        if (lastLiveResp.rCode() != ILiveServiceRemote.GET_LAST_LIVE_ID_NOT_FOUND) {
            lastLiveId = lastLiveResp.target();
        }

        Result<GetLiveRemoteResult> getLiveResp = liveServiceRemote.getLive(GetLiveRemoteParam.builder()
                .liveId(lastLiveId)
                .build());

        if (getLiveResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getInPreviewLive error - userId:{}, liveRoomId:{}, appId:{}, rCode:{}", userId, liveRoomId, appId, getLiveResp.rCode());
            return null;
        }

        LiveBean liveBean = getLiveResp.target().getLiveBean();
        if (liveBean.getStatus() == LiveStatusEnum.FORENOTICE.getValue() || liveBean.getStatus() == LiveStatusEnum.ON_AIR.getValue()) {
            return liveBean.getId();
        }

        return 0L;
    }

    private boolean checkLivePermission(long userId, int appId) {
        ResultVO<Void> resultVO = openLivePermissionManager.checkPermission(userId, appId);
        return resultVO.isOK();
    }

    /**
     * 是否可以开麦
     *
     * @param userId 目标用户ID
     * @return 结果
     */
    private ResultVO<Void> isVoiceCalling(long userId) {
        Result<UserPlayStatusEnum> playSateResult = amusementService.getUserPlaySate(userId);
        if (playSateResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //不成功，忽略
            return ResultVO.success();
        }

        //用户在通话中，不能操作开麦
        if (playSateResult.target() == UserPlayStatusEnum.VOICE_CALL_ING) {
            return ResultVO.failure(LiveMsgCodes.ADD_LIVE_VOICE_CALL_ING.getCode(), LiveMsgCodes.ADD_LIVE_VOICE_CALL_ING.getMsg());
        }
        return ResultVO.success();
    }
}
