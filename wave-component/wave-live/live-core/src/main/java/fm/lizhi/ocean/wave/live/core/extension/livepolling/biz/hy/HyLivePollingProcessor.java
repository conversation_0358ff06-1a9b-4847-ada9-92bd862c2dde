package fm.lizhi.ocean.wave.live.core.extension.livepolling.biz.hy;

import com.google.common.base.Objects;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.call.hy.api.CallService;
import fm.lizhi.live.call.hy.protocol.LiveCallProto;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.DeviceBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.RoomType;
import fm.lizhi.live.data.bean.UserBehaviorType;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.ILiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.hy.HyLiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.manager.LiveHeartbeatManager;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.LivePollingProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.bean.LivePollingPostBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.HyBehaviorKafkaProducer;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyDeviceBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyUserBehaviorBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Slf4j
@Component
public class HyLivePollingProcessor implements LivePollingProcessor {

    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private CallService callService;

    @Autowired
    private HyLiveHeartbeatProcessor processor;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Override
    public ResultVO<Void> postprocessor(LivePollingPostBean param) {
        if (isOldVersion()) {
            processor.reportLiveHeartbeat(param);
        }

        return ResultVO.success();
    }


    /**
     * 是否是旧版本
     */
    private boolean isOldVersion() {
        try {
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            return clientVersion < commonProviderConfig.getMinHeartbeatClientVersion();
        } catch (Exception e) {
            log.error("hy isOldVersion error: ", e);
            return true;
        }
    }


    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public CommonLiveConfig getLiveCommonConfig() {
        return liveConfig.getHy();
    }

    @Override
    public String getActualChannelId(long liveId, String currentChannelId) {
        Result<LiveCallProto.ResponseGetLineCode> getLineCodeResult = callService.getLineCode(liveId);
        if(getLineCodeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //获取失败直接置null，抛弃这一次的数据
            log.warn("hy getActualChannelId error, liveId={}`currentChannelId={}`rCode={}", liveId, currentChannelId, getLineCodeResult.rCode());
            return null;
        }
        return getLineCodeResult.target().getLineCode();
    }
}
