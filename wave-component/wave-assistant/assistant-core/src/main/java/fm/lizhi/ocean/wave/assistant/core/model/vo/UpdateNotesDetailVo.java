package fm.lizhi.ocean.wave.assistant.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 应用更新公告详情
 *
 * <AUTHOR>
 * @date 2024-08-22 04:52:37
 */
@Data
@Accessors(chain = true)
public class UpdateNotesDetailVo {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 更新公告id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long noteId;

    /**
     * tab
     */
    private String tab;

    /**
     * 更新内容 Url
     */
    private String contentUrl;

    /**
     * 排序
     */
    private Integer sortNum;
}