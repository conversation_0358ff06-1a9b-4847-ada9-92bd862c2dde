package fm.lizhi.ocean.wave.amusement.core.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class VoiceCallOperateVo {

    /**
     * 拨打者信息
     */
    private VoiceCallUserVo caller;

    /**
     * 接受者信息
     */
    private VoiceCallUserVo callee;

    /**
     * 平台匹配类型，1：1V1通话
     */
    private Integer callWaveType;

    /**
     * 通话频道信息
     */
    private VoiceCallChannelVo callChannelInfo;

    /**
     * 当前用户是否关注了对方
     */
    private boolean userRelation;

}
