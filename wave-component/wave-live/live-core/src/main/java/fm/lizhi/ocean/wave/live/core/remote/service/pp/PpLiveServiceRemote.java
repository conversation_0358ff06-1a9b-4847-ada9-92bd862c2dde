package fm.lizhi.ocean.wave.live.core.remote.service.pp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.googlecode.protobuf.format.JsonFormat;
import fm.lizhi.business.constants.PhotoConst;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.pp.api.LiveAfficheService;
import fm.lizhi.live.room.pp.api.LiveNewService;
import fm.lizhi.live.room.pp.api.LiveNotifyLimitService;
import fm.lizhi.live.room.pp.api.LiveService;
import fm.lizhi.live.room.pp.protocol.LiveAfficheProto;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.live.room.pp.protocol.LiveNotifyLimitProto;
import fm.lizhi.ocean.wave.api.live.bean.UserLiveRoomStatus;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.comment.core.remote.bean.LiveRoomBackGround;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveRoomBackGroundResult;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.live.core.model.result.GetLiveExtraInfoResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.SpringUtils;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.common.util.VoiceUtils;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.constants.BizLiveModeMappingEnum;
import fm.lizhi.ocean.wave.live.core.model.result.Live;
import fm.lizhi.ocean.wave.live.core.remote.adapter.pp.*;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.pp.FamilyNjBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.pp.PpLiveExtraBean;
import fm.lizhi.ocean.wave.live.core.remote.constants.PpBehaviorStatusEnum;
import fm.lizhi.ocean.wave.live.core.remote.param.*;
import fm.lizhi.ocean.wave.live.core.remote.result.*;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.UserRealNameAuthStatus;
import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import fm.lizhi.pp.content.search.api.SearchService;
import fm.lizhi.pp.content.search.protocol.SearchProto;
import fm.lizhi.pp.decorate.api.call.PpChannelLiveService;
import fm.lizhi.pp.decorate.protocol.call.ChannelLiveProto;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.pp.vip.api.DecorateUserService;
import fm.lizhi.pp.vip.bean.enums.DecorateTipEnum;
import fm.lizhi.pp.vip.bean.req.GetUsingDecorateStockReq;
import fm.lizhi.pp.vip.bean.req.UsingDecorateStockDto;
import fm.lizhi.pp.vip.bean.resp.GetUsingDecorateStokResp;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;
import fm.lizhi.pp.vip.protocol.DecorateUserProto;
import fm.lizhi.server.business.util.ImgUtil;
import fm.pp.family.api.FamilyService;
import fm.pp.family.protocol.FamilyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pp.fm.lizhi.live.data.api.LiveStateService;
import pp.fm.lizhi.live.data.api.UserBehaviorService;
import pp.fm.lizhi.live.data.protocol.LiveBehaviorProto;
import pp.fm.lizhi.live.data.protocol.LiveStateProto;
import pp.fm.lizhi.live.enternotice.api.EnterNoticeService;
import pp.fm.lizhi.live.pp.push.api.PpPushService;
import pp.fm.lizhi.live.pp.push.protocol.PpPushProto;
import pp.fm.lizhi.live.pp.push.protocol.PpPushRequestProto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Slf4j
@Component
public class PpLiveServiceRemote extends RemoteServiceInvokeFacade implements ILiveServiceRemote {


    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private PpAddLiveAdapter ppAddLiveAdapter;

    @Autowired
    private UserService userService;

    @MyAutowired
    private ILiveRoomServiceRemote iLiveRoomServiceRemote;

    @Autowired
    private UserBehaviorService userBehaviorService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private LiveStateService liveStateService;

    @Autowired
    private LiveConfig config;

    @Autowired
    private LiveNewService liveNewService;

    @Autowired
    private PpBatchGetLastEditLivesAdapter getLastEditLivesAdapter;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private PpGetLiveAdapter ppGetLiveAdapter;

    @Autowired
    private DecorateUserService decorateUserService;

    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getExecutors(
            "ppUserCachePool", 1, 1, new LinkedBlockingQueue<Runnable>(1000), ThreadUtils.getCallerRunsPolicyExecutionHandler());

    private final ListeningExecutorService refreshLocalCachePool = MoreExecutors.listeningDecorator(THREAD_POOL_EXECUTOR);

    /**
     * 用户直播间缓存
     */
    private final LoadingCache<Long, Optional<GetLiveRemoteResult>> LIVE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<GetLiveRemoteResult>>() {
                @Override
                public Optional<GetLiveRemoteResult> load(Long liveId) throws Exception {
                    // 获取直播间信息，该接口业务侧无缓存
                    Result<GetLiveRemoteResult> result = getLive(GetLiveRemoteParam.builder().liveId(liveId).build());
                    if (result.rCode() != 0) {
                        log.warn("PpLiveServiceRemote LIVE_CACHE load error, liveId:{}, rCode:{}", liveId, result.rCode());
                        // 加载失败直接返回null，避免该时间段内的请求一直失败
                        return Optional.empty();
                    }
                    return Optional.ofNullable(result.target());
                }

                @Override
                public ListenableFuture<Optional<GetLiveRemoteResult>> reload(Long liveId, Optional<GetLiveRemoteResult> oldValue) {
                    //调用时如果缓存已经达到刷新时间，则先返回旧的数据，并且触发刷新请求，下次调用get方法时，如果缓存已经刷新，则返回新的数据
                    return refreshLocalCachePool.submit(() -> {
                        try {
                            Result<GetLiveRemoteResult> result = getLive(GetLiveRemoteParam.builder().liveId(liveId).build());
                            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                                return Optional.ofNullable(result.target());
                            }
                        } catch (Exception e) {
                            log.error("ppLiveServiceRemote LIVE_CACHE reload error, liveId:{}", liveId, e);
                        }
                        // 加载失败直接返回null，避免该时间段内的请求一直失败
                        return Optional.empty();
                    });
                }
            });


    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.PP.equals(evnEnum);
    }

    @Override
    public Result<GetLiveRemoteResult> getLive(GetLiveRemoteParam param) {
        return execute(param, PpGetLiveAdapter.class
                , queryUserParam -> getSpringInterfaceProxyBean(LiveNewService.class).getLiveNoCache((LiveNewProto.GetLiveParams) queryUserParam));
    }


    @Override
    public Result<GetLiveRoomBackGroundResult> getLiveRoomBackGround(long njId, long userId) {
        GetUsingDecorateStockReq req = new GetUsingDecorateStockReq();
        req.setOwnerId(njId);
        req.setType(DecorateTypeEnum.BACKGROUND.getType());
        try {
            Result<DecorateUserProto.ResponseGetUsingDecorateStock> result =
                    decorateUserService.getUsingDecorateStock(JsonUtil.dumps(req));
            int rCode = result.rCode();
            if (rCode == DecorateTipEnum.NOT_DATA.getCode()) {
                return new Result<>(GET_LIVE_BACKGROUND_NO_FIND_ERROR, null);
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("pp getLiveRoomBackGround.getUsingDecorateStock error, rCode={}, userId={}", rCode, userId);
                return new Result<>(GET_LIVE_BACKGROUND_ERROR, null);
            }
            if (!result.target().hasUsingDecorateStockDtoStr()) {
                return new Result<>(GET_LIVE_BACKGROUND_NO_FIND_ERROR, null);
            }
            UsingDecorateStockDto usingBg = JsonUtil.loads(result.target().getUsingDecorateStockDtoStr(), GetUsingDecorateStokResp.class)
                    .getUsingDecorateStockDto();
            String backgroundColor = "";
            List<JSONObject> extInfos = JSONObject.parseArray(usingBg.getDecorateDto().getExtInfo(), JSONObject.class);
            if (extInfos != null && !extInfos.isEmpty()) {
                for (JSONObject extInfo : extInfos) {
                    if ("backgroundColor".equals(extInfo.getString("key"))) {
                        backgroundColor = "#" + extInfo.getString("value");
                        break;
                    }
                }
            }

            String liveBgUrl = usingBg.getDecorateDto().getThumbUrl();
            liveBgUrl = liveBgUrl.startsWith("http") ? liveBgUrl : ConfigUtil.getImageCdn() + liveBgUrl;

            //动态背景图
            String materialSvgaUrl = UrlUtils.getImageUrl(commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost(), usingBg.getDecorateDto().getMaterialUrl());

            LiveRoomBackGround liveRoomBackGround = LiveRoomBackGround.builder()
                    .materialUrl(liveBgUrl)
                    .materialSvgaUrl(materialSvgaUrl)
                    .effectColor(backgroundColor).build();
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetLiveRoomBackGroundResult.builder()
                    .liveRoomBackGround(liveRoomBackGround).build());
        } catch (Exception e) {
            log.warn("pp getLiveRoomBackGround error njId={}", njId, e);
            return new Result<>(GET_LIVE_BACKGROUND_ERROR, null);
        }
    }


    @Override
    public Result<GetLiveRemoteResult> getLiveByCache(GetLiveRemoteParam param) {
        Optional<GetLiveRemoteResult> cacheRes = LIVE_CACHE.getUnchecked(param.getLiveId());
        if (!cacheRes.isPresent()) {
            //如果获取到空的，清空空缓存，避免某个时间段的所有查询直播间信息请求都中断了
            LIVE_CACHE.invalidate(param.getLiveId());
            log.warn("PpLiveServiceRemote getLiveByLocalCache fail, liveId:{}", param.getLiveId());
            return new Result<>(ILiveServiceRemote.GET_LIVE_ERROR, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, cacheRes.get());
    }

    @Override
    public Result<List<LiveBean>> getLives(List<Long> liveIds) {
        PpGetLivesAdapter adapter = SpringUtils.getBean(PpGetLivesAdapter.class);
        Result<LiveNewProto.ResponseGetLives> lives = getSpringInterfaceProxyBean(LiveNewService.class)
                .getLives(adapter.convertParam(liveIds));
        if (lives.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getLives error, liveIds.size={}`rCode={}", liveIds.size(), lives.rCode());
        }
        return adapter.convertResult(lives);
    }

    @Override
    public Result<List<LiveBean>> getLivesByLiveRoomIds(List<Long> liveRoomIds) {
        PpGetLivesByLiveRoomIdsAdapter adapter = SpringUtils.getBean(PpGetLivesByLiveRoomIdsAdapter.class);
        Result<LiveNewProto.ResponseGetLivesByLiveRoomIds> lives = getSpringInterfaceProxyBean(LiveNewService.class)
                .getLivesByLiveRoomIds(adapter.convertParam(liveRoomIds));
        if (lives.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getLives error, liveRoomIds.size={}`rCode={}", liveRoomIds.size(), lives.rCode());
        }
        return adapter.convertResult(lives);
    }

    @Override
    public Result<GetLiveRemoteResult> getLastEditLive(GetLastEditLiveRemoteParam param) {
        try {
            Result<LiveNewProto.ResponseGetLastEditLive> responseGetLastEditLiveResult = getSpringInterfaceProxyBean(LiveNewService.class).getLastEditLive(LiveNewProto.GetLastEditLiveParams.newBuilder()
                    .setLiveRoomId(param.getLiveRoomId())
                    .setAppId(param.getAppId())
                    .build());
            if (responseGetLastEditLiveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("PpLiveServiceRemote getLives error, liveRoomIds.liveRoomId={}``appId={}`rCode={}", param.getLiveRoomId(), param.getAppId(), responseGetLastEditLiveResult.rCode());
                return new Result<>(GET_LAST_EDIT_LIVE_ERROR, null);
            }

            LiveNewProto.Live lastLive = responseGetLastEditLiveResult.target().getLive();
            BusinessConfig businessConfig = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId());
            LiveBean liveBean = PpLiveBeanAdapter.convert(lastLive, businessConfig.getCdnHost());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetLiveRemoteResult.builder().liveBean(liveBean).build());
        } catch (Exception e) {
            log.warn("PpLiveServiceRemote getLives error, liveRoomIds.liveRoomId={}``appId={}", param.getLiveRoomId(), param.getAppId(), e);
        }

        return new Result<>(GET_LAST_EDIT_LIVE_ERROR, null);
    }


    @Override
    public Result<Long> addLive(AddLiveRemoteParam param) {
        LiveNewProto.AddLiveParams addLiveParams = ppAddLiveAdapter.convertParam(param);
        Result<LiveNewProto.ResponseAddLive> resp = getSpringInterfaceProxyBean(LiveNewService.class).addLive(addLiveParams);

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote addLive error, param:{}, rCode:{}", JsonUtil.dumps(param), resp.rCode());
            if (resp.rCode() == LiveNewService.ADD_LIVE_ERR_NOT_OPEN_LIVE_ROOM) {
                return new Result<>(ILiveServiceRemote.ADD_LIVE_NOT_OPEN_LIVE_ROOM, null);
            } else if (resp.rCode() == LiveNewService.ADD_LIVE_ERR_LIVE_ROOM_BLOCK) {
                return new Result<>(ILiveServiceRemote.ADD_LIVE_ERR_LIVE_ROOM_BLOCK, null);
            }
            return new Result<>(ILiveServiceRemote.ADD_LIVE_ERROR, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.target().getId());
    }

    @Override
    public Result<Void> closeLive(CloseLiveRemoteParam param) {
        Result<LiveNewProto.ResponseCloseLive> closeLiveResult = getSpringInterfaceProxyBean(LiveNewService.class).closeLive(LiveNewProto.CloseLiveParams
                .newBuilder().setAppId(BusinessEvnEnum.PP.getAppId())
                .setSource(LiveNewProto.CloseSource.WAVE)
                .setLiveId(param.getLiveId()).build());
        if (closeLiveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote closeLive not success, liveId:{}, rCode:{}", param.getLiveId(), closeLiveResult.rCode());
        }

        if (closeLiveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (closeLiveResult.rCode() == LiveNewService.CLOSE_LIVE_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.CLOSE_LIVE_ERR_NOT_FOUND, null);
            }
            if (closeLiveResult.rCode() == LiveNewService.CLOSE_LIVE_ERR_EXIST_SMALL_GAME) {
                return new Result<>(ILiveServiceRemote.CLOSE_LIVE_ERR_EXIST_SMALL_GAME, null);
            }
            return new Result<>(ILiveServiceRemote.CLOSE_LIVE_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> deleteLive(DeleteLiveRemoteParam param) {
        Result<LiveNewProto.ResponseDeleteLive> deleteLiveResult = getSpringInterfaceProxyBean(LiveNewService.class)
                .deleteLive(LiveNewProto.DeleteLiveParams.newBuilder().setLiveId(param.getLiveId()).setAppId(param.getAppId()).build());
        if (deleteLiveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote deleteLive not success, liveId:{}, rCode:{}", param.getLiveId(), deleteLiveResult.rCode());
        }
        return new Result<>(deleteLiveResult.rCode(), null);
    }

    @Override
    public Result<Void> editLive(EditLiveRemoteParam editParam) {
        LiveNewService liveService = getSpringInterfaceProxyBean(LiveNewService.class);
        String tags = "";
        if (!Objects.isNull(editParam.getLiveBean()) && StringUtils.isNotBlank(editParam.getLiveBean().getExtraJson())) {
            try {
                tags = JsonUtil.loads(editParam.getLiveBean().getExtraJson(), PpLiveExtraBean.class).getTags();
            } catch (Exception e) {
                log.error("PpLiveServiceRemote editLive|转换tag失败,param: " + JsonUtil.dumps(editParam), e);
            }
        }
        editParam.setTags(tags);
        PpEditLiveAdapter adapter = getMethodParamAndResultAdapter(PpEditLiveAdapter.class);
        LiveNewProto.EditLiveParams editLiveParams = adapter.convertParam(editParam);
        Result<LiveNewProto.ResponseEditLive> result = liveService.editLive(editLiveParams);
        int rCode = result.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {

            if (rCode == LiveNewService.EDIT_LIVE_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.EDIT_LIVE_NOT_FOUND, null);
            } else if (rCode == LiveNewService.EDIT_LIVE_ERR_LIVE_ROOM_BLOCK) {
                return new Result<>(ILiveServiceRemote.EDIT_LIVE_ERR_LIVE_ROOM_BLOCK, null);
            }
            log.warn("PpLiveServiceRemote editLive error param:{}, rCode:{}", JsonUtil.dumps(editParam), rCode);
            return new Result<>(ILiveServiceRemote.EDIT_LIVE_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);

    }

    @Override
    public Result<Long> getLastLiveIdByParam(GetLatestLiveIdRemoteParam latestLiveIdRemoteParam) {
        LiveNewProto.GetUpToDateLiveIdParams.Builder builder = LiveNewProto.GetUpToDateLiveIdParams.newBuilder();
        if (latestLiveIdRemoteParam.getLiveRoomId() != null) {
            builder.setLiveRoomId(latestLiveIdRemoteParam.getLiveRoomId());
        }
        if (latestLiveIdRemoteParam.getUserId() != null) {
            builder.setUserId(latestLiveIdRemoteParam.getUserId());
        }

        builder.setAppId(BusinessEvnEnum.PP.getAppId());
        Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache> resp = getSpringInterfaceProxyBean(LiveNewService.class)
                .getUpToDateLiveIdNotCache(builder.build());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getLastLiveIdByParam error, param:{}, rCode:{}", JsonUtil.dumps(latestLiveIdRemoteParam), resp.rCode());
        }

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (resp.rCode() == LiveNewService.GET_UP_TO_DATE_LIVE_ID_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.GET_LAST_LIVE_ID_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.GET_LAST_LIVE_ID_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.target().getLiveId());
    }

    public Result<String> getLiveAffiche(Live live) {
        Long liveRoomId = live.getLiveRoomId();
        Result<LiveAfficheProto.ResponseGetLiveAffiche> resp = getSpringInterfaceProxyBean(LiveAfficheService.class).getLiveAffiche(liveRoomId);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getLiveAffiche error, liveRoomId:{}, rCode:{}", liveRoomId, resp.rCode());
            return new Result<>(ILiveServiceRemote.GET_LIVE_AFFICHE_ERROR, null);
        }
        LiveAfficheProto.ResponseGetLiveAffiche liveAffiche = resp.target();
        if (liveAffiche == null || liveAffiche.getId() <= 0) {
            return new Result<>(ILiveServiceRemote.GET_LIVE_AFFICHE_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, liveAffiche.getContent());
    }

    @Override
    public Result<GetFirstLiveResult> getFirstLive(GetFirstLiveParam param) {
        Result<LiveNewProto.ResponseGetFirstLivesNotCache> result = getSpringInterfaceProxyBean(LiveNewService.class)
                .getFirstLivesNotCache(param.getLiveRoomId());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getFirstLive error, param:{}, rCode:{}", JsonUtil.dumps(param), result.rCode());
            return new Result<>(ILiveServiceRemote.GET_FIRST_LIVE_ID_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetFirstLiveResult.builder().liveId(result.target().getLive().getId()).build());

    }

    @Override
    public Result<Void> checkUserLivePassword(CheckUserLivePasswordRequestParam param) {
        //PP的房间没有加锁
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<UserRealNameAuthStatus> checkPermission(long userId, Integer appId) {

        if (!liveConfig.getPp().isPubLiveCheckPermissionSwitch()) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, UserRealNameAuthStatus.builder().status(fm.lizhi.ocean.wave.user.constant.UserRealNameAuthStatus.AUTH.getAuthStatus()).build());
        }
        return userService.getUserRealNameAuthStatus(userId, appId);
    }

    @Override
    public Result<GetHomeLiveListResult> getHomeLiveRoomList(GetHomeLiveListRequestParam param) {
        long userId = param.getUserId();
        List<Long> liveRoomIds = Lists.newArrayList();
        List<Long> roomUserIds = Lists.newArrayList();

        // 厅主
        Result<GetLiveRoomResult> getRoomResp = iLiveRoomServiceRemote.getLiveRoomByUserId(userId);
        if (getRoomResp.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(getRoomResp.target())) {
            LiveRoomBean liveRoomBean = getRoomResp.target().getLiveRoomBean();
            if (Objects.equals(liveRoomBean.getUserId(), userId) && liveRoomBean.getStatus() != LiveRoomStatusEnum.BLOCK) {
                liveRoomIds.add(liveRoomBean.getId());
                roomUserIds.add(liveRoomBean.getUserId());
            }
        }


        // 查询用户的家族角色
        Result<fm.pp.family.protocol.FamilyServiceProto.ResponseGetUserFamily> result = getSpringInterfaceProxyBean(FamilyService.class).getUserFamily(userId, true);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getHomeLiveList - getUserFamily fail, userId:{}, rCode:{}", userId, result.rCode());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                    .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());
        }

        FamilyServiceProto.UserFamilyInfo userFamilyInfo = result.target().getUserFamilyInfo();
        //是否是家族长
        boolean isFamily = userFamilyInfo.getIsFamily();
        //是否是陪玩
        boolean isPlayer = userFamilyInfo.getIsPlayer();

        if (!isFamily && !isPlayer) {
            log.info("PpLiveServiceRemote getHomeLiveList - user is not family or player, userId:{}", userId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                    .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());
        }


        if (isFamily) {
            //查询家族下全部签约主播
            Result<FamilyServiceProto.ResponseGetFamilyNj> getSignNjListResp = getSpringInterfaceProxyBean(FamilyService.class).getFamilyNj(userFamilyInfo.getFamilyId());
            if (getSignNjListResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("PPLiveServiceRemote getHomeLiveList - getFamilySignNjIds fail, userId:{}, rCode:{}", userId, getSignNjListResp.rCode());
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                        .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());
            }
            String signNjListJson = getSignNjListResp.target().getData();
            if (StringUtils.isBlank(signNjListJson) || "[]".equals(signNjListJson)) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                        .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());
            }

            List<FamilyNjBean> familyNjBeans = JSONObject.parseArray(signNjListJson, FamilyNjBean.class);

            for (FamilyNjBean familyNjBean : familyNjBeans) {
                getRoomResp = iLiveRoomServiceRemote.getLiveRoomByUserId(familyNjBean.getNjId());
                if (getRoomResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                    log.warn("PPLiveServiceRemote getHomeLiveList - getLiveRoomByUserId fail, userId:{}, rCode:{}", userId, getRoomResp.rCode());
                    continue;
                }
                LiveRoomBean liveRoomBean = getRoomResp.target().getLiveRoomBean();
                if (liveRoomBean.getStatus() == LiveRoomStatusEnum.BLOCK) {
                    log.warn("PPLiveServiceRemote getHomeLiveList - liveRoom is block, userId:{}, liveRoomId:{}", userId, liveRoomBean.getId());
                    continue;
                }
                liveRoomIds.add(liveRoomBean.getId());
                roomUserIds.add(liveRoomBean.getUserId());
            }
        }
        //查询陪玩
        if (isPlayer) {
            long njId = userFamilyInfo.getNjId();
            getRoomResp = iLiveRoomServiceRemote.getLiveRoomByUserId(njId);
            if (getRoomResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("PPLiveServiceRemote getHomeLiveList - getLiveRoomByUserId fail, userId:{}, rCode:{}", userId, getRoomResp.rCode());
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                        .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());
            }
            LiveRoomBean liveRoomBean = getRoomResp.target().getLiveRoomBean();
            liveRoomIds.add(liveRoomBean.getId());
            roomUserIds.add(liveRoomBean.getUserId());
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetHomeLiveListResult.builder()
                .liveRoomIds(liveRoomIds).roomUserIds(roomUserIds).build());

    }

    @Override
    public Result<GetLiveModeRemoteResult> getLiveMode(GetLiveModeRemoteParam param) {
        LiveNewProto.GetLiveModeRequest.Builder request = LiveNewProto.GetLiveModeRequest.newBuilder();
        request.setLiveId(param.getLiveId());

        Result<LiveNewProto.ResponseGetLiveModeByTime> result = getSpringInterfaceProxyBean(LiveNewService.class).getLiveModeByTime(request.build());
        log.info("pp getLiveMode liveId={},rCode={}", param.getLiveId(), result.rCode());
        int liveModeId = LiveModeEnum.NORMAL_AMUSEMENT.getLiveMode();
        if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            liveModeId = result.target().getRecord().getLiveModeId();
        }
        LiveModeEnum liveModeEnum = BizLiveModeMappingEnum.bizValue2Wave(liveModeId);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,
                GetLiveModeRemoteResult.builder()
                        .liveModeEnum(liveModeEnum)
                        .bizLiveModeId(liveModeId)
                        .build());
    }

    /**
     * 获取音频相关数据
     *
     * @param param
     */
    @Override
    public Result<GetLiveVoiceDataRemoteResult> getLiveVoiceData(GetLiveVoiceDataRemoteParam param) {
        int myVoiceUserId = getVoiceUserId(param.getCurrentUserId());
        GetLiveVoiceDataRemoteResult.GetLiveVoiceDataRemoteResultBuilder builder = GetLiveVoiceDataRemoteResult.builder();
        builder.myVoiceUserId(myVoiceUserId);
        Optional<VoiceUtils.StreamData> streamData = VoiceUtils.parseStreamData(param.getStreamUrl());
        if (streamData.isPresent()) {
            if (StringUtils.isBlank(streamData.get().getAppKey())) {
                builder.appKey(liveConfig.getPp().getDefaultStreamAppKey());
            } else {
                builder.appKey(streamData.get().getAppKey());
            }
            builder.channelId(streamData.get().getChannelId());
        } else {
            builder.appKey(liveConfig.getPp().getDefaultStreamAppKey());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    @Override
    public Result<String> getNormalVoiceAppKey() {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, liveConfig.getPp().getNormalVoiceAppKey());
    }

    @Override
    public Result<Long> getLiveIdIfInRoom(long userId) {
        return execute(userId, PpGetLiveIdIfInRoomAdapter.class,
                followUserParam -> getSpringInterfaceProxyBean(UserBehaviorService.class).getUserBehaviors((LiveBehaviorProto.UserBehaviorsParams) followUserParam));
    }

    @Override
    public Result<UserLiveRoomStatus> getUserLiveRoomStatus(long userId, boolean noCache) {
        try {
            List<LiveBehaviorProto.UserBehavior> behaviorsList = null;
            if (noCache) {
                Result<LiveBehaviorProto.ResponseGetUserBehaviorsNoCache> userBehaviorsResult = userBehaviorService.getUserBehaviorsNoCache(LiveBehaviorProto.UserBehaviorsParams.newBuilder()
                        .setUserId(userId)
                        .build());
                if (userBehaviorsResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                    return new Result<>(ILiveServiceRemote.GET_USER_LIVE_ROOM_STATUS_ERROR, null);
                }
                behaviorsList = userBehaviorsResult.target().getBehaviorsList();
            } else {
                Result<LiveBehaviorProto.ResponseGetUserBehaviors> userBehaviorsResult = userBehaviorService.getUserBehaviors(LiveBehaviorProto.UserBehaviorsParams.newBuilder()
                        .setUserId(userId)
                        .build());
                if (userBehaviorsResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                    return new Result<>(ILiveServiceRemote.GET_USER_LIVE_ROOM_STATUS_ERROR, null);
                }
                behaviorsList = userBehaviorsResult.target().getBehaviorsList();
            }

            if (CollectionUtils.isEmpty(behaviorsList)) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }
            LiveBehaviorProto.UserBehavior majorBehavior = getMajorBehavior(behaviorsList, userId);
            if (Objects.isNull(majorBehavior)) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }
            if (majorBehavior.getType().getNumber() == LiveBehaviorProto.BehaviorType.LIVE_LISTEN_VALUE
                    || majorBehavior.getType().getNumber() == LiveBehaviorProto.BehaviorType.LIVE_OPEN_VALUE
                    || majorBehavior.getType().getNumber() == LiveBehaviorProto.BehaviorType.SERVING_WHEAT_VALUE) {
                LiveBehaviorProto.LiveListenBehaviorData liveListenBehaviorData = LiveBehaviorProto.LiveListenBehaviorData.parseFrom(majorBehavior.getData());
                Result<GetLiveRemoteResult> liveResult = this.getLiveByCache(GetLiveRemoteParam.builder().liveId(liveListenBehaviorData.getLiveId()).build());
                if (liveResult.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(liveResult.target().getLiveBean())) {
                    UserLiveRoomStatus userLiveRoomStatus = new UserLiveRoomStatus();
                    userLiveRoomStatus.setRoomName(liveResult.target().getLiveBean().getName());
                    userLiveRoomStatus.setLiveId(liveResult.target().getLiveBean().getId());
                    userLiveRoomStatus.setRoomUserId(liveResult.target().getLiveBean().getUserId());
                    userLiveRoomStatus.setOnlineStatus(convertBehaviorStatus(majorBehavior.getType().getNumber()));
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, userLiveRoomStatus);
                }

            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        } catch (Exception e) {
            log.error("pp UserBehaviorManager getPartialLiveFollowUsers getLiveRoom error userId={}", userId, e);
            return new Result<>(ILiveServiceRemote.GET_USER_LIVE_ROOM_STATUS_ERROR, null);
        }
    }

    private LiveBehaviorProto.UserBehavior getMajorBehavior(List<LiveBehaviorProto.UserBehavior> behaviorsList,
                                                            long userId) {
        LiveBehaviorProto.UserBehavior majorBehavior = null;
        //如果是只有一条数据，且是在麦上，说明是脏数据
        if (behaviorsList.size() == 1 && behaviorsList.get(0).getType().getNumber() == LiveBehaviorProto.BehaviorType.SERVING_WHEAT_VALUE) {
            return null;
        }

        for (LiveBehaviorProto.UserBehavior userBehavior : behaviorsList) {
            if (null == majorBehavior) {
                majorBehavior = userBehavior;
                continue;
            }
            PpBehaviorStatusEnum newBehavior = PpBehaviorStatusEnum.getBehaviorStatusEnum(userBehavior.getType().getNumber());
            PpBehaviorStatusEnum oriBehavior = PpBehaviorStatusEnum.getBehaviorStatusEnum(majorBehavior.getType().getNumber());
            if (newBehavior == null || oriBehavior == null) {
                //无法识别的行为
                continue;
            }
            int newPriority = newBehavior.getPriority();
            int origPriority = oriBehavior.getPriority();
            if (newPriority > origPriority) {
                majorBehavior = userBehavior;
            }
        }
        return majorBehavior;
    }

    @Override
    public Result<Void> requestEnterContinue(Long userId, Long njId) {
        if (Objects.equals(userId, njId)) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        try {
            Result<Void> result = getSpringInterfaceProxyBean(EnterNoticeService.class).requestEnterContinue(userId, njId);
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.info("PpLiveServiceRemote requestEnterContinue success userId:{}, njId:{}", userId, njId);
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            } else {
                log.warn("PpLiveServiceRemote requestEnterContinue fail userId:{}, njId:{}, rCode:{}", userId, njId, result.rCode());
            }
        } catch (Exception e) {
            log.error("PpLiveServiceRemote requestEnterContinue error, userId:" + userId + " njId" + njId, e);
        }

        return new Result<>(ILiveServiceRemote.REQUEST_ENTER_CONTINUE_ERROR, null);
    }

    @Override
    public Result<Void> startLive(StartLiveRequestParam param) {
        int liveMode = param.getMode() == 0 ? fm.lizhi.live.room.pp.enums.LiveModeEnum.AMUSEMENT_MODE.getLiveModeId() : param.getMode();
        Result<LiveNewProto.ResponseStartLive> resp = getSpringInterfaceProxyBean(LiveNewService.class).startLive(LiveNewProto.StartLiveParams.newBuilder()
                .setAppId(BizRouterEnum.PP.getValue())
                .setLiveId(param.getLiveId())
                .setIp(param.getIp())
                .setMode(liveMode)
                .build());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote startLive error, param:{}, rCode:{}", JsonUtil.dumps(param), resp.rCode());
            if (resp.rCode() == LiveNewService.START_LIVE_ERR_NOT_EXIST
                    || resp.rCode() == LiveNewService.START_LIVE_ERR_ILLEGAL_STATUS) {
                return new Result<>(ILiveServiceRemote.START_LIVE_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.START_LIVE_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);

    }

    @Override
    public Result<GetNotifyFansLimitResult> getLiveNotifyFansLimit(long userId) {
        Result<LiveNotifyLimitProto.ResponseGetNotifyLimit> result = getSpringInterfaceProxyBean(LiveNotifyLimitService.class).getNotifyLimit(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp LiveServiceRemote getLiveNotifyFansLimit error, userId={}`rCode={}", userId, result.rCode());
            return new Result<>(ILiveServiceRemote.GET_LIVE_NOTIFY_FANS_LIMIT_ERROR, null);
        }
        GetNotifyFansLimitResult.GetNotifyFansLimitResultBuilder builder = GetNotifyFansLimitResult.builder();
        builder.limit(result.target().getLimit())
                .limitInterval(result.target().getLimitInterval() / 1000)
                .remainTime(result.target().getRemianTime() / 1000);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    @Override
    public Result<Void> markNotifyFansLimit(long userId) {

        Result<LiveNotifyLimitProto.ResponseMarkNotifyLimit> result = getSpringInterfaceProxyBean(LiveNotifyLimitService.class).markNotifyLimit(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp LiveServiceRemote markNotifyFansLimit error, userId={}`rCode={}", userId, result.rCode());
            return new Result<>(ILiveServiceRemote.MARK_LIVE_NOTIFY_FANS_LIMIT_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> pushNotifyFans(long userId) {
        PpPushRequestProto.NotifyFansRequest.Builder builder = PpPushRequestProto.NotifyFansRequest.newBuilder();
        builder.setUserId(userId);
        Result<PpPushProto.ResponseNotifyFans> result = getSpringInterfaceProxyBean(PpPushService.class).notifyFans(builder.build());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp LiveServiceRemote pushNotifyFans error, userId={}`rCode={}", userId, result.rCode());
            return new Result<>(ILiveServiceRemote.PUSH_LIVE_NOTIFY_FANS_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GetRecommendLiveListResponse> getRecommendLiveList(long liveId) {
        //热搜分类ID
        long categoryId = config.getPp().getHotTabCategoryId();
        int count = config.getPp().getMinDialogOptLive();
        LiveStateProto.RankLiveIdParams params = LiveStateProto.RankLiveIdParams.newBuilder().setCategory(categoryId).setNum(count).build();
        Result<LiveStateProto.ResponseGetRankLiveIds> result = liveStateService.getRankLiveIds(params);
        if (result.rCode() != 0) {
            log.warn("pp LiveServiceRemote getRecommendLiveList error, liveId={}`rCode={}", liveId, result.rCode());
            return new Result<>(ILiveServiceRemote.GET_RECOMMEND_LIVE_LIST_ERROR, null);
        }

        List<Long> idsList = result.target().getLiveIdsList();
        List<Long> allRecommendLists = new ArrayList<>(idsList);

        if (liveConfig.getPp().isOpenRecommendConfig()) {
            allRecommendLists.addAll(liveConfig.getPp().getRecommendLiveIds());
        }

        List<GetRecommendLiveListResponse.RecommendLive> recommendLives = new ArrayList<>();
        for (Long id : allRecommendLists) {
            recommendLives.add(GetRecommendLiveListResponse.RecommendLive.builder().liveId(id).build());
        }

        GetRecommendLiveListResponse listResult = GetRecommendLiveListResponse.builder().liveList(recommendLives).build();
        return new Result<>(0, listResult);
    }

    @Override
    public Result<SearchLiveResponse> searchLive(SearchLiveRequest param) {
        SearchProto.Identity identity = SearchProto.Identity.newBuilder()
                .setUserId(param.getUserId())
                .setDeviceId(param.getDeviceId())
                .setClientVersion(param.getClientVersion())
                .setDeviceType(SearchProto.DeviceTypeEnum.Web)
                .build();

        SearchProto.SearchLiveRequest.Builder builder = SearchProto.SearchLiveRequest.newBuilder();
        builder.setContent(param.getContent());
        if (!StringUtils.isEmpty(param.getPerformanceId())) {
            builder.setCursor(param.getPerformanceId());
        }
        builder.setIdentity(identity);
        log.info("pp searchLive, request, content={}, cursor={}, identity={}", param.getContent(), param.getPerformanceId(), JsonFormat.printToString(identity));
        Result<SearchProto.ResponseSearchLive> result = searchService.searchLive(builder.build());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp searchLive, rCode={}, content={}, cursor={}, identity={}",
                    result.rCode(), param.getContent(), param.getPerformanceId(), identity);
            return new Result<>(ILiveServiceRemote.SEARCH_LIVE_ERROR, null);
        }
        // 组装返回数据
        List<SearchProto.Live> liveProtoList = result.target().getLiveList();
        List<SearchLiveResponse.SearchLiveVo> liveList = new ArrayList<>();

        liveProtoList.forEach(liveProto -> {
            String resultCover = ImgUtil.getImageThumbUrl(liveProto.getImage(), PhotoConst.THUMB_WIDTH, PhotoConst.THUMB_HEIGHT);
            liveList.add(SearchLiveResponse.SearchLiveVo.builder()
                    .liveId(liveProto.getLiveId())
                    .band(liveProto.getBand())
                    .title(liveProto.getTitle())
                    .image(UrlUtils.addCdnHost(commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost(), resultCover))
                    .totalPopularity(liveProto.getTotalPopularity())
                    .password("").build());
        });

        SearchLiveResponse searchLiveResult = SearchLiveResponse.builder()
                .total(result.target().getTotal())
                .isLastPage(result.target().getIsLastPage())
                .performanceId(result.target().getCursor())
                .liveList(liveList).build();

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, searchLiveResult);
    }

    @Override
    public Result<BatchGetLastEditLivesByUserIdsResult> batchGetLastEditLivesByUserIds(List<Long> userIds) {
        Result<LiveNewProto.ResponseGetLivesByUserIds> lives = liveNewService.getLivesByUserIds(getLastEditLivesAdapter.convertParam(userIds));
        int rCode = lives.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp.LiveServiceRemote batchGetLastEditLivesByUserIds error, userIds.size={}`rCode={}", userIds.size(), rCode);
            if (rCode == LiveNewService.GET_LIVE_NO_CACHE_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.GET_LIVE_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.GET_LIVE_ERROR, null);
        }

        if (lives.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp.LiveServiceRemote getLives error, userIds.size={}`rCode={}", userIds.size(), lives.rCode());
        }

        return getLastEditLivesAdapter.convertResult(lives);
    }

    @Override
    public Result<GetLiveRemoteResult> getLiveByUserId(long userId) {
        Result<LiveNewProto.ResponseGetLivesByUserIds> getLivesByUserIdsResult = getSpringInterfaceProxyBean(LiveNewService.class).getLivesByUserIds(LiveNewProto.GetLivesByUserIdsParams.newBuilder().addUserId(userId).build());
        if (getLivesByUserIdsResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpLiveServiceRemote getLiveByUserId error, userId={}`rCode={}", userId, getLivesByUserIdsResult.rCode());
            return new Result<>(ILiveServiceRemote.GET_LIVE_BY_USER_ID_ERROR, null);
        }

        List<LiveNewProto.Live> lives = getLivesByUserIdsResult.target().getLiveList();
        if (CollectionUtils.isEmpty(lives)) {
            log.info("PpLiveServiceRemote getLiveByUserId not found, userId={}", userId);
            return new Result<>(ILiveServiceRemote.GET_LIVE_BY_USER_ID_NOT_FOUND, null);
        }

        BusinessConfig businessConfig = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId());
        LiveBean liveBean = PpLiveBeanAdapter.convert(lives.get(0), businessConfig.getCdnHost());

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetLiveRemoteResult.builder()
                .liveBean(liveBean).defaultWelcomeMsg(liveConfig.getPp().getDefaultLiveWelcomeMsg()).build());
    }

    @Override
    public Result<LiveBean> getLastEditLiveNoCache(long liveRoomId) {
        LiveNewProto.GetLastEditLiveParams params = LiveNewProto.GetLastEditLiveParams.newBuilder().setLiveRoomId(liveRoomId).build();
        Result<LiveNewProto.ResponseGetLastEditLiveNoCache> result = liveNewService.getLastEditLiveNoCache(params);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || result.target() == null) {
            log.warn("pp.getLastEditLiveNoCache,liveRoomId={},rCode={}", liveRoomId, result.rCode());
            return new Result<>(LiveService.GET_LIVE_ROOM_NEWEST_LIVE_ERROR_NOT_FOUND, null);
        }
        LiveNewProto.Live live = result.target().getLive();
        LiveBean liveBean = PpLiveBeanAdapter.convert(live, commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, liveBean);
    }

    /**
     * 获取直播扩展信息
     * @param liveId
     * @return
     */
    @Override
    public Result<GetLiveExtraInfoResult> getLiveExtraInfo(long liveId) {
        return null;
    }

    private int getVoiceUserId(Long userId) {
        try {
            Result<ChannelLiveProto.ResponseGetUid32> result = getSpringInterfaceProxyBean(PpChannelLiveService.class).getUid32(userId, 0, 0);
            int rCode = result.rCode();
            if (rCode != 0) {
                log.warn("getUid32RCode={}`uid={}", rCode, userId);
                return Math.abs(Long.hashCode(userId));
            }
            return result.target().getUid32();
        } catch (Exception e) {
            log.warn("getUid32ErrorUid={}", userId, e);
            return Math.abs(Long.hashCode(userId));
        }
    }


    /**
     * 转换行为状态数据
     *
     * @param type 类型
     * @return 结果
     */
    private int convertBehaviorStatus(int type) {
        if (type == LiveBehaviorProto.BehaviorType.LIVE_OPEN_VALUE) {
            return OnlineStatusEnum.LIVING.getValue();
        } else if (type == LiveBehaviorProto.BehaviorType.SERVING_WHEAT_VALUE) {
            return OnlineStatusEnum.ON_MIC.getValue();
        } else if (type == LiveBehaviorProto.BehaviorType.LIVE_LISTEN_VALUE) {
            return OnlineStatusEnum.LISTEN.getValue();
        }
        return OnlineStatusEnum.ONLINE.getValue();
    }

}
