package fm.lizhi.ocean.wave.verify.core.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取实名认证参数结果
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAuthParameterResult {

    /**
     * 支付宝认证url
     */
    private String certifiedUrl;
    /**
     * 认证id（后续发起查询使用）
     */
    private String certifyId;
}
