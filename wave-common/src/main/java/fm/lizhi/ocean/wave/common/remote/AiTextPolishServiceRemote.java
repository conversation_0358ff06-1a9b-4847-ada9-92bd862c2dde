package fm.lizhi.ocean.wave.common.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.godzilla.api.model.common.AiPolishDataResult;
import fm.lizhi.ocean.godzilla.api.model.common.TextPolishStyle;
import fm.lizhi.ocean.godzilla.api.model.request.RequestBatchGetTextPolishStyle;
import fm.lizhi.ocean.godzilla.api.model.request.RequestSubmitPolishText;
import fm.lizhi.ocean.godzilla.api.model.response.ResponseBatchGetTextPolishStyle;

import fm.lizhi.ocean.godzilla.api.model.response.ResponsePopPolishResult;
import fm.lizhi.ocean.godzilla.api.model.response.ResponseSubmitPolishText;
import fm.lizhi.ocean.godzilla.api.service.AiTextPolishService;
import fm.lizhi.ocean.wave.common.model.dto.PolishResultMsgDTO;
import fm.lizhi.ocean.wave.common.model.param.AiPolishExecuteParam;
import fm.lizhi.ocean.wave.common.model.result.PolishDataResult;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.vo.AiPolishConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AiTextPolishServiceRemote {

    @Autowired
    private AiTextPolishService aiTextPolishService;

    /**
     * 获取润色配置
     *
     * @return 结果
     */
    public Result<List<AiPolishConfigVO>> getPolishConfig() {
        RequestBatchGetTextPolishStyle req = new RequestBatchGetTextPolishStyle();
        Result<ResponseBatchGetTextPolishStyle> result = aiTextPolishService.batchGetTextPolishStyle(req);
        if (RpcResult.isFail(result)) {
            return new Result<>(result.rCode(), null);
        }

        ResponseBatchGetTextPolishStyle response = result.target();
        List<TextPolishStyle> textPolishStyles = response.getTextPolishStyles();
        List<AiPolishConfigVO> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(textPolishStyles)) {
            for (TextPolishStyle textPolishStyle : textPolishStyles) {
                AiPolishConfigVO vo = new AiPolishConfigVO().setId(textPolishStyle.getId()).setName(textPolishStyle.getName());
                res.add(vo);
            }
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, res);
    }

    public Result<Long> polishText(int appId, long userId, AiPolishExecuteParam param) {
        RequestSubmitPolishText req = new RequestSubmitPolishText();
        req.setAppId((long) appId);
        req.setText(param.getOriginalText());
        req.setStyleId(param.getStyleId());
        req.setUserId(userId);
        Result<ResponseSubmitPolishText> result = aiTextPolishService.submitPolishText(req);
        if (RpcResult.isFail(result)) {
            return new Result<>(result.rCode(), null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target().getTaskId());
    }

    public Result<List<PolishResultMsgDTO>> getPolishResult(long taskId) {
        Result<List<ResponsePopPolishResult>> polishResult = aiTextPolishService.popPolishResult(taskId);
        if (RpcResult.isFail(polishResult)) {
            return new Result<>(GET_POLISH_RESULT_FAIL, null);
        }
        List<ResponsePopPolishResult> list = polishResult.target();
        List<PolishResultMsgDTO> res = new ArrayList<>();
        for (ResponsePopPolishResult result : list) {
            if (!result.isSuccess()) {
                //下游返回润色失败，直接结束，告知前端
                return new Result<>(GET_POLISH_RESULT_FAIL, null);
            }

            AiPolishDataResult data = result.getData();
            if (data == null) {
                continue;
            }
            PolishDataResult dataResult = new PolishDataResult().setResult(data.getResult())
                    .setIndex(data.getIndex())
                    .setEnd(data.getEnd())
                    .setTotalTokens(data.getTotalTokens())
                    .setPromptTokens(data.getPromptTokens())
                    .setCompletionTokens(data.getCompletionTokens());
            PolishResultMsgDTO msgDTO = new PolishResultMsgDTO().setSuccess(result.isSuccess()).setData(dataResult);
            res.add(msgDTO);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, res);
    }

    int GET_POLISH_RESULT_FAIL = 1;
}
