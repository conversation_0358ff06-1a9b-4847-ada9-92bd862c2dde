package fm.lizhi.ocean.wave.assistant.core.model.bean;

import lombok.Data;

import java.util.Date;

/**
 * 全麦奖励记录
 *
 * <AUTHOR>
 * @date 2025-02-28 02:40:22
 */
@Data
public class CheckInAllMicGiftRecordBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 档期ID
     */
    private Long scheduleId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 送礼人 ID
     */
    private Long sendUserId;

    /**
     * 礼物ID
     */
    private Long giftId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 礼物价值
     */
    private Long giftCoin;

    /**
     * 礼物数量
     */
    private Integer giftAmount;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 收入
     */
    private Long income;

    /**
     * 奖励档位
     */
    private Long rewardLadder;

    /**
     * 奖励金额, 如果包含房主奖励，则是房主奖励档位，否则就是普通档位奖励
     */
    private Long rewardAmount;

    /**
     * 是否包含房主奖励
     */
    private Boolean containNjRewardAmount;

    /**
     * 礼物批次 Id
     */
    private Long giftBatchId;

    /**
     * 被分配用户 ID
     */
    private Long allocationUserId;

    /**
     * 分配时间
     */
    private Date allocationTime;

    /**
     * 创建时间
     */
    private Date createTime;
}