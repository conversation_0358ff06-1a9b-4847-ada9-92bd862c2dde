package fm.lizhi.ocean.wave.user.core.convert;

import java.util.Map;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wave.user.core.remote.bean.VipPrivilegeMedalBean;
import fm.lizhi.ocean.wave.user.result.VipPrivilegeMedalResult;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserVipPrivilegeConvert {

    UserVipPrivilegeConvert I = Mappers.getMapper(UserVipPrivilegeConvert.class);

    VipPrivilegeMedalResult bean2Result(VipPrivilegeMedalBean bean);

    Map<Long, VipPrivilegeMedalResult> beanMap2ResultMap(Map<Long, VipPrivilegeMedalBean> beanMap);

}
