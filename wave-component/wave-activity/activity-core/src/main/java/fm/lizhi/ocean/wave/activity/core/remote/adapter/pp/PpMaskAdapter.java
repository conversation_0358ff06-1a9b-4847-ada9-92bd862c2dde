package fm.lizhi.ocean.wave.activity.core.remote.adapter.pp;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.activity.core.remote.bean.GetMaskByLiveIdResponse;
import fm.lizhi.ocean.wave.activity.core.remote.bean.MaskMicInfoBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.pp.mask.dto.MaskInfoDto;
import pp.fm.lizhi.pp.mask.protocol.MaskProto;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/28 16:31
 */
@Component
@Slf4j
public class PpMaskAdapter {
    public Result<GetMaskByLiveIdResponse> converMaskInfo(Result<MaskProto.ResponseGetMaskByLiveId> result) {
        if (!result.target().hasMaskStr()) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        MaskInfoDto maskInfo = JsonUtil.loads(result.target().getMaskStr(), MaskInfoDto.class);
        GetMaskByLiveIdResponse response = new GetMaskByLiveIdResponse().setMaskId(maskInfo.getId())
                .setMicInfoList(maskInfo.getMicInfoDtoList().stream().map(e -> MaskMicInfoBean.builder()
                        .micIndex(e.getMicIndex()).micUserId(e.getMicUserId()).micIcon(e.getMicIcon())
                        .micName(e.getMicName()).build()).collect(Collectors.toList()));
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }
}
