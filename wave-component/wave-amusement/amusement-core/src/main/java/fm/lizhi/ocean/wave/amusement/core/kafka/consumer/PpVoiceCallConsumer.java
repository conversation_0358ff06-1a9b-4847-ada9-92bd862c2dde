package fm.lizhi.ocean.wave.amusement.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallBizTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallOperateMapping;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementPushManager;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushVoiceCallVO;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.*;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import fm.lizhi.pp.social.constant.chat.VoiceCallOperateMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${pp.kafka.consumer.enable}")
public class PpVoiceCallConsumer {

    @Autowired
    private CommonProviderConfig config;


    @Autowired
    private UserService userService;

    @Autowired
    private AmusementPushManager amusementPushManager;

    @KafkaHandler(topic = "pp_topic_voice_call_operate_msg",
            group = "pp_topic_voice_call_operate_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleVoiceCallMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            log.info("pp.handleVoiceCallMsg msg={}", msg);
            VoiceCallOperateMsg operateMsg = JsonUtils.fromJsonString(msg, VoiceCallOperateMsg.class);
            //处理消息
            pushCallVO(operateMsg);
        } catch (Exception e) {
            log.error("PpVoiceCallConsumer.handleVoiceCallMsg happen error, msg={}", msg, e);
        }
    }

    /**
     * 通话信息推送
     *
     * @param operateMsg 操作信息
     */
    public void pushCallVO(VoiceCallOperateMsg operateMsg) {
        SimpleUser user = getUser(operateMsg.getCallerId());
        //如果是打电话，那就发通话的推送
        PushVoiceCallVO.PushVoiceCallVOBuilder callVOBuilder = PushVoiceCallVO.builder().callId(String.valueOf(operateMsg.getCallId()))
                .calleeId(String.valueOf(operateMsg.getCalleeId()))
                .callerId(String.valueOf(operateMsg.getCallerId()))
                .waveBizType(VoiceCallBizTypeEnum.bizValue2WaveType(operateMsg.getBizType()))
                .operateType(VoiceCallOperateMapping.bizValue2WaveType(operateMsg.getOperateType()))
                .timeout(operateMsg.getCallWaitDuration());
        if (user != null) {
            String cdnHost = config.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost();
            callVOBuilder.targetUserInfo(String.valueOf(user.getUserId()), user.getNickName(), UrlUtils.getAvatarUrl(cdnHost, user.getAvatar()));
        }

        PushVoiceCallVO callVO = callVOBuilder.build();

        if (Objects.equals(callVO.getOperateType(), VoiceCallOperateMapping.CALL.getWaveType())) {
            //如果是拨打，则给被拨打者推
            amusementPushManager.pushCallNotice(callVOBuilder.build(), operateMsg.getCalleeId());
            //需要推送给发起者，因为发起者如果在APP端发起，PC端也需要感知到
            amusementPushManager.pushCallerNotice(callVOBuilder.build(), operateMsg.getCallerId());
        }

        if (Objects.equals(callVO.getOperateType(), VoiceCallOperateMapping.RECEIVE.getWaveType())) {
            //如果是接听，则给双方推
            amusementPushManager.pushCallNotice(callVOBuilder.build(), operateMsg.getCallerId());
            amusementPushManager.pushCallNotice(callVOBuilder.build(), operateMsg.getCalleeId());
        }
    }

    /**
     * 查询用户信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    private SimpleUser getUser(long userId) {
        Result<GetSimpleUserResult> result = userService.getSimpleUserByCache(userId);
        if (RpcResult.isFail(result)) {
            return null;
        }
        GetSimpleUserResult simpleUserResult = result.target();
        return simpleUserResult.getSimpleUser();
    }

}
