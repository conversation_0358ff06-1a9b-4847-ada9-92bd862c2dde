package fm.lizhi.ocean.wave.live.core.remote.demotion.hy;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.api.PopularityService;
import fm.lizhi.live.room.hy.protocol.LivePopularityProto;
import fm.lizhi.ocean.wave.common.blockhandler.rpc.IRpcFaceBlockHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HyPopularityServiceBlockHandler implements IRpcFaceBlockHandler<DegradeException> {

    @Override
    public Class<DegradeException> getExceptionClass() {
        return DegradeException.class;
    }

    @Override
    public Class<?> getTarget() {
        return PopularityService.class;
    }

    /**
     * 人气组
     *
     * @param liveId 直播节目ID
     * @return 结果
     */
    public Result<LivePopularityProto.ResponseGetPopularityGroupCount> getPopularityGroupCount(long liveId) {
        LivePopularityProto.ResponseGetPopularityGroupCount build = LivePopularityProto.ResponseGetPopularityGroupCount.newBuilder()
                .setCurrentCount(0)
                .setTotalCount(0)
                .build();
        log.info("HyPopularityServiceBlockHandler getPopularityGroupCount liveId={}", liveId);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, build);
    }
}
