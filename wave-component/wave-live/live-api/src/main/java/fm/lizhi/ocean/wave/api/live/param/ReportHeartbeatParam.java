package fm.lizhi.ocean.wave.api.live.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ReportHeartbeatParam {


    private boolean isFirst;

    private int appId;

    /**
     * 直播间id, 不在直播间时为空
     */
    private Long liveId;


    /**
     * 用户状态值 [listen,onMic]
     */
    private List<String> userStatus;

    /**
     * 用户id
     */
    private Long userId;

    private String ip;

    private String deviceId;

    private String deviceType;

    private String clientVersion;



}
