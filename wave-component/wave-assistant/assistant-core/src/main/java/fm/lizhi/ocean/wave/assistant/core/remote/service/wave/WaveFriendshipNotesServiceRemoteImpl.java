package fm.lizhi.ocean.wave.assistant.core.remote.service.wave;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.assistant.core.config.AssistantConfig;
import fm.lizhi.ocean.wave.assistant.core.datastore.redis.FriendshipNotesRedisDao;
import fm.lizhi.ocean.wave.assistant.core.model.param.FriendshipNotesParam;
import fm.lizhi.ocean.wave.assistant.core.remote.service.FriendshipNotesServiceRemote;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveFriendshipNotesBean;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveFriendshipTabBean;
import fm.lizhi.ocean.wave.platform.api.user.request.RequestGetFriendshipNotes;
import fm.lizhi.ocean.wave.platform.api.user.request.RequestSaveOrUpdateFriendshipNotes;
import fm.lizhi.ocean.wave.platform.api.user.response.ResponseFriendshipNotes;
import fm.lizhi.ocean.wave.platform.api.user.service.FriendshipNotesService;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WaveFriendshipNotesServiceRemoteImpl implements FriendshipNotesServiceRemote {

    @Autowired
    private FriendshipNotesService friendshipNotesService;

    @Autowired
    private AssistantConfig assistantConfig;

    @Autowired
    private FriendshipNotesRedisDao friendshipNotesRedisDao;

    @Override
    public Result<WaveFriendshipNotesBean> saveOrUpdateFriendshipNotes(FriendshipNotesParam param, long userId) {

        RequestSaveOrUpdateFriendshipNotes.RequestSaveOrUpdateFriendshipNotesBuilder builder = RequestSaveOrUpdateFriendshipNotes.builder()
                .id(param.getId())
                .noteContent(param.getNoteContent())
                .noteTab(param.getNoteTab())
                .itemId(param.getItemId())
                .targetUserId(param.getTargetUserId())
                .userId(userId)
                ;

        Result<WaveFriendshipNotesBean> result = friendshipNotesService.saveOrUpdateFriendshipNotes(builder.build());
        if (RpcResult.isFail(result)){
            log.warn("WaveFriendshipNotesServiceRemoteImpl saveFriendshipNotes is fail. itemId: {}, userId: {}, targetUserId: {}", param.getItemId(), userId, param.getTargetUserId());
            return new Result<>(result.rCode(), null);
        }

        // 删除缓存
        if (assistantConfig.getExclusiveNicknameItemId().equals(param.getItemId())){
            String cache = friendshipNotesRedisDao.getExclusiveNicknameCache(userId, param.getTargetUserId());
            if (StringUtils.isNotBlank(cache)){
                friendshipNotesRedisDao.delExclusiveNicknameCache(userId, param.getTargetUserId());
            }
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target());
    }


    @Override
    public Result<ResponseFriendshipNotes> getFriendshipNotes(long userId, String targetUserId, Integer tabId){

        Result<ResponseFriendshipNotes> result = friendshipNotesService.getFriendshipNotes(RequestGetFriendshipNotes.builder()
                .tabId(tabId).targetUserId(Long.valueOf(targetUserId)).userId(userId)
                .build());

        if (RpcResult.isFail(result)){
            log.warn("WaveFriendshipNotesServiceRemoteImpl getFriendshipNotes is fail. userId: {}, targetUserId: {}, tabId: {}", userId, targetUserId, tabId);
            return new Result<>(result.rCode(), null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target());
    }

    @Override
    public Result<List<WaveFriendshipTabBean>> getFriendshipTab(boolean includeGlobal) {

        List<WaveFriendshipTabBean> tabCache = friendshipNotesRedisDao.getFriendshipNoteTabCache(includeGlobal);
        if (CollectionUtils.isNotEmpty(tabCache)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, tabCache);
        }

        //加分布式锁，设置缓存
        long userId = ContextUtils.getContext().getUserId();
        try (RedisLock lock = friendshipNotesRedisDao.getFriendshipNoteTabLock(includeGlobal)) {
            //加锁,只获取一次锁
            if (!lock.tryLock()) {
                log.warn("getFriendshipTab lock fail. uid:{}", userId);
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }

            //再从缓存一次
            List<WaveFriendshipTabBean> cache = friendshipNotesRedisDao.getFriendshipNoteTabCache(includeGlobal);
            if (CollectionUtils.isNotEmpty(cache)){
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, cache);
            }

            Result<List<WaveFriendshipTabBean>> friendshipTab = friendshipNotesService.getFriendshipTab(includeGlobal);
            if (RpcResult.isFail(friendshipTab)){
                log.warn("WaveFriendshipNotesServiceRemoteImpl getFriendshipTab is fail. uid: {}", userId);
                return new Result<>(FriendshipNotesServiceRemote.GET_FRIENDSHIP_TAB_FAIL, null);
            }

            if (CollectionUtils.isNotEmpty(friendshipTab.target())){
                friendshipNotesRedisDao.setFriendshipNoteTabCache(includeGlobal, friendshipTab.target());
            }
            return friendshipTab;
        } catch (Exception e) {
            log.error("WaveFriendshipNotesServiceRemoteImpl getFriendshipTab error, uid: {}", userId, e);
            return new Result<>(FriendshipNotesServiceRemote.GET_FRIENDSHIP_TAB_FAIL, null);
        }
    }


    @Override
    public Result<String> getFriendshipNotesByExclusiveNickname(Long userId, Long targetUserId) {
        Result<WaveFriendshipNotesBean> result = friendshipNotesService.getFriendshipNotesByExclusiveNickname(userId, targetUserId);
        if (RpcResult.isFail(result)){
            log.warn("WaveFriendshipNotesServiceRemoteImpl getFriendshipNotesByExclusiveNickname is fail. uid: {}, targetUserId: {}", userId, targetUserId);
            return new Result<>(result.rCode(), null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,
                Optional.ofNullable(result.target())
                        .map(WaveFriendshipNotesBean::getNoteContent)
                        .orElse(null)
        );
    }
}
