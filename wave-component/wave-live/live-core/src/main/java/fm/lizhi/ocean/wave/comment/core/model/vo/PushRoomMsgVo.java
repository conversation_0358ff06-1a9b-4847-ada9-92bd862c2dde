package fm.lizhi.ocean.wave.comment.core.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/7/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushRoomMsgVo {

    /**
     * 评论ID
     */
    private String liveId;

    /**
     * 直播间最新消息的时间
     */
    private Long lastMsgTime;

}
