package fm.lizhi.ocean.wave.singer.core.remote.adapter;

import fm.lizhi.ocean.wave.singer.core.remote.result.GetSingerEntranceInfoResponse;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerEntranceInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wave.singer.core.remote.param.SingerVerifyApplyRequest;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerStatus;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SingerVerifyConvert {

    SingerVerifyConvert I = Mappers.getMapper(SingerVerifyConvert.class);

    /**
     * 转换请求
     * @param request
     * @return 结果
     */
    RequestSingerVerifyApply toRequest(SingerVerifyApplyRequest request);

    /**
     * 转换响应
     * @param response
     * @return 结果
     */
    @Mapping(target = "nextSingerEntranceInfo.type", source = "nextSingerEntranceInfo.singerType")
    @Mapping(target = "nextSingerEntranceInfo.name", source = "nextSingerEntranceInfo.singerBizName")
    @Mapping(target = "currentSingerEntranceInfo.type", source = "currentSingerEntranceInfo.singerType")
    @Mapping(target = "currentSingerEntranceInfo.name", source = "currentSingerEntranceInfo.singerBizName")
    GetSingerEntranceInfoResponse statusToResponse(ResponseGetSingerEntranceInfo response);
}
