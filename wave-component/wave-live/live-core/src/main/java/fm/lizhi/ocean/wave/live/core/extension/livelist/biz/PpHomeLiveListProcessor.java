package fm.lizhi.ocean.wave.live.core.extension.livelist.biz;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.livelist.IHomeLiveListProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PpHomeLiveListProcessor implements IHomeLiveListProcessor {

    @Autowired
    private LiveConfig liveConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public CommonLiveConfig getCommonLiveConfig() {
        return liveConfig.getPp();
    }
}
