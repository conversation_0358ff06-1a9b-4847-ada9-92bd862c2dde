package fm.lizhi.ocean.wave.user.core.constant;

import lombok.AllArgsConstructor;

/**
 * 审核结果类型
 */
@AllArgsConstructor
public enum EnumReportStatus {
    PROCESSING(0, "处理中"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败");

    private Integer code;
    private String description;

    public static EnumReportStatus find(Integer code) {
        if (code == null) {
            return null;
        }

        for (EnumReportStatus element : EnumReportStatus.values()) {
            if (element.getCode().equals(code)) {
                return element;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }}
