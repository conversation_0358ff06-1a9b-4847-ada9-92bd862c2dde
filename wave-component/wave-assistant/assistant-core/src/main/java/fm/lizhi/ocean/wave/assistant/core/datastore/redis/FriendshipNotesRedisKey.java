package fm.lizhi.ocean.wave.assistant.core.datastore.redis;

import fm.lizhi.ocean.wave.common.datastore.redis.CacheKeyGenerator;

/**
 * 交友笔记换成 key
 *
 * <AUTHOR>
 */
public enum FriendshipNotesRedisKey implements CacheKeyGenerator.CacheKeyType {


    /**
     * 专属昵称缓存
     * 数据结构：STR
     * EXCLUSIVE_NICKNAME_CACHE_STR_#{userId}_#{targetUserId}
     * value: 专属昵称
     * param: userId: 用户ID
     * param: targetUserId: 对方用户 ID
     * 过期时间：可配置
     */
    EXCLUSIVE_NICKNAME_CACHE_STR,
    EXCLUSIVE_NICKNAME_LOCK_STR,

    /**
     * 交友笔记 Tab 缓存
     * 数据结构：STR
     * FRIENDSHIP_NOTE_TAB_CACHE_STR
     * param: includeGlobal: 是否包含全局 Tab
     * value: tab list
     * 过期时间：一小时
     */
    FRIENDSHIP_NOTE_TAB_CACHE_STR,
    FRIENDSHIP_NOTE_TAB_LOCK_STR
    ;


    @Override
    public String getPrefix() {
        return "WAVE_ASSISTANT";
    }

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        switch (this) {
            default:
                sb.append("_");
                sb.append(this.name());
                break;
        }

        for (Object o : args) {
            sb.append("_" + o);
        }
        return sb.toString();
    }
}
