package fm.lizhi.ocean.wave.comment.core.convert;

import fm.lizhi.ocean.wave.api.live.bean.RoomIncomeItemBean;
import fm.lizhi.ocean.wave.comment.core.model.vo.LevelMarkVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.LevelMark;
import fm.lizhi.ocean.wave.live.core.model.vo.RoomIncomeItemVO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlayerQuestConvert {

    PlayerQuestConvert I = Mappers.getMapper(PlayerQuestConvert.class);

    List<LevelMarkVO> toMarkVOList(List<LevelMark> levelMarks);

}
