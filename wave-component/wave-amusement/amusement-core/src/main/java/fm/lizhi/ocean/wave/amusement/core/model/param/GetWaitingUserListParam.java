package fm.lizhi.ocean.wave.amusement.core.model.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/30
 */
@Data
public class GetWaitingUserListParam {

    @NotNull(message = "参数错误")
    @JsonSerialize(using = ToStringSerializer.class)
    private long liveId;

}
