package fm.lizhi.ocean.wave.live.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorMsg;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.room.pp.bean.BanCommentMsg;
import fm.lizhi.live.room.pp.bean.RoleChangeMsg;
import fm.lizhi.ocean.wave.comment.core.manager.CommentCacheCleanManager;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.live.core.manager.LiveChangeEventManager;
import fm.lizhi.ocean.wave.live.core.manager.LivePushManager;
import fm.lizhi.ocean.wave.live.core.model.dto.LiveChangeEventDTO;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p>
 * Description: pp kafka消息消费者
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${pp.kafka.consumer.enable}")
public class LivePpKafkaConsumer {
    @Autowired
    private LivePushManager livePushManager;

    @Autowired
    private CommentCacheCleanManager commentCacheCleanManager;

    @Autowired
    private LiveChangeEventManager liveChangeEventManager;

    /**
     * 评论禁言消息
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_pp_ban_comment_msg",
            group = "lz_ocean_wave_live_ban_comment_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleBanCommentMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            BanCommentMsg banCommentMsg = JsonUtil.loads(msg, BanCommentMsg.class);
            long liveRoomId = banCommentMsg.getLiveRoomId();
            int operateType = banCommentMsg.getOperateType();
            long userId = banCommentMsg.getUserId();
            log.info("LivePpKafkaConsumer banCommentMsg liveRoomId={}`userId={}`operateType={}", liveRoomId, userId, operateType);
            livePushManager.pushBanComment(liveRoomId, userId, operateType, BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("LivePpKafkaConsumer banCommentMsg json parse error,  msg={}`orgMsg={}", msg, body, e);
        }
    }


    @KafkaHandler(topic = "lz_topic_pp_role_change_msg",
            group = "lz_ocean_wave_live_role_change_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleRoleChangeMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            RoleChangeMsg roleChangeMsg = JsonUtil.loads(msg, RoleChangeMsg.class);
            long liveRoomId = roleChangeMsg.getLiveRoomId();
            long userId = roleChangeMsg.getUserId();
            int role = roleChangeMsg.getRole();
            int operateType = roleChangeMsg.getOperateType();
            log.info("LivePpKafkaConsumer roleChangeMsg liveRoomId:{}, userId:{}, role:{}, operateType:{}", liveRoomId, userId, role, operateType);
            livePushManager.pushRoleChange(liveRoomId, userId, role, operateType, BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("LivePpKafkaConsumer roleChangeMsg json parse error, msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    /**
     * pp主播行为消息
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "lz_pp_topic_live_room_anchor_behavior_message",
            group = "lz_ocean_wave_live_anchor_behavior_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAnchorBehaviorMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AnchorBehaviorMsg anchorBehaviorMsg;
            anchorBehaviorMsg = JsonUtil.loads(msg, AnchorBehaviorMsg.class);
            AnchorBehaviorType behaviorType = AnchorBehaviorType.from(anchorBehaviorMsg.getType());

            if (behaviorType == null) {
                log.warn("pp.anchorBehaviorMsg type, type:{}", anchorBehaviorMsg.getType());
                return;
            }
            log.info("pp.anchorBehaviorMsg type:{}, userId:{}, liveId:{}", behaviorType, anchorBehaviorMsg.getUserId(), anchorBehaviorMsg.getLive().getLiveId());

            //直播中不做任何操作，量级太大了
            if (behaviorType == AnchorBehaviorType.LIVE) {
                return;
            }

            int appId = BusinessEvnEnum.PP.getAppId();
            long liveId = anchorBehaviorMsg.getLive().getLiveId();
            long liveRoomId = anchorBehaviorMsg.getLive().getLiveRoomId();
            livePushManager.pushLiveSync(liveRoomId, liveId, appId);
            //清理评论缓存
            if (behaviorType == AnchorBehaviorType.CLOSE_LIVE) {
                commentCacheCleanManager.setCommentCacheExpireTime(appId, liveId);
            }
        } catch (Exception e) {
            log.error("pp.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }


    /**
     * pp 更改直播背景消息
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "lz_topic_pp_live_change_event",
            group = "lz_ocean_wave_live_change_event_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleLiveChangeEvent(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            int appId = BusinessEvnEnum.PP.getAppId();
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            LiveChangeEventDTO liveChangeEventDto = JsonUtil.loads(msg, LiveChangeEventDTO.class);
            liveChangeEventManager.handleLiveChangeEvent(liveChangeEventDto, appId);
        } catch (Exception e) {
            log.error("pp.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }finally {
            ContextUtils.clearContext();
        }
    }

}
