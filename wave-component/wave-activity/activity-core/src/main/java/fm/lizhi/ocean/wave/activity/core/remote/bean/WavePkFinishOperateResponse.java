package fm.lizhi.ocean.wave.activity.core.remote.bean;

import fm.lizhi.ocean.wave.activity.core.constant.ActivityMsgCodes;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 结束Pk的响应
 */
@Data
@ToString
public class WavePkFinishOperateResponse {

    /**
     * 提示语状态码
     */
    @NotNull
    private Integer toastCode;

    /**
     * 提示消息
     */
    private String toastMsg;

    public static WavePkFinishOperateResponse failOf(ActivityMsgCodes activityMsgCode) {
        WavePkFinishOperateResponse response = new WavePkFinishOperateResponse();
        response.toastCode = activityMsgCode.getCode();
        response.toastMsg = activityMsgCode.getMsg();
        return response;
    }
    public static WavePkFinishOperateResponse success() {
        WavePkFinishOperateResponse response = new WavePkFinishOperateResponse();
        response.toastCode = 0;
        return response;
    }

    public static WavePkFinishOperateResponse ppPenaltiesFailOf(Long penaltiesTime) {
        WavePkFinishOperateResponse response = new WavePkFinishOperateResponse();
        response.toastCode = ActivityMsgCodes.PK_FINISH_PENALTIES_TIME_FAIL.getCode();
        response.toastMsg = String.format(ActivityMsgCodes.PK_FINISH_PENALTIES_TIME_FAIL.getMsg(), penaltiesTime);
        return response;
    }
}
