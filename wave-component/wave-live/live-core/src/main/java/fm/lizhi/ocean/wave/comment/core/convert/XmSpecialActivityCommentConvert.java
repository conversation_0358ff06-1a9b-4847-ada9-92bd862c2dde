package fm.lizhi.ocean.wave.comment.core.convert;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.*;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.XmCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FeedContentInfoBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import xm.fm.lizhi.datacenter.comment.pp.bean.*;
import xm.fm.lizhi.datacenter.comment.pp.bean.SimpleMedal;
import xm.fm.lizhi.datacenter.comment.pp.bean.TransientComment;

import java.util.ArrayList;
import java.util.List;

/**
 * PP评论查询适配器
 *
 * <AUTHOR>
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface XmSpecialActivityCommentConvert {

    XmSpecialActivityCommentConvert I = Mappers.getMapper(XmSpecialActivityCommentConvert.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "aspectRatio", ignore = true)
    @Mapping(target = "bizType", source = "biz")
    @Mapping(target = "simpleUser", expression = "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression = "java(convertWealthLevel(transientComment.getPpWealthLevelBean()))")
    @Mapping(target = "freshUser", expression = "java(convertFreshUser(transientComment.getFreshUserBean()))")
    @Mapping(target = "medal", expression = "java(convertMedals(transientComment.getPpMedalBeans(), transientComment.getMedalWallMedals()))")
    TransientCommentDTO convertComment(TransientComment transientComment);

    default TransientCommentDTO convert(TransientComment transientComment, CommentConfig commentConfig) {
        TransientCommentDTO transientCommentDTO = convertComment(transientComment);

        // 评论类型转换
        int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(), ContextUtils.getContext().getBusinessEvnEnum(), transientComment.getCommentTypeExtension());

        XmCommentExtraDTO commentExtraDTO = new XmCommentExtraDTO();
        commentExtraDTO.setAtUsers(convertAtUserIds(transientComment.getAtUsers()));
        commentExtraDTO.setAnonymous(transientComment.isAnonymous());

        transientCommentDTO.setCommentType(commentType);
        transientCommentDTO.setXmCommentExtra(commentExtraDTO);
        return transientCommentDTO;
    }

    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(PpWealthLevelBean wealthLevel);

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", source = "userNameColors")
    SimpleUser convertSimpleUser(xm.fm.lizhi.datacenter.comment.pp.bean.SimpleUser simpleUser);

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    @Mapping(target = "aspect", ignore = true)
    @Mapping(target = "url", ignore = true)
    FreshUser convertFreshUser(FreshUserBean freshUser);

    /**
     * 转换勋章
     *
     * @param ppMedalList        勋章列表
     * @param medalWallMedalList 佩戴的勋章列表
     * @return 结果
     */
    default List<Medal> convertMedals(List<PpMedalBean> ppMedalList, List<SimpleMedal> medalWallMedalList) {
        List<Medal> medalBeans = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ppMedalList)) {
            for (PpMedalBean ppMedal : ppMedalList) {
                Medal medalBean = new Medal();
                medalBean.setCover(ppMedal.getIcon());
                medalBean.setAspect(ppMedal.getAspect());
                medalBeans.add(medalBean);
            }
        }

        if (CollectionUtils.isNotEmpty(medalWallMedalList)) {
            for (SimpleMedal simpleMedal : medalWallMedalList) {
                Medal medalBean = new Medal();
                medalBean.setCover(simpleMedal.getImage());
                medalBean.setAspect(simpleMedal.getAspect());
                medalBeans.add(medalBean);
            }
        }

        return medalBeans;
    }

    /**
     * 转换为at用户列表
     *
     * @param atusersList at用户列表
     * @return 转换结果
     */
    List<AtUser> convertAtUserIds(List<AtUserBean> atusersList);

    /**
     * 点唱信息
     *
     * @param bean bean
     * @return 结果
     */
    SongInfo convertSongInfo(OrderSongInfoBean bean);

    /**
     * 点唱信息
     *
     * @param songInfo 点唱信息
     * @return 结果
     */
    XmSongSpecialActivityCommentExtra convertSongCommentBizExtra(SongInfo songInfo);

    /**
     * 房间精选评论
     *
     * @param roomPlaylistBean bean
     * @return 结果
     */
    RoomPlaylistCommentCardBean convertRoomPlayListCommentBean(xm.fm.lizhi.datacenter.comment.pp.bean.RoomPlaylistCommentCardBean roomPlaylistBean);

    /**
     * 转换房间精选评论
     *
     * @param bean bean
     * @return 结果
     */
    XmRoomPlayListCommentCardCommentExtra convertRoomPlayListCommentBizExtra(RoomPlaylistCommentCardBean bean);

    /**
     * 房间轮盘
     *
     * @param roomPlaylistBean bean
     * @return 结果
     */
    RoomRouletteWheelCommentCardBean convertRoomRouletteWheelCommentBean(RouletteWheelExtraBean roomPlaylistBean);

    /**
     * 转换房间轮盘评论
     *
     * @param bean bean
     * @return 结果
     */
    XmRoomRouletteWheelCommentExtra convertRoomRouletteWheelCommentBizExtra(RoomRouletteWheelCommentCardBean bean);

    /**
     * 新用户偏好信息
     *
     * @param freshUserInterestInfoBean bean
     * @return 结果
     */
    FreshUserInterestBean convertFreshUserInterestCommentBean(FreshUserInterestInfoBean freshUserInterestInfoBean);

    /**
     * 转换作品内容信息评论
     *
     * @param feedContentInfoBean 结果
     * @return 结果
     */
    FeedContentInfoBean convertFeedContentInfoToBean(xm.fm.lizhi.datacenter.comment.pp.bean.FeedContentInfoBean feedContentInfoBean);

    /**
     * 转换房间轮盘评论
     *
     * @param bean bean
     * @return 结果
     */
    XmFreshUserInterestCommentExtra convertFreshUserInterestCommentBizExtra(FreshUserInterestBean bean);

    /**
     * 转换作品内容信息评论
     *
     * @param feedContentInfoBean bean
     * @return 结果
     */
    XmFeedContentCommentExtra convertFeedContentCommentToExtra(FeedContentInfoBean feedContentInfoBean);

}
