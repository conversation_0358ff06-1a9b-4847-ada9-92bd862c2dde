package fm.lizhi.ocean.wave.comment.core.remote.service.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.remote.service.IPlayerAutoSendServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import hy.fm.lizhi.live.pp.player.api.PpPlayerAutoSendService;
import hy.fm.lizhi.live.pp.player.protocol.PpPlayerAutoSendProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 陪玩自动发信息服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyPlayerAutoSendServiceRemote extends RemoteServiceInvokeFacade implements IPlayerAutoSendServiceRemote {

    @Autowired
    private PpPlayerAutoSendService ppPlayerAutoSendService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    @Override
    public Result<Void> sendComeRoomCommentToPlayer(long liveId, long uid, int comeSource, String comeJson) {
        try {
            Result<PpPlayerAutoSendProto.ResponseSendComeRoomCommentToPlayer> result = ppPlayerAutoSendService.sendComeRoomCommentToPlayer(liveId, uid, comeSource, comeJson);
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.info("HyPlayerAutoSendServiceRemote.sendComeRoomCommentToPlayer fail, rCode={}, liveId={}, userId={}", result.rCode(), liveId, uid);
                return new Result<>(IPlayerAutoSendServiceRemote.SEND_COME_ROOM_COMMENT_TO_PLAYER_FAIL, null);
            }
            return new Result<>(IPlayerAutoSendServiceRemote.SEND_ROOM_INTRO_TO_USER_SUCCESS, null);
        } catch (Exception e) {
            log.warn("HyPlayerAutoSendServiceRemote.sendComeRoomCommentToPlayer fail, liveId={}, userId={}", liveId, uid, e);
            return new Result<>(IPlayerAutoSendServiceRemote.SEND_COME_ROOM_COMMENT_TO_PLAYER_FAIL, null);
        }

    }
}
