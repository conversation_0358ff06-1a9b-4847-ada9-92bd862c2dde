package fm.lizhi.ocean.wave.user.core.model.param;

import fm.lizhi.ocean.wave.api.amusement.constants.UserHeartBeatStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 用户心跳参数
 * <AUTHOR>
 */
@Data
public class UserHeartbeatParam {

    private boolean isFirst;

    // --------- 以下参数都可能为空 ----------

    /**
     * 直播间id, 不在直播间时为空
     */
    private Long liveId;

    /**
     * 用户状态值
     * @see UserHeartBeatStatusEnum
     */
    private List<String> userStatus;



}
