package fm.lizhi.ocean.wave.user.core.remote.adapter.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.remote.param.GetFollowUserListParam;
import fm.lizhi.ocean.wave.user.core.remote.result.GetFollowUserListResult;
import fm.lizhi.ocean.wave.user.core.remote.service.IUserServiceRemote;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.pp.relation.api.PpRelationService;
import xm.fm.lizhi.pp.relation.protocol.PpRelationBaseProto;
import xm.fm.lizhi.pp.relation.protocol.PpRelationProto;
import xm.fm.lizhi.pp.relation.protocol.PpRelationRequestProto;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class XmGetFollowUserListAdapter implements IRemoteMethodParamAndResultAdapter<
        GetFollowUserListParam, PpRelationRequestProto.GetFollowListRequest,
        Result<PpRelationProto.ResponseGetFollowList>, Result<GetFollowUserListResult>
        > {

    private static UserProviderConfig userProviderConfig;

    @Autowired
    private void setUserProviderConfig(UserProviderConfig userProviderConfig) {
        this.userProviderConfig = userProviderConfig;
    }

    @Override
    public PpRelationRequestProto.GetFollowListRequest convertParam(GetFollowUserListParam param) {
        return PpRelationRequestProto.GetFollowListRequest.newBuilder()
                .setUserId(param.getUserId())
                .setPerformanceId(param.getPerformanceId())
                .setPageSize(param.getPageSize())
                .build();
    }

    @Override
    public Result<GetFollowUserListResult> convertResult(Result<PpRelationProto.ResponseGetFollowList> result) {
        int rCode = result.rCode();
        if (rCode != PpRelationService.GET_FOLLOW_LIST_SUCCESS) {
            return new Result<>(IUserServiceRemote.GET_FOLLOW_LIST_FAIL, null);
        }

        List<Long> officialUserIds = getOfficialUserIds();
        List<Long> resultList = result.target().getFollowList().stream()
                .map(PpRelationBaseProto.Follow::getUserId)
                .filter(userId -> !userProviderConfig.getXm().isXmShowOfficial() && !officialUserIds.contains(userId))
                .collect(Collectors.toList());
        long performanceId = result.target().getPerformanceId();

        return new Result<>(IUserServiceRemote.GET_FOLLOW_LIST_SUCCESS, GetFollowUserListResult.builder()
                .resultList(resultList)
                .performanceId(performanceId)
                .build());
    }


    private List<Long> getOfficialUserIds() {
        return Arrays.stream(userProviderConfig.getXm().getXmOfficialUserIds().split(","))
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }
}
