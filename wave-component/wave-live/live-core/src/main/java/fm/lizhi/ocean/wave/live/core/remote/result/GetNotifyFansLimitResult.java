package fm.lizhi.ocean.wave.live.core.remote.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取通知粉丝限制结果
 *
 * <AUTHOR>
 * @Date 2023/07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetNotifyFansLimitResult {
    /**
     * 是否被限制，0-不被限制，1-限制
     */
    private int limit;
    /**
     * 通知粉丝时间间隔，单位：秒
     */
    private long limitInterval;
    /**
     * 限制剩余时间，单位：秒
     */
    private long remainTime;
}
