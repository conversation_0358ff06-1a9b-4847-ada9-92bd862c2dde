package fm.lizhi.ocean.wave.user.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
public class VisitorUserVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private long userId;
    private String nickName;
    private Integer age;

    private String signature;

    private String avatar;

    private String band;

    /**
     * 在线描述
     */
    private String onlineStatusDesc;

    /**
     * 在线状态码
     * 1-直播中 2-在麦上 3-收听中 4-在线 5-xxx小时前在线
     */
    private Integer onlineStatusCode;
}
