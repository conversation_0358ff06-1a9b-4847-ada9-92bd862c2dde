package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IEnterNoticeMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.FreshUserMedalV2Node;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.WealthMedalV2Node;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.XmMsgUserExtraInfo;
import fm.lizhi.ocean.wave.common.util.ContainsUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmEnterNoticeMsgProcessor implements IEnterNoticeMsgProcessor {

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private FreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public MsgUserVO<?> buildUserVoAndAdapterExtra(EnterNoticeDTO enterNoticeEntry) {
        MsgUserVO<XmMsgUserExtraInfo> userVO = new MsgUserVO<>();
        XmMsgUserExtraInfo xmMsgUserExtraInfo = new XmMsgUserExtraInfo();
        userVO.setBizOtherExtra(xmMsgUserExtraInfo);
        return userVO;
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getXm();
    }

    @Override
    public List<EnterNoticeDTO> filterHideEnterNotice(List<EnterNoticeDTO> enterNoticeEntries) {
        //西米的进房公告隐身在下游做了
        return enterNoticeEntries;
    }

    @Override
    public List<ICommentV2MedalNode> getBuildEnterNoticeMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(8);
        //2. 设置财富等级勋章
        //3. 设置新用户勋章
        nodes.add(wealthMedalNode);
        nodes.add(freshUserMedalNode);
        return nodes;
    }

    /**
     * 是否展示欢迎按钮， 西米刚刚上线的。暂时屏蔽
     *
     * @param liveId           直播节目ID
     * @param enterNoticeEntry 进房公告消息
     */
    @Override
    public void setShowButton(long liveId, long userId, EnterNoticeDTO enterNoticeEntry, EnterMsgVO enterNoticeVO) {
    }

    @Override
    public boolean isCanGetEnterNotice(long liveId) {
        return commentConfig.getXm().isEnterNoticeOn()
                && !ContainsUtils.contains(commentConfig.getXm().getBigStarLiveLiveIds(), liveId);
    }

    @Override
    public boolean isBigStarNjHideMount(long njId) {
        if (StringUtils.isEmpty(commentConfig.getXm().getMountBlackListIds())) {
            return false;
        }
        return commentConfig.getXm().getMountBlackListIds().contains(String.valueOf(njId));
    }

}
