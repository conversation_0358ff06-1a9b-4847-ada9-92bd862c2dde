package fm.lizhi.ocean.wave.comment.core.model.vo;

import lombok.Data;

/**
 * 等级标记
 * <AUTHOR>
 */
@Data
public class LevelMarkVO {
    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 1技能陪玩，2娱乐厅陪玩，3娱乐厅主持
     */
    private Integer type;
    /**
     * 等级数
     */
    private Integer level;
    /**
     * 等级名称
     */
    private String name;
    /**
     * 标识图URL（小徽章+名称）
     */
    private String cover;
    /**
     * 比例
     */
    private Double aspect;
    /**
     * 大徽章
     */
    private String badge;
    /**
     *
     */
    private String nameCover1;
    /**
     *
     */
    private String nameCover2;
    /**
     * 等级证书名
     */
    private String levelInfoIntro;
    /**
     * 等级证书
     */
    private String levelInfoMask;
    /**
     * 等级证书背景
     */
    private String levelInfoBgImg;
}
