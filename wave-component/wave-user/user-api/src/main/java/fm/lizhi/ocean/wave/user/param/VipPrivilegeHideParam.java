package fm.lizhi.ocean.wave.user.param;

import fm.lizhi.ocean.wave.user.constant.WaveNoblePrivilegeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VIP特权隐身请求参数
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class VipPrivilegeHideParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * @see WaveNoblePrivilegeType
     * 特意标识,无特权标识的业务可不填写
     */
    private int privilege;


}
