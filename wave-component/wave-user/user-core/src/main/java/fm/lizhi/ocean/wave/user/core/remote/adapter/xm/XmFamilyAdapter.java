package fm.lizhi.ocean.wave.user.core.remote.adapter.xm;

import fm.lizhi.ocean.wave.user.core.remote.bean.FamilyInfo;
import org.springframework.stereotype.Component;

@Component
public class XmFamilyAdapter {

    /**
     * protoToBean
     * @param familyInfo 家族信息
     * @return 家族bean
     */
    public FamilyInfo familyProtoToBean(fm.lizhi.xm.family.bean.FamilyInfo familyInfo){
        return FamilyInfo.builder()
                .id(familyInfo.getId())
                .familyName(familyInfo.getFamilyName())
                .familyType(familyInfo.getFamilyType())
                .familyNote(familyInfo.getFamilyNote())
                .status(familyInfo.getStatus())
                .userId(familyInfo.getUserId())
                .familyIconUrl(familyInfo.getFamilyIconUrl())
                .build();
    }
}
