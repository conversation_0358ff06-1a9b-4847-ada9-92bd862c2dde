package fm.lizhi.ocean.wave.user.core.remote.service.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.core.remote.param.GetUserOfficialCertifiedRequest;
import fm.lizhi.ocean.wave.user.core.remote.result.GetUserOfficialCertifiedResponse;
import fm.lizhi.ocean.wave.user.core.remote.service.IOfficialCertifiedServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Slf4j
@Component
public class PpOfficialCertifiedServiceRemote extends RemoteServiceInvokeFacade implements IOfficialCertifiedServiceRemote {
    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.PP.equals(evnEnum);
    }

    @Override
    public Result<GetUserOfficialCertifiedResponse> getUserOfficialCertified(GetUserOfficialCertifiedRequest request) {
        GetUserOfficialCertifiedResponse response = new GetUserOfficialCertifiedResponse();
        response.setOfficialCertifiedList(Collections.emptyList());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }
}
