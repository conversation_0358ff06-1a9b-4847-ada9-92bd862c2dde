package fm.lizhi.ocean.wave.gift.core.controller;

import fm.lizhi.ocean.wave.gift.core.manager.GiftPanelManager;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftBoxParam;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftGroupParam;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftListParam;
import fm.lizhi.ocean.wave.gift.core.model.result.GiftGroupResult;
import fm.lizhi.ocean.wave.gift.core.model.result.GiftListResult;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping
public class GiftPanelController {

    @Autowired
    private GiftPanelManager giftGroupManager;

    /**
     * 获取礼物分组
     */
    @GetMapping("/giftGroup/list")
    @VerifyUserToken
    public ResultVO<GiftGroupResult> getGiftGroup(@Validated GiftGroupParam param) {
        param.setUserId(String.valueOf(ContextUtils.getContext().getUserId()));
        return ResultVO.success(giftGroupManager.getGiftGroup(param));
    }


    /**
     * 根据分组获取礼物列表
     *
     * @param param
     * @return
     */
    @GetMapping("/gift/listByGroup")
    @VerifyUserToken
    public ResultVO<GiftListResult> listByGroup(@Validated GiftListParam param) {
        param.setUserId(String.valueOf(ContextUtils.getContext().getUserId()));
        return ResultVO.success(giftGroupManager.listByGroup(param));
    }


    /**
     * 获取盲盒礼物列表
     * @param param
     * @return
     */
    @GetMapping("/gift/getGiftBoxRelation")
    @VerifyUserToken
    public ResultVO<GiftListResult> getGiftBoxRelation(GiftBoxParam param){
        param.setUserId(String.valueOf(ContextUtils.getContext().getUserId()));
        return ResultVO.success(giftGroupManager.getGiftBoxRelation(param));
    }

}
