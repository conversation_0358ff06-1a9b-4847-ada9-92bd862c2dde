package fm.lizhi.ocean.wave.common.extension;


import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/15
 */
@Component
public class ProcessorFactory implements InitializingBean {

    @Autowired
    private List<IProcessor<?, ?, ?, ?>> processorList;

    private final Map<String, Map<Integer, IProcessor<?, ?, ?, ?>>> classProcessorMap = new HashMap<>();

    /**
     * 获取处理类（废弃，后续新的功能处理流程差异化不再继承IProcessor）
     *
     * @param appId
     * @param clazz
     * @param <T>
     * @return
     */
    @Deprecated
    public <T extends IProcessor<?, ?, ?, ?>> T getProcessor(int appId, Class<T> clazz) {
        Map<Integer, IProcessor<?, ?, ?, ?>> appIdMap = this.classProcessorMap.get(clazz.getName());
        if (MapUtils.isEmpty(appIdMap)) {
            throw new RuntimeException("未找到对应的处理类");
        }

        IProcessor<?, ?, ?, ?> iProcessor = appIdMap.get(appId);
        if (iProcessor == null) {
            throw new RuntimeException("未找到对应的处理类");
        }

        return clazz.cast(iProcessor);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // @what：字段注入后再解析为map @why：构造参数注入无法解决循环依赖
        for (IProcessor<?, ?, ?, ?> processor : this.processorList) {
            int appId = processor.getBizRouter().getValue();
            Class<? extends IProcessor> baseClass = processor.getBaseBusinessProcessor();
            this.classProcessorMap.computeIfAbsent(baseClass.getName(), key -> new HashMap<>())
                    .put(appId, processor);
        }

        if (isCheck()) {
            int bizNum = BizRouterEnum.values().length;
            for (Map.Entry<String, Map<Integer, IProcessor<?, ?, ?, ?>>> mapEntry : this.classProcessorMap.entrySet()) {
                if (mapEntry.getValue().keySet().size() != bizNum) {
                    throw new RuntimeException(mapEntry.getKey()+" 实现类不完整");
                }
            }
        }

    }

    private boolean isCheck() {
        return ConfigUtils.getEnv() == Env.TEST
                || ConfigUtils.getEnv() == Env.LOCAL
                || ConfigUtils.getEnv() == Env.DEV;
    }
}
