package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.manager.LiveCommentExtra;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoomRouletteWheelCommentVO implements LiveCommentExtra {

    /**
     * 消息卡片标题
     */
    private String title;


    /**
     * 选项列表
     */
    private List<RoomRouletteWheelOptionVO> options;

    private Integer resultOptionId;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoomRouletteWheelOptionVO {
        /**
         * 选项id
         */
        private Integer optionId;

        /**
         * 选项名称
         */
        private String name;

        /**
         * 权重
         */
        private Integer weight;
    }
}
