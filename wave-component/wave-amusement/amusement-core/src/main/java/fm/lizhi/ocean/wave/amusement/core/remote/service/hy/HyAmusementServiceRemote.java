package fm.lizhi.ocean.wave.amusement.core.remote.service.hy;

import api.activity.api.LivePkService;
import api.activity.bean.pk.PkData;
import api.activity.bean.pk.PkLiveData;
import api.activity.constants.WaveLivePkStatusEnum;
import api.activity.param.PkInfoParam;
import api.activity.result.PkInfoResult;
import com.google.protobuf.ByteString;
import com.lizhi.pplive.PPliveBusiness;
import com.yibasan.lizhifm.protocol.LZModelsPtlbuf;
import fm.lizhi.amusement.commons.beeper.api.BpVoiceCallService;
import fm.lizhi.amusement.commons.beeper.dto.response.BatchUserVoiceCallingResp;
import fm.lizhi.amusement.commons.beeper.dto.response.VoiceCallingResp;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.rome.push.legacy.api.LegacyMessagePushService;
import fm.lizhi.commons.rome.push.legacy.api.PushMetadata;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.amusement.hy.api.HyNewLiveAmusementService;
import fm.lizhi.live.amusement.hy.api.LiveAmusementCommonService;
import fm.lizhi.live.amusement.hy.api.NewLiveSeatUserVerifyService;
import fm.lizhi.live.amusement.hy.api.PpUserDayMapService;
import fm.lizhi.live.amusement.hy.protocol.LiveAmusementCommonProto;
import fm.lizhi.live.amusement.hy.protocol.LiveAmusementProto;
import fm.lizhi.live.amusement.hy.protocol.NewLiveSeatUserVerifyAmusementProto;
import fm.lizhi.live.amusement.hy.protocol.UserDayMapProto;
import fm.lizhi.live.operation.hy.api.OperationMessageService;
import fm.lizhi.live.operation.hy.protocol.OperationMessageProto;
import fm.lizhi.live.operation.hy.protocol.OperationMessageRequestProto;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.amusement.core.constant.GuestOperationTypeMapping;
import fm.lizhi.ocean.wave.amusement.core.constant.TeamWarStateEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.UserSeatStatusEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.WaveGuestOperationTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementPushManager;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushInviteOnSeatEventVo;
import fm.lizhi.ocean.wave.amusement.core.remote.adapter.hy.HyWaitingUserAdapter;
import fm.lizhi.ocean.wave.amusement.core.remote.bean.*;
import fm.lizhi.ocean.wave.amusement.core.remote.param.*;
import fm.lizhi.ocean.wave.amusement.core.remote.result.*;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IAmusementServiceRemote;
import fm.lizhi.ocean.wave.api.amusement.bean.hy.HyAmusementExtra;
import fm.lizhi.ocean.wave.api.amusement.constants.ApplyModeEnum;
import fm.lizhi.ocean.wave.api.amusement.constants.UserPlayStatusEnum;
import fm.lizhi.ocean.wave.api.amusement.constants.hy.HyFunModeType;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.bean.Live;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.common.util.ImgUtil;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.TimeUtil;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.pp.util.protocol.PpCommonCodec;
import hy.fm.lizhi.live.pp.user.api.UserInviteService;
import hy.fm.lizhi.live.pp.user.protocol.UserInviteProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wave.common.constant.PhotoConstant.*;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/17
 */
@Slf4j
@Component
public class HyAmusementServiceRemote extends RemoteServiceInvokeFacade implements IAmusementServiceRemote {

    private static final int CMD_INVITE_ON_CALL = 0x12;
    /**
     * 0.首次实名操作，1.二次实名认证操作
     */
    private static final int OPERATION = 1;

    @Autowired
    private LiveService liveService;

    @Autowired
    LegacyMessagePushService hyMessagePushService;

    @Autowired
    private AmusementConfig amusementConfig;

    @Autowired
    private PpUserDayMapService ppUserDayMapService;

    @Autowired
    private HyNewLiveAmusementService hyNewLiveAmusementService;

    @Autowired
    private OperationMessageService operationMessageService;
    @Autowired
    private AmusementPushManager amusementPushManager;
    @Autowired
    private UserInviteService userInviteService;
    @Autowired
    private NewLiveSeatUserVerifyService newLiveSeatUserVerifyService;
    @Autowired
    private BpVoiceCallService bpVoiceCallService;
    @Autowired
    private LivePkService livePkService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    @Override
    public Result<ResponseGetAmusementInfoResult> liveAmusementInfo(RequestGetAmusementInfoParam param) {

        long liveId = param.getLiveId();
        //查询娱乐模式信息
        Result<LiveAmusementProto.ResponseLiveAmusementInfo> getAmusementResp = hyNewLiveAmusementService.liveAmusementInfo(liveId, Long.MIN_VALUE);
        if (getAmusementResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy get amusement info error, liveId: {}, rCode: {}", liveId, getAmusementResp.rCode());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_INFO_ERROR, null);
        }

        //判断是否开启了娱乐模式
        LiveAmusementProto.LiveFunSwitch liveFunSwitch = getAmusementResp.target().getLiveFunSwitch();
        if (liveFunSwitch.getFunMode() == HyNewLiveAmusementService.LIVE_AMUSEMENT_MODE_CLOSE) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ResponseGetAmusementInfoResult.builder()
                    .amusementBaseInfoBean(AmusementBaseInfoBean.builder().amusementSwitch(false).build())
                    .build());
        }

        //获取节目信息-走本地缓存获取
        Result<GetLiveResult> getLiveResp = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        if (getLiveResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy get live info error, liveId: {}, rCode: {}", liveId, getLiveResp.rCode());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_INFO_ERROR, null);
        }

        // 娱乐模式基本信息
        //查询排麦模式 黑叶特有的
        Long liveRoomId = getLiveResp.target().getLive().getLiveRoomId();
        Result<LiveAmusementCommonProto.ResponseGetliveRoomMicMode> getMicResp = getSpringInterfaceProxyBean(LiveAmusementCommonService.class).getliveRoomMicMode(liveRoomId);
        if (getMicResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy get live room mic mode error, liveRoomId: {}, rCode: {}", liveRoomId, getMicResp.rCode());
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_INFO_ERROR, null);
        }
        HyFunModeType hyFunModeType = HyFunModeType.NORMAL;
        if (getAmusementResp.target().getLiveFunLikeMoment().getLikeMomentState() == 1) {
            hyFunModeType = HyFunModeType.LIKE_MOMENT;
        } else if (getAmusementResp.target().getLiveFunTeamWar().getState() == 1) {
            hyFunModeType = HyFunModeType.LIVE_FUN_TEAM;
        }
        HyAmusementExtra hyAmusementExtra = new HyAmusementExtra();
        hyAmusementExtra.setHyFunModeType(hyFunModeType.getValue());

        AmusementBaseInfoBean amusementBaseInfoBean = AmusementBaseInfoBean.builder()
                .amusementSwitch(true)
                .voiceChannelId(liveFunSwitch.getChannelId())
                .njVoiceUserId(liveFunSwitch.getUniqueId())
                .appKey(liveFunSwitch.getAppKey())
                .applyMode(getMicResp.target().getMicMode())
                .extraJson(JsonUtil.dumps(hyAmusementExtra))
                .lastModifyTime(getAmusementResp.target().getLastModifyTime())
                .build();

        // 麦位信息
        List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans = buildAmusementSeatBaseInfoBeans(getAmusementResp.target().getLiveFunSeatsList());

        // 获取团战信息
        TeamWarBean.TeamWarBeanBuilder teamWarBuilder = TeamWarBean.builder();
        fillTeamWarInfo(getAmusementResp.target(), teamWarBuilder);
        LivePkSimpleInfoBean pkSimpleInfo = buildPkSimpleInfo(getAmusementResp.target().getLiveFunLivePk(), param);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ResponseGetAmusementInfoResult.builder()
                .amusementBaseInfoBean(amusementBaseInfoBean)
                .amusementSeatBaseInfoBeans(amusementSeatBaseInfoBeans)
                .teamWar(teamWarBuilder.build())
                .pkSimpleInfo(pkSimpleInfo)
                .build());
    }

    private LivePkSimpleInfoBean buildPkSimpleInfo(LiveAmusementProto.LiveCrossRoomPKPlayingData liveFunLivePk, RequestGetAmusementInfoParam param) {
        if(liveFunLivePk == null || param.getUserId() <= 0) {
             return null;
        }

        // 获取Pk麦位信息
        Result<PkInfoResult> result = livePkService.pkInfo(param.getUserId(), PkInfoParam.builder()
                .liveId(param.getLiveId())
                .matchId(liveFunLivePk.getMatchId())
                .build());

        if (RpcResult.isFail(result)) {
            return null;
        }
        LivePkSimpleInfoBean simpleInfoBean = new LivePkSimpleInfoBean();

        PkData pkInfo = result.target().getPkData();
        PkLiveData targetPkLiveData = pkInfo == null ? null : pkInfo.getTargetPkLiveData();
        return simpleInfoBean
                .setMatchId(String.valueOf(liveFunLivePk.getMatchId()))
                .setTargetSeatList(buildAmusementSeatBaseInfoBeans(liveFunLivePk.getTargetSeatsList()))
                .setStatus((result.target().getPkInvitationData() != null) ? WaveLivePkStatusEnum.PK_INVITING_STATUS.getStatus() : pkInfo == null ? WaveLivePkStatusEnum.PK_NO_OPEN_STATUS.getStatus() : pkInfo.getStatus())
                .setLiveId(Optional.ofNullable(targetPkLiveData).map(target -> target.getLive() == null ? null : Long.parseLong(target.getLive().getId())).orElse(null));

    }

    private List<AmusementSeatBaseInfoBean> buildAmusementSeatBaseInfoBeans(List<LiveAmusementProto.LiveFunSeat> liveFunSeatsList) {

        //查询通话状态
        List<Long> userIds = liveFunSeatsList.stream().map(LiveAmusementProto.LiveFunSeat::getUserId).collect(Collectors.toList());
        Result<BatchUserVoiceCallingResp> result = bpVoiceCallService.batchUserVoiceCalling(userIds);
        Map<Long, Boolean> isVoiceCallingMap = new HashMap<>(userIds.size());
        if (RpcResult.isSuccess(result)) {
            isVoiceCallingMap = result.target().getUserVoiceCallings();
        }

        List<AmusementSeatBaseInfoBean> amusementSeatBaseInfoBeans = new ArrayList<>(liveFunSeatsList.size());
        for (LiveAmusementProto.LiveFunSeat liveFunSeat : liveFunSeatsList) {
            AmusementSeatBaseInfoBean.AmusementSeatBaseInfoBeanBuilder builder = AmusementSeatBaseInfoBean.builder()
                    .seatNo(liveFunSeat.getSeat())
                    .seatStatus(liveFunSeat.getState())
                    .userId(liveFunSeat.getUserId())
                    .speakState(liveFunSeat.getSpeakState() == 1)
                    .voiceUserId(liveFunSeat.getUniqueId())
                    .charm(new Double(liveFunSeat.getLiveFunGuestCharm()).intValue())
                    .teamWarMvp(liveFunSeat.getTeamWarMvp());

            Boolean isVoiceCalling = isVoiceCallingMap.get(liveFunSeat.getUserId());
            if (isVoiceCalling != null) {
                builder.userState(isVoiceCalling ? UserSeatStatusEnum.CALL.getValue() : UserSeatStatusEnum.NORMAL.getValue());
            }

            amusementSeatBaseInfoBeans.add(builder.build());
        }
        return amusementSeatBaseInfoBeans;
    }

    private void fillTeamWarInfo(LiveAmusementProto.ResponseLiveAmusementInfo target, TeamWarBean.TeamWarBeanBuilder teamWarBuilder) {
        LiveAmusementProto.LiveFunTeamWar liveFunTeamWar = target.getLiveFunTeamWar();
        teamWarBuilder.state(TeamWarStateEnum.bizValue2Wave(liveFunTeamWar.getState()).getValue())
                .remainingTime((int) liveFunTeamWar.getRemainingTime())
                .startTime(String.valueOf(liveFunTeamWar.getStartTime()));

        LiveAmusementProto.LiveFunTeamWarTeamInfo aTeamInfo = liveFunTeamWar.getATeamInfo();
        TeamInfoBean.TeamInfoBeanBuilder aTeamBuilder = TeamInfoBean.builder();
        aTeamBuilder.teamLevel(aTeamInfo.getTeamLevel())
                .charmValue(aTeamInfo.getCharmValue())
                .nextFullCharm(aTeamInfo.getNextFullCharm())
                .currentBaseCharm(aTeamInfo.getCurrentBaseCharm());
        teamWarBuilder.aTeamInfo(aTeamBuilder.build());

        LiveAmusementProto.LiveFunTeamWarTeamInfo bTeamInfo = liveFunTeamWar.getBTeamInfo();
        TeamInfoBean.TeamInfoBeanBuilder bTeamBuilder = TeamInfoBean.builder();
        bTeamBuilder.teamLevel(bTeamInfo.getTeamLevel())
                .charmValue(bTeamInfo.getCharmValue())
                .nextFullCharm(bTeamInfo.getNextFullCharm())
                .currentBaseCharm(bTeamInfo.getCurrentBaseCharm());
        teamWarBuilder.bTeamInfo(bTeamBuilder.build());
    }

    @Override
    public Result<ResponseGetApplyModeResult> getApplyMode(RequestGetApplyModeParam param) {
        long liveId = param.getLiveId();
        Result<GetLiveResult> getLiveResp = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        if (getLiveResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy get live info error, liveId: {}, rCode: {}", liveId, getLiveResp.rCode());
            return new Result<>(IAmusementServiceRemote.GET_APPLY_MODE_ERROR, null);
        }

        Long liveRoomId = getLiveResp.target().getLive().getLiveRoomId();
        Result<LiveAmusementCommonProto.ResponseGetliveRoomMicMode> getMicResp = getSpringInterfaceProxyBean(LiveAmusementCommonService.class).getliveRoomMicMode(liveRoomId);
        if (getMicResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy get live room mic mode error, liveRoomId: {}, rCode: {}", liveRoomId, getMicResp.rCode());
            return new Result<>(IAmusementServiceRemote.GET_APPLY_MODE_ERROR, null);
        }

        ApplyModeEnum applyModeEnum;

        if (getMicResp.target().getMicMode() == 1) {
            applyModeEnum = ApplyModeEnum.APPLY;
        } else {
            applyModeEnum = ApplyModeEnum.FREE;
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ResponseGetApplyModeResult.builder()
                .applyModeEnum(applyModeEnum)
                .build());
    }

    @Override
    public Result<Void> manageSeat(RequestManageSeatParam param) {

        long liveId = param.getLiveId();
        int operation = param.getOperation();
        int seat = param.getSeat();


        Result<Void> resp = hyNewLiveAmusementService.manageSeat(liveId, operation, seat);

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy manageSeat error, liveId: {}, operation: {}, seat: {}, rCode: {}", liveId, operation, seat, resp.rCode());
            return new Result<>(IAmusementServiceRemote.MANAGE_SEAT_ERROR, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GetUserSeatResult> userLiveSeat(RequestGetUserSeatParam param) {

        long liveId = param.getLiveId();
        long userId = param.getUserId();

        Result<OperationMessageProto.ResponseUserLiveSeat> resp = operationMessageService.userLiveSeat(liveId, userId);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy user live seat error, liveId: {}, userId: {}, rCode: {}", liveId, userId, resp.rCode());
            return new Result<>(IAmusementServiceRemote.GET_USER_SEAT_ERROR, null);
        }

        int seat = resp.target().getSeat();
        GetUserSeatResult getUserSeatResult = new GetUserSeatResult();
        getUserSeatResult.setSeat(seat);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, getUserSeatResult);
    }

    @Override
    public Result<Integer> manageGuest(RequestManageGuestParam param) {

        Result<LiveAmusementProto.ResponseManageGuest> resp = hyNewLiveAmusementService.manageGuest(param.getLiveId(), param.getOperation(), param.getGuestUserId(), param.getOperateUserId());

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy manage guest error, liveId: {}, operation: {}, guestUserId: {}, operateUserId: {}, rCode: {}",
                    param.getLiveId(), param.getOperation(), param.getGuestUserId(), param.getOperateUserId(), resp.rCode());

            //rCode == 6 : 重复操作，直接给前端返回成功即可
            if (resp.rCode() == 6) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }

            String msg;
            switch (resp.rCode()) {
                case 1:
                    msg = "参数非法";
                    break;
                case 2:
                    msg = "嘉宾已主动下座";
                    break;
                case 3:
                    msg = "座位已满";
                    break;
                case 4:
                    msg = "不是娱乐模式";
                    break;
                case 5:
                    msg = "心动时刻中用户不能上麦";
                    break;
                case 7:
                    msg = "嘉宾已关闭了自己的麦";
                    break;
                case 8:
                    msg = "嘉宾已闭麦";
                    break;
                case 9:
                    msg = "嘉宾已开麦";
                    break;
                case 10:
                    msg = "权限不够，无法关闭该用户的麦";
                    break;
                case 11:
                    msg = "系统繁忙，请稍后再试";
                    break;
                case 12:
                    msg = "已认证房间主持才能上1号麦，其余麦位已满";
                    break;
                case 13:
                    msg = "房间成员才能排上2-7号麦，其余麦位已满";
                    break;
                case 14:
                    msg = "男/女生只能上男/女友房间的2-7号麦";
                    break;
                case 15:
                    msg = "只有该房间的签约成员才能上1-7号麦哦～";
                    break;
                default:
                    msg = "内部异常，请联系管理员";
                    break;
            }

            Result<Integer> result = new Result<>(IAmusementServiceRemote.MANAGE_GUEST_ERROR, null);
            result.setMessage(msg);
            return result;
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> guestOperation(RequestGuestOperationParam param) {
        long liveId = param.getLiveId();
        long guestUserId = param.getGuestUserId();
        WaveGuestOperationTypeEnum operation = param.getOperationEnum();
        int terminalType = param.getTerminalType();

        // 当 operation = ON_SPEAKING 时，用户存在两个操作路径
        // 1. 用户申请上麦 --> seatNo = null, 走下面的 guestOperation 方法，然后自动帮用户上麦
        // 2. 用户右键麦位上麦 --> seatNo = 用户指定的麦位, 走 micNumSwitch 方法
        if ((operation == WaveGuestOperationTypeEnum.ON_SPEAKING && null != param.getSeatNo())
                || operation == WaveGuestOperationTypeEnum.SEAT_REPLACE) {
            // fix 当operation=SEAT_REPLACE，为了兼容前端seatNo可能会传 null，
            int seatNo = null == param.getSeatNo() ? 0 : param.getSeatNo();

            int type = operation == WaveGuestOperationTypeEnum.ON_SPEAKING ? 1 : 0;
            Result<LiveAmusementCommonProto.ResponseMicNumSwitch> resp =
                    getSpringInterfaceProxyBean(LiveAmusementCommonService.class).micNumSwitch(liveId, type, guestUserId, seatNo);

            if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("guest operation error1, liveId: {}, guestUserId: {}, operation: {}, seatNo: {}, rCode: {}",
                        liveId, guestUserId, operation, seatNo, resp.rCode());
                String msg;
                switch (resp.rCode()) {
                    case 1:
                        msg = "参数非法";
                        break;
                    case 2:
                        msg = "嘉宾已主动下座";
                        break;
                    case 3:
                        msg = "座位不是空位";
                        break;
                    case 4:
                        msg = "不是娱乐模式";
                        break;
                    case 5:
                        msg = "心动时刻中用户不能上麦";
                        break;
                    case 6:
                        msg = "只有该房间签约成员才能上1-7号麦哦～";
                        break;
                    case 11:
                        msg = "系统繁忙，请稍后再试";
                        break;
                    case 12:
                        msg = "这个位置已经有人啦!";
                        break;
                    default:
                        msg = "内部异常，请联系管理员";
                        break;
                }

                Result<Void> res = new Result<>();
                res.setRCode(IAmusementServiceRemote.GUEST_OPERATION_ERROR);
                res.setMessage(msg);
                return res;
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        // 黑叶的拒绝邀请上麦操作，执行的是拒绝接口
        if (operation.equals(WaveGuestOperationTypeEnum.REFUSE_HOLD_MICROPHONE)) {
            Result<UserInviteProto.ResponseUserInviteOnCallClose> result =
                    userInviteService.userInviteOnCallClose(param.getGuestUserId());
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("userInviteOnCallClose error, guestUserId: {}, rCode: {}", guestUserId, result.rCode());
                return new Result<>(IAmusementServiceRemote.GUEST_OPERATION_ERROR, null);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        //获取业务操作类型
        int bizOperationType = GuestOperationTypeMapping.getBizOperationType(
                //operation = 6、嘉宾直接上麦时，相当于operation=1，嘉宾申请上麦，  下面会直接帮嘉宾直接自动上麦
                (operation == WaveGuestOperationTypeEnum.ON_SPEAKING ? WaveGuestOperationTypeEnum.APPLY_FOR_SPEAKING : operation).getType()
        );
        Result<Void> resp = hyNewLiveAmusementService.guestOperation(param.getLiveId(),
                param.getGuestUserId(), bizOperationType, param.getTerminalType());

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("guest operation error2, liveId: {}, guestUserId: {}, operation: {}, terminalType: {}, rCode: {}",
                    liveId, guestUserId, operation, terminalType, resp.rCode());

            //已经开麦和已经闭麦的情况下直接，直接返回成功
            if (resp.rCode() == 7 || resp.rCode() == 8) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }

            String msg;
            switch (resp.rCode()) {
                case 1:
                    msg = "参数错误";
                    break;
                case 2:
                    msg = "您已下麦";
                    break;
                case 3:
                    msg = "排麦位置已满";
                    break;
                case 4:
                    msg = "房主还没开启娱乐模式";
                    break;
                case 5:
                    msg = "你已经在座位上了";
                    break;
                case 6:
                    msg = "房主已关闭了你的麦";
                    break;
                case 9:
                    msg = "你已经在排队了";
                    break;
                case 10:
                    msg = "请在客户端端取消排麦";
                    break;
                case 11:
                    msg = "系统繁忙，请稍后再试";
                    break;
                case 12:
                    msg = "已经在主持位上，不能申请排麦";
                    break;
                case 13:
                    msg = "被禁止上麦";
                    break;
                default:
                    msg = "内部异常，请联系管理员";
                    break;
            }
            Result<Void> res = new Result<>();
            res.setRCode(IAmusementServiceRemote.GUEST_OPERATION_ERROR);
            res.setMessage(msg);
            return res;
        }

        // 嘉宾自动上麦
        autoAgreeForApplySpeaking(param);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }


    @Override
    public Result<Void> changeApplyMode(RequestChangeApplyModeParam param) {

        Result<LiveAmusementCommonProto.ResponseLiveRoomMicModeSwitch> resp = getMethodParamAndResultAdapter(LiveAmusementCommonService.class)
                .liveRoomMicModeSwitch(param.getLiveRoomId(), param.getNjId(), param.getLiveId(), param.getApplyMode());

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("change apply mode error, liveRoomId: {}, njId: {}, liveId: {}, applyMode: {}, rCode: {}",
                    param.getLiveRoomId(), param.getNjId(), param.getLiveId(), param.getApplyMode(), resp.rCode());
            return new Result<>(IAmusementServiceRemote.CHANGE_APPLY_MODE_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> removeUserAmusementInfo(RequestRemoveAmuseInfoParam removeAmusementInfoParam) {
        Result<Void> resp = hyNewLiveAmusementService.removeUserAmusementInfo(removeAmusementInfoParam.getLiveId(), removeAmusementInfoParam.getUserId());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy remove user amusement info error, liveId: {}, userId: {}, rCode: {}",
                    removeAmusementInfoParam.getLiveId(), removeAmusementInfoParam.getUserId(), resp.rCode());
            return new Result<>(IAmusementServiceRemote.REMOVE_USER_AMUSEMENT_INFO_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> liveAmusementModeSwitch(LiveAmusementModeSwitchRequestParam param) {
        long liveId = param.getLiveId();
        long njId = param.getNjId();
        long liveRoomId = param.getLiveRoomId();
        int operation = param.getOperation();
        Result<Void> resp = hyNewLiveAmusementService.liveAmusementModeSwitch(liveId, njId, liveRoomId, operation, param.getFunModeType());
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return new Result<>(IAmusementServiceRemote.LIVE_AMUSEMENT_MODE_SWITCH_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<WaitingUsersResult> getWaitingUsers(WaitingUsersParams params) {
        IRemoteMethodParamAndResultAdapter waitingUserAdapter = getMethodParamAndResultAdapter(HyWaitingUserAdapter.class);
        return (Result<WaitingUsersResult>) waitingUserAdapter.convertResult(hyNewLiveAmusementService.waitingUsers(params.getLiveId(), 0L));
    }

    @Override
    public Result<Void> clearCharm(RequestClearCharmParams param) {
        Result<Void> clearResp = hyNewLiveAmusementService.clearCharm(param.getLiveId());
        if (clearResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy clear charm value error, liveId: {}, rCode: {}", param.getLiveId(), clearResp.rCode());
            return new Result<>(IAmusementServiceRemote.CLEAR_CHARM_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> inviteOnCall(RequestInviteOnCallParam data) {
        PPliveBusiness.structPlayerRoomInfo.Builder roomInfo = PPliveBusiness.structPlayerRoomInfo.newBuilder();
        //查询邀请人的用户信息，推给被邀请人
        PPliveBusiness.structPPSimpleUser user = this.buildPPSimpleUserNoNew(data.getUser());
        roomInfo.setUser(user);
        //查询直播间房主
        long njIdByLiveId = data.getLive().getUserId();
        //角色 1-房主 2-管理员
        roomInfo.setRole(data.getUserId() == njIdByLiveId ? 1 : 2);
        roomInfo.setWindowCloseTime(amusementConfig.getHy().getInviteOnCallWindowCloseTime());
        //陪玩当前直播间id
        roomInfo.setLiveId(data.getLiveId());
        roomInfo.setInvitePlayerType(1);

        //发送邀请抱TA上麦推送
        this.pushMessage(data.getTargetUserId(), CMD_INVITE_ON_CALL, roomInfo.build().toByteString());
        getSpringInterfaceProxyBean(UserInviteService.class).setInviteOnCallCheck(data.getUserId(), data.getTargetUserId());
        PushInviteOnSeatEventVo pushInviteOnSeatEventVo = new PushInviteOnSeatEventVo();
        pushInviteOnSeatEventVo.setFromUserId(String.valueOf(user.getUserId()));
        pushInviteOnSeatEventVo.setToUserId(String.valueOf(data.getTargetUserId()));
        pushInviteOnSeatEventVo.setFromUserName(user.getName());
        pushInviteOnSeatEventVo.setLiveId(String.valueOf(data.getLiveId()));
        amusementPushManager.pushInviteOnSeatEvent(pushInviteOnSeatEventVo, BusinessEvnEnum.HEI_YE.getAppId());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> insertLitchiRank(RequestInsertLitchiRankParam rankParam) {
        Result<Void> insertRankResp = hyNewLiveAmusementService.insertLitchiRank(rankParam.getLiveId());
        if (insertRankResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy insert litchi rank not success, liveId: {}, rCode: {}", rankParam.getLiveId(), insertRankResp.rCode());
            return new Result<>(IAmusementServiceRemote.INSERT_LITCHI_RANK_ERROR, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> reportOnMicData(ReportOnMicDataRequest request) {
        Result<UserDayMapProto.ResponseOnMicHeartBeat> result
                = ppUserDayMapService.onMicHeartBeat(request.getUserId(), request.getLiveId());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("Hy reportOnMicData fail, userId={}`liveId={}`rCode={}", request.getUserId(), request.getLiveId(), result.rCode());
            return new Result<>(IAmusementServiceRemote.REPORT_ON_MIC_DATA_FAIL, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 黑叶空实现
     *
     * @param userId
     * @return
     */
    @Override
    public Result<UserPlayStatusEnum> getUserPlaySate(long userId) {
        Result<VoiceCallingResp> result = bpVoiceCallService.userVoiceCalling(userId);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(IAmusementServiceRemote.GET_USER_PLAY_STATE_ERROR);
        }

        return RpcResult.success(result.target().isCalling()
                ? UserPlayStatusEnum.VOICE_CALL_ING
                : UserPlayStatusEnum.NONE);
    }

    @Override
    public Result<Void> incrUserKey(long userId, String key, int value) {
        Result<UserDayMapProto.ResponseIncrUserKey> result = ppUserDayMapService.incrUserKey(userId, key, value);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(INCR_USER_KEY_ERROR);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> syncUserVerifyNo(long userId, String orderNo) {
        NewLiveSeatUserVerifyAmusementProto.SyncUserVerifyNoRequest request = NewLiveSeatUserVerifyAmusementProto.SyncUserVerifyNoRequest.newBuilder()
                .setUserId(userId)
                .setOrderNo(orderNo)
                .setOperation(OPERATION)
                .build();
        Result<NewLiveSeatUserVerifyAmusementProto.ResponseSyncUserVerifyNo> result =
                newLiveSeatUserVerifyService.syncUserVerifyNo(request);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(SYNC_USER_VERIFY_NO_ERROR);
        }
        return RpcResult.success();
    }

    @Override
    public Result<ResponseGetAllSeatsInfo> getAllSeatsInfo(long liveId) {
        Result<OperationMessageProto.ResponseGetAllSeatsInfo> result = operationMessageService.getAllSeatsInfo(liveId);
        if (RpcResult.isFail(result)) {
            return new Result<>(IAmusementServiceRemote.GET_ALL_SEATS_INFO_FAIL, null);
        }
        List<OperationMessageRequestProto.SeatInfo> seatInfoList = result.target().getSeatInfoListList();
        List<SeatInfoBean> seatRes = seatInfoList.stream().map(info -> {
            return new SeatInfoBean().setSeatNo(info.getSeatNo()).setUserId(info.getUserId());
        }).collect(Collectors.toList());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, new ResponseGetAllSeatsInfo().setSeatInfoList(seatRes));
    }

    public PPliveBusiness.structPPSimpleUser buildPPSimpleUserNoNew(SimpleUser user) {
        if (Objects.nonNull(user)) {
            String uriFromUrl = UrlUtils.getUriFromUrl(user.getAvatar());
            String imageThumbUrl = ImgUtil.getImageThumbUrl(uriFromUrl, THUMB_WIDTH, THUMB_HEIGHT);
            LZModelsPtlbuf.photo photo = buildPhoto(buildPortraitPath(uriFromUrl), buildPortraitPath(imageThumbUrl));
            return PPliveBusiness.structPPSimpleUser.newBuilder()
                    .setUserId(user.getUserId())
                    .setName(user.getNickName())
                    .setGender(user.getGender())
                    .setAge(TimeUtil.calAge(Objects.isNull(user.getBirthday()) ? 0L : user.getBirthday().getTime()))
                    .setSignature(StringUtils.trimToEmpty(""))
                    .setPortrait(photo)
                    .build();
        }
        return null;
    }

    private LZModelsPtlbuf.photo buildPhoto(String cover, String thumb) {
        LZModelsPtlbuf.photo.Builder pBuilder = LZModelsPtlbuf.photo.newBuilder();
        pBuilder.setUrl(amusementConfig.getHy().getCdnHost());
        pBuilder.setOriginal(buildImage(cover, PORTRAIT_BIG_WIDTH, PORTRAIT_BIG_HEIGHT));
        pBuilder.setThumb(buildImage(thumb, 0, 0));
        return pBuilder.build();
    }

    private LZModelsPtlbuf.image buildImage(String file, int width, int height) {
        LZModelsPtlbuf.image.Builder builder = LZModelsPtlbuf.image.newBuilder();
        if (file != null) {
            if (!file.startsWith("/")) {
                file = "/" + file;
            }
            builder.setFile(ImgUtil.getImageThumbUrl(file, width, height));
        }
        builder.setWidth(width);
        builder.setHeight(height);
        return builder.build();
    }


    /**
     * 黑叶自动同意上麦
     *
     * @param param 请求参数
     */
    private void autoAgreeForApplySpeaking(RequestGuestOperationParam param) {
        if (param.getOperationEnum() == WaveGuestOperationTypeEnum.ACCEPT_HOLD_MICROPHONE
                || param.getOperationEnum() == WaveGuestOperationTypeEnum.ON_SPEAKING) {
            Result<GetLiveResult> getLiveResp = liveService.getLiveByLocalCache(
                    GetLiveParam.builder().liveId(param.getLiveId()).build());
            if (getLiveResp.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                Live live = getLiveResp.target().getLive();
                Result<LiveAmusementProto.ResponseManageGuest> manageGuestResult =
                        hyNewLiveAmusementService.manageGuest(param.getLiveId(), 1, param.getGuestUserId(), live.getUserId());
                if (manageGuestResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                    log.warn("auto agree for apply speaking error, liveId: {}, guestUserId: {}, rCode: {}",
                            param.getLiveId(), param.getGuestUserId(), manageGuestResult.rCode());
                }
            }

        }
    }


    public static final String buildPortraitPath(String imgPath) {
        return imgPath == null ? null : "/" + imgPath;
    }


    public void pushMessage(long userId, int cmd, ByteString rawData) {
        try {
            PushMetadata pushMetadata = new PushMetadata();
            pushMetadata.setBusiness("heiye");
            Result result = hyMessagePushService.pushMessage2User(pushMetadata, userId, PpCommonCodec.genPushPayload(cmd, rawData));
            log.info("PushManager pushMessage, uid={}, cmd={}, rCode={}", userId, cmd, result.rCode());
        } catch (Exception e) {
            log.error("PushManager pushMessage error, msg={}, userId={}, cmd={}", e.getMessage(), userId, cmd, e);
        }
    }


}
