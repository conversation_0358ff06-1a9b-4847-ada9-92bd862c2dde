package fm.lizhi.ocean.wave.common.util;

import com.rits.cloning.Cloner;

/**
 * 克隆工具
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
public class CloneUtils {

    /**
     * cloning的性能高，单纯深拷贝的性能是ModelMapper的10倍左右
     */
    public static final Cloner CLONER = new Cloner();

    /**
     * 深拷贝
     *
     * @param source
     * @param <T>
     * @return 深拷贝后的数据
     */
    public static <T> T deepClone(T source) {
        return CLONER.deepClone(source);
    }
}
