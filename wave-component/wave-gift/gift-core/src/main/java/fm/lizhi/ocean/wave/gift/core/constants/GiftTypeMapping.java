package fm.lizhi.ocean.wave.gift.core.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import hy.fm.lizhi.live.gift.constants.GiftType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum GiftTypeMapping {

    //================ HY礼物类型枚举 ==================
    /**
     * 黑叶新版荔枝礼物
     */
    HY_NEW_VERSION_GIFT(WaveGiftType.NEW_VERSION_GIFT, BusinessEvnEnum.HEI_YE, GiftType.NEW_GIFT.getValue()),

    /**
     * 黑叶宝箱礼物
     */
    HY_BOX_GIFT(WaveGiftType.BOX_GIFT, BusinessEvnEnum.HEI_YE, GiftType.BOX_GIFT.getValue()),

    //================ PP礼物类型枚举 ==================
    /**
     * PP新版荔枝礼物
     */
    PP_NEW_VERSION_GIFT(WaveGiftType.NEW_VERSION_GIFT, BusinessEvnEnum.PP, pp.fm.lizhi.live.gift.constants.GiftType.NEW_GIFT.getValue()),

    /**
     * PP宝箱礼物
     */
    PP_BOX_GIFT(WaveGiftType.BOX_GIFT, BusinessEvnEnum.PP, pp.fm.lizhi.live.gift.constants.GiftType.BOX_GIFT.getValue()),

    //================ XM礼物类型枚举 ==================
    /**
     * XM新版荔枝礼物
     */
    XM_NEW_VERSION_GIFT(WaveGiftType.NEW_VERSION_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.NEW_GIFT.getValue()),

    /**
     * XM宝箱礼物
     */
    XM_BOX_GIFT(WaveGiftType.BOX_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.BOX_GIFT.getValue()),

    /**
     * xm形象礼物
     */
    XM_AVATAR_GIFT(WaveGiftType.AVATAR_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.AVATAR_GIFT.getValue()),

    /**
     * xm双人形象礼物
     */
    XM_DOUBLE_AVATAR_GIFT(WaveGiftType.DOUBLE_AVATAR_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.CP_AVATAR_GIFT.getValue()),

    /**
     * xm玩法类型
     */
    XM_PLAY_TYPE_GIFT(WaveGiftType.SPECIAL_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.SPECIAL_GIFT.getValue()),

    /**
     * xm默契风铃
     */
    XM_WIND_BELL_GIFT(WaveGiftType.WIND_CHIMES_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.WIND_CHIMES.getValue()),

    /**
     * 双人形象礼物
     */
    XM_CP_AVATAR_GIFT(WaveGiftType.CP_AVATAR_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.CP_AVATAR_GIFT.getValue()),

    /**
     * 形象礼物宝箱礼物
     */
    XM_AVATAR_BOX_GIFT(WaveGiftType.AVATAR_BOX_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.AVATAR_BOX_GIFT.getValue()),

    /**
     * 双人形象宝箱礼物
     */
    XM_CP_AVATAR_BOX_GIFT(WaveGiftType.CP_AVATAR_BOX_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.CP_AVATAR_BOX_GIFT.getValue()),

    /**
     * AI礼物
     */
    XM_AI_GIFT(WaveGiftType.AI_GIFT, BusinessEvnEnum.XIMI, xm.fm.lizhi.live.gift.constants.GiftType.AI_GIFT.getValue()),
    ;

    GiftTypeMapping(int waveGiftType, BusinessEvnEnum businessEvn, int bizGiftType) {
        this.waveGiftType = waveGiftType;
        this.businessEnv = businessEvn;
        this.bizGiftType = bizGiftType;
    }

    /**
     * 平台礼物类型
     */
    private final int waveGiftType;

    /**
     * 业务类型
     */
    private final BusinessEvnEnum businessEnv;

    /**
     * 业务礼物类型
     */
    private final int bizGiftType;


    /**
     * 业务方礼物类型转平台礼物类型
     *
     * @param bizGiftType 业务礼物类型
     * @param businessEnv 业务类型
     * @return 平台礼物类型
     */
    public static int getWaveGiftType(int bizGiftType, BusinessEvnEnum businessEnv) {
        for (GiftTypeMapping typeMapping : values()) {
            if (typeMapping.businessEnv != businessEnv) {
                //不是同业务的，过滤掉
                continue;
            }

            //礼物类型匹配即是目标类型
            if (typeMapping.getBizGiftType() == bizGiftType) {
                return typeMapping.getWaveGiftType();
            }
        }
        return WaveGiftType.UNKNOWN_GIFT;
    }

    /**
     * 业务方礼物类型转平台礼物类型
     *
     * @param waveGiftType 平台业务礼物类型
     * @param businessEvn  环境
     * @return 业务礼物类型
     */
    public static int getBizGiftType(int waveGiftType, BusinessEvnEnum businessEvn) {
        for (GiftTypeMapping typeMapping : values()) {
            if (typeMapping.businessEnv != businessEvn) {
                //不是同业务的，过滤掉
                continue;
            }

            //礼物类型匹配即是目标类型, 平台礼物类型是唯一的，对应业务的礼物类型也是唯一的
            if (typeMapping.getWaveGiftType() == waveGiftType) {
                return typeMapping.getBizGiftType();
            }
        }

        log.warn("GiftTypeMapping not found,GiftTypeMapping={},businessEvn={}", waveGiftType, businessEvn.getBusinessEnv());
        //找不到对应的礼物类型，抛出异常
        throw new RuntimeException("找不到合适的业务礼物类型");
    }

}
