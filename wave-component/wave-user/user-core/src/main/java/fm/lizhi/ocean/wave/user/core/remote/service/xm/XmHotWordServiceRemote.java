package fm.lizhi.ocean.wave.user.core.remote.service.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.core.remote.service.IHotWordsServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
@Component
public class XmHotWordServiceRemote extends RemoteServiceInvokeFacade implements IHotWordsServiceRemote {

    private static final ExecutorService HOT_WORD_EXECUTOR = ThreadUtils.getTtlExecutors(
            "xm-hot-word-executor", 2, 5, new LinkedBlockingQueue<Runnable>(1000));

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }

    @Override
    public Result<Void> updateHotWordsSearchCount(String content) {
//        HOT_WORD_EXECUTOR.submit(()->{
//            try {
//                hotWordsManagementService.updateHotWordsSearchCount(content);
//            } catch (Exception e) {
//                log.warn("xm updateHotWordsSearchCount error,content={}", content, e);
//            }
//        });
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,null);
    }
}
