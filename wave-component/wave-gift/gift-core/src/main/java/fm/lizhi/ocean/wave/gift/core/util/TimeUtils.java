package fm.lizhi.ocean.wave.gift.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Random;

/**
 * 时间工具类
 * Created in 2017-12-14 14:19.
 *
 * <AUTHOR>
 */
public class TimeUtils {
    private static Logger logger = LoggerFactory.getLogger(TimeUtils.class);

    /**
     * 高峰开始时间，晚上7点
     */
    private static int peakStart = 19;

    /**
     * 高峰结束时间，凌晨1点
     */
    private static int peakEnd = 1;

    private static Random random = new Random();

    /**
     * 获取过期时间
     *
     * @param expectTime 期望的过期时间
     * @return 过期时间 + 高峰偏移 + 随机值（分散过期），非高峰期会偏移到高峰过期的情况，暂时不考虑。
     */
    public static int getExpire(int expectTime) {
        int result = expectTime;

        // 跳过高峰时段
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        int offset = 0;
        if (hour >= peakStart || hour <= peakEnd) {
            offset = 7 * 60 * 60;
        }

        // 加上偏移值，避开高峰
        result += offset;

        // 加上随机值，分散过期，1小时范围内
        int number = random.nextInt(3600);
        result += number;
        //logger.info("expectTime:{}, offset:{}, number:{}, result:{}", expectTime, offset, number, result);
        return result;
    }
}
