package fm.lizhi.ocean.wave.live.core.extension.enterlive.biz.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.biz.AbstractEnterNoticeMsgSender;
import fm.lizhi.ocean.wave.live.core.remote.result.mount.GetUserUsingMountResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description: pp进房公告发送类
 * @author: guoyibin
 * @create: 2023/08/29 21:50
 */
@Slf4j
@Component
public class PpEnterNoticeMsgSender extends AbstractEnterNoticeMsgSender {

    @Autowired
    private LiveConfig liveConfig;

    /**
     * 控制最大消息队列长度
     */
    private AtomicInteger rc = new AtomicInteger(0);
    /**
     * 控制最大消息队列长度
     */
    private AtomicInteger rcVehicle = new AtomicInteger(0);
    private ConcurrentLinkedQueue<String> ppNoticeQ;
    private ConcurrentLinkedQueue<String> ppVehicleNoticeQ;
    private ExecutorService executorService;
    private ExecutorService vehicleExecutorService;

    @PostConstruct
    public void init() {
        /**
         * 初始化：1、缓存服务接口；2、初始化公告模板
         */

        ppNoticeQ = new ConcurrentLinkedQueue<>();
        executorService = ThreadUtils.getTtlExecutors("pp-save-enter-notice-worker");
        for (int count = 0; count < liveConfig.getPp().getSendEnterNoticeThreadCount(); ++count) {
            executorService.execute(RunnableWrapper.of(this::getNoticeAndSend));
        }


        //座驾的队列
        ppVehicleNoticeQ = new ConcurrentLinkedQueue<>();
        vehicleExecutorService = ThreadUtils.getTtlExecutors("pp-vehicle-save-enter-notice-worker");
        for (int count = 0; count < liveConfig.getPp().getSendEnterNoticeThreadCount(); ++count) {
            vehicleExecutorService.execute(RunnableWrapper.of(this::getVehicleNoticeAndSend));
        }
    }


    @Override
    protected void offerNoticeMsgToQueue(GetUserUsingMountResponse mount, String notice) {
        if (mount != null) {
            //座驾
            int index = rcVehicle.getAndIncrement();//加一，做最大限制

            if (index < liveConfig.getPp().getVehicleEnterNoticeQueueMaxSize()) {
                ppVehicleNoticeQ.offer(notice);
            }
        } else {

            int index = rc.getAndIncrement();//加一，做最大限制
            if (index < liveConfig.getPp().getEnterNoticeUserMaxLen()) {
                ppNoticeQ.offer(notice);
            }
        }
    }

    @Override
    protected boolean isEnterNoticeSwitchOn() {
        return liveConfig.getPp().getEnterNoticeSwitch();
    }

    @Override
    protected String getVehicleNoticeFromQueue() {
        String notice = null;
        try {
            notice = ppVehicleNoticeQ.poll();
            rcVehicle.getAndDecrement();//减一，作为最大容量统计
        } catch (Exception e) {
            log.error("pp getVehicleNoticeAndSend error:", e);
        }
        return notice;
    }

    @Override
    protected String getNoticeFromQueue() {
        String notice = null;
        try {
            notice = ppNoticeQ.poll();
            rc.getAndDecrement();//减一，作为最大容量统计
        } catch (Exception e) {
            log.error("pp getNoticeAndSend error:", e);
        }
        return notice;
    }

    @Override
    protected BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
