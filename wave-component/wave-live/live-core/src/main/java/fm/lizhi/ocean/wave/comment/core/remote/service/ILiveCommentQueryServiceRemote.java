package fm.lizhi.ocean.wave.comment.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.remote.bean.CommentBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.CommentStyle;
import fm.lizhi.ocean.wave.comment.core.remote.param.AddCommentImageParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetTransientCommentReviewParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetCommentWithServerTimeResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveCommentResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTransientCommentReviewResult;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

import java.util.List;

/**
 * 直播评论接口适配
 * <AUTHOR>
 */
public interface ILiveCommentQueryServiceRemote extends IBaseRemoteServiceInvoker {

    /**
     * 获取评论列表
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    Result<GetLiveCommentResult> getCommentWithServerTime(GetCommentWithServerTimeParam param);

}
