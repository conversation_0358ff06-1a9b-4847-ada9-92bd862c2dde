package fm.lizhi.ocean.wave.user.bean;

import fm.lizhi.ocean.wave.user.constant.UserBehaviorsEnum;
import lombok.Data;

/**
 * describe: 用户行为数据
 */
@Data
public class UserBehaviorsBean {

    /**
     * 行为类型
     */
    private String dataStr;

    /**
     * 实际数据，根据具体行为类型而定
     */
    private int type;

    /**
     * data转成string
     */
    private String data;

    /**
     * 用户行为状态
     */
    private UserBehaviorsEnum behaviorsEnum;

}
