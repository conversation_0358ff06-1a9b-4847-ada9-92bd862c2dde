package fm.lizhi.ocean.wave.permission.core.config;

import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class PpPermissionConfig {
    /**
     * 平台-业务角色映射
     */
    private Map<Integer, Integer> roleMappings = new HashMap<>();

    /**
     * 投票权限最小版本号
     */
    private int voteMinVersion;

    /**
     * 蒙面权限最小版本号
     */
    private int maskMinVersion;

    /**
     * 拥有蒙面权限的直播类型，配置的是平台直播模式枚举
     * @see LiveModeEnum
     */
    private List<Integer> hasMaskLiveModes;
    /**
     * 个播品类
     */
    private long personalLiveCategoryId = 10012;
    /**
     * pgc厅品类id 小哥哥，小姐姐，点唱，交友，电台互动
     */
    private String pgcCategoryIds="10003,10004,10010,10002,10012";

}