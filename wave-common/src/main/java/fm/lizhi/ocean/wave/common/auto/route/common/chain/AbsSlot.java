package fm.lizhi.ocean.wave.common.auto.route.common.chain;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/20 19:10
 */
public abstract class AbsSlot implements ISlot{

    @Getter
    @Setter
    private AbsSlot next = null;

    /**
     * 执行下一个节点
     * @param param
     * @return
     * @throws Exception
     */
    protected Result<?> invokeNext(ChainInvokingParam param) throws Exception{
        if (next != null) {
            return next.invoke(param);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, null);
    }

}
