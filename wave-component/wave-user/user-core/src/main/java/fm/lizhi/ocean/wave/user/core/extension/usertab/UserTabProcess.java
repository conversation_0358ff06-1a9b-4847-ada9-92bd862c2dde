package fm.lizhi.ocean.wave.user.core.extension.usertab;

import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.user.core.manager.strategy.UserTabGetContext;

/**
 * 用户资料页tab处理类
 *
 * <AUTHOR>
 */
public interface UserTabProcess extends BusinessEnvAwareProcessor {
    /**
     * 判断是否支持音乐作品tab
     *
     * @param context
     * @return
     */
    public boolean isSupportMusicWorkTab(UserTabGetContext context);


    /**
     * 判断 是否支持歌单tab
     *
     * @param context
     * @return
     */
    public boolean isSupportSongListTab(UserTabGetContext context);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return UserTabProcess.class;
    }
}
