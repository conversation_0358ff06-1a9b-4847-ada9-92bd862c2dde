package fm.lizhi.ocean.wave.assistant.core.model.vo;

import fm.lizhi.ocean.wave.assistant.core.model.bean.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CheckInDetailVO {

    /**
     * 定排主播打卡列表
     */
    private List<CheckInDetailBean> checkInScheduledList;

    /**
     * 非定排主播打卡列表
     */
    private List<CheckInDetailBean> checkInNotScheduledList;

    /**
     * 统计数据
     */
    private CheckInTotalDataBean totalData;

    /**
     * 主持信息
     */
    private CheckInUserInfoBean hostInfo;

    /**
     * 厅主信息
     */
    private CheckInUserInfoBean njInfo;

    /**
     * 档期信息
     */
    private CheckInScheduleBean scheduleInfo;

    /**
     * 保存版本号，保存或者调账时需要带上
     */
    private Long checkInVersion;
}
