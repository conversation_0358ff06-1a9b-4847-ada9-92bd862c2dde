package fm.lizhi.ocean.wave.user.core.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.facade.UserServiceFacade;
import fm.lizhi.ocean.wave.common.pojo.UserInfoBean;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.auth.service.UserAuthService;
import fm.lizhi.ocean.wave.user.core.manager.UserManager;
import fm.lizhi.ocean.wave.user.export.api.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Slf4j
@Component
public class UserServiceFacadeImpl implements UserServiceFacade {

    @Autowired
    private UserManager userManager;

    @Autowired
    private UserAuthService userAuthService;


    @Override
    public Long getUserIdByToken(String token) {
        Result<Long> userIdResult = userAuthService.getUserIdByToken(token);
        if (RpcResult.isFail(userIdResult) || userIdResult.target() <= 0) {
            log.warn("getUserIdByToken fail. token={}", token);
            return null;
        }

        return userIdResult.target();
    }

    @Override
    public UserInfoBean getUserById(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        UserInfo userInfo = userManager.getUserById(userId);
        if (userInfo != null) {
            UserInfoBean bean = new UserInfoBean();
            bean.setBand(userInfo.getBand());
            bean.setNickName(userInfo.getName());
            return bean;
        }
        return null;
    }
}
