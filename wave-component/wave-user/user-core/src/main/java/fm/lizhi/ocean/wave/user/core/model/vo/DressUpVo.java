package fm.lizhi.ocean.wave.user.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class DressUpVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 封面
     */
    private String cover;


    /**
     * 剩余的时间
     * eq: 永久：0  过期：-1   具体到期时间：1219203
     */
    private Long remainTime;

    /**
     * 剩余的时间
     * eq: 永久   已过期   0天23小时12分
     */
    private String remainTimeDesc;

    /**
     * 是否是已经过期
     * -1过期 0：正常
     */
    private Integer status;

    /**
     * 0未使用，1使用中
     * 使用状态
     */
    private Integer useStatus;

    /**
     * 是否展示操作按钮
     */
    private boolean showButton;


}
