package fm.lizhi.ocean.wave.amusement.core.extension.config;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.enums.LiveModeEnum;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementCommonConfig;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.amusement.core.model.param.CheckUserVerifyParam;
import fm.lizhi.ocean.wave.amusement.core.remote.bean.ResponsePlayerCurSignNjBean;
import fm.lizhi.ocean.wave.amusement.core.remote.service.ILiveAmusementServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/21 15:04
 */
@Slf4j
@Component
public class HyConfigProcessor implements IConfigProcessor {

    @Autowired
    private AmusementConfig amusementConfig;
    @MyAutowired
    private ILiveAmusementServiceRemote liveAmusementServiceRemote;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public AmusementCommonConfig getAmusementCommonConfig() {
        return amusementConfig.getHy();
    }

    @Override
    public boolean needSeatVerify(CheckUserVerifyParam request) {
        AmusementCommonConfig amusementCommonConfig = getAmusementCommonConfig();
        return amusementCommonConfig.isOnSeatVerifySwitch()
                && request.getLiveMode().getLiveMode() == LiveModeEnum.AMUSEMENT_MODE.getLiveModeId()
                && isUserPlayerSign(request.getUserId());
    }

    public boolean isUserPlayerSign(Long userId) {
        Result<ResponsePlayerCurSignNjBean> result = liveAmusementServiceRemote.playerCurSignNj(userId);
        if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("AmusementModeCheckManager isUserPlayerSign userId={},njId={}", userId, Optional.ofNullable(result.target()).map(ResponsePlayerCurSignNjBean::getNjId).orElse(0L));
            return !Long.valueOf(-1).equals(result.target().getNjId());
        }
        return false;
    }
}
