package fm.lizhi.ocean.wave.live.core.extension.enterlive.bean;

import lombok.Data;

import java.util.List;

/**
 * @description: 进房信息bean
 * @author: guoyibin
 * @create: 2023/08/30 15:43
 */
@Data
public class EnterInfoBean {

    private WealthInfoBean wealthInfoVo;

    /**
     * 用户Id
     */
    private long userId;
    /**
     * 主播的Id
     */
    private long liveId;
    /**
     * 进房的时间戳，微秒数
     */
    private long enterTime;

    /**
     * 是否机器人 0否 1是
     */
    private int automaton = 0;

    private String content;

    private String userCover;

    /**
     * 是否显式
     */
    private boolean explicit;

    private VipInfoBean vipInfoVo;

    private UserMountInfo userMountVo;

    /**
     * 西米的用户的vip在房状态
     */
    private Integer userRoomVipStatus;

    /**
     * 亲密关系进房特效JSON
     */
    private String closeFriendEnterRoomEffectJson;

    /**
     * vo结构是否为基础能力改造，会多下面2个字段
     * @return
     */
    private boolean basicAbilityUpgradeVersion;

    /**
     * 是否新用户
     */
    private boolean freshUser;

    /**
     * 麦上签约用户
     */
    private List<Long> playerIdList;
}
