package fm.lizhi.ocean.wave.gift.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.gift.core.manager.GiftEffectMsgHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 礼特特效消息处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${hy.kafka.consumer.enable}")
public class HyGiftEffectMsgConsumer {

    @Autowired
    private GiftEffectMsgHandler giftEffectMsgHandler;

    @KafkaHandler(topic = "lz_topic_hy_gift_effect_contain_end",
            group = "lz_topic_hy_gift_effect_contain_end_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handlerGiftEffectMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("hy.handlerGiftEffectMsg - receive gift effect msg,msg={}", msg);

            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
            giftEffectMsgHandler.processMsg(msg);
        } catch (Exception e) {
            log.error("hy.handlerGiftEffectMsg - json parse error,msg:{}", msg, e);
        }
    }
}
