package fm.lizhi.ocean.wave.amusement.core.config;

import lombok.Data;

@Data
public class XmAmusementConfigAmusement implements AmusementCommonConfig {

    /**
     * 是否限制海外IP
     * 关联原配置 app_hy_live
     */
    private boolean overseaLimitSwitch = true;

    /**
     * 海外用户手机App受限白名单
     * 关联原配置 app_hy_live
     */
    private boolean overseaPhoneNumSwitch = true;

    /**
     * 海外用户手机App受限名单
     * 关联原配置 app_hy_live
     */
    private String overseaPhoneNumAppWhiteList = "57333013";

    /**
     * 海外用户手机受限白名单
     * 关联原配置 app_hy_live
     */
    private String overseaPhoneNumWhiteList;

    /**
     * 海外IP测试白名单，json格式，key：userId，value：ip，示例：{"123456":"***************"}
     * 关联原配置 app_hy_live
     */
    private String overseaIpTestWhitelist;

    /**
     * 关联原配置 lz_hy_common_util namespace -> lz_hy_common_utils
     */
    private int socialOperationLimitWithoutPhone = 0;

    private String cdnHost = "http://cdnoffice.lizhi.fm/";

    /**
     * 邀请上麦弹窗关闭时间 单位:秒
     */
    private int inviteOnCallWindowCloseTime = 10;

    /**
     * 触发实名认证流程开关：true为开，false为关
     */
    private boolean onSeatVerifySwitch = false;

    /**
     * 排麦白名单权限
     */
    private String applyForSpeakingUserGroupConfig = "[]";

    /**
     * 麦位申请白名单
     */
    private String applySeatWhiteList = "";

    private String teamWarConfigJson = "{\n" +
            "    \"title\": \"魔法团战\",\n" +
            "    \"desc\": \"魔法团战开启后，麦上嘉宾自动分成左右两方，在规定时间内根据双方累积的魅力值进行PK，魅力值高的一方获得比赛胜利。\",\n" +
            "    \"theme\": \"\",\n" +
            "    \"extraTime\": 10,\n" +
            "    \"warTimes\": [\n" +
            "        \"30\",\"40\",\"60\"\n" +
            "    ]\n" +
            "}";

    /**
     * 魔法团战主题字数限制
     */
    public int teamWarThemeCount = 6;

    /**
     * 魔法团战主题机审失败文案
     */
    public String teamWarReviewFailMsg = "魔法团战主题文本不符合平台规范，请重新编辑";

    /**
     * 审核开关
     */
    public boolean isReviewSwitch = true;

    /**
     * 麦位操作频率限制开关，true：开启限制
     */
    private boolean operationLimitSwitch = true;

    /**
     * 一分钟最大操作次数
     */
    private int minuteMaxOperationCount = 30;

    /**
     * 语音通话最大收益
     */
    private long voiceCallMaxIncome = 110;

    /**
     * 最大通话时长，西米是正记时的，所以无限制，填写-1
     */
    private int maxCallTimeLimit = -1;

    /**
     * 0收入激励文案
     */
    private String zeroIncomeEncourageContent = "主动挂断或通话不满2分钟没有收益哦，下次和对方聊久一点吧~";

    /**
     * 0-最大收入激励文案
     */
    private String mediumIncomeEncourageContent = "提前准备些好玩的话题或才艺，吸引对方聊久些赚取更多收益吧！";

    /**
     * 最大收入激励文案
     */
    private String maxIncomeEncourageContent = "看来你们很聊得来哦！抓住机会继续私聊发展关系吧！";

    /**
     * 声网AppKey
     */
    private String agoraAppKey = "tX3skQGONJfhyygG";

    /**
     * 心动连线-解锁条件提示语
     */
    private String unlockRule = "收到对方一条私信就可以解锁哦";

    /**
     * 心动连线-超时时间
     */
    private Integer heartbeatCallTimeOut = 30;

    /**
     * 语音通话场景配置id
     */
    public long voiceCallSceneId = 5266965266079679615L;

    private String callTips = "";

    private boolean callPageShowSignCard = true;

    /**
     * 通话接收超时时间
     */
    private int voiceCallAcceptTimeout = 30;


}
