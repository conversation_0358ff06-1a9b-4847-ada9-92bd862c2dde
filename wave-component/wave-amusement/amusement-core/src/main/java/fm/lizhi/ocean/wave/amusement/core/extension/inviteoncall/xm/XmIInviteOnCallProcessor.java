package fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.xm;

import fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.IInviteOnCallProcessor;
import fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.bean.InviteOnCallPostBean;
import fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.bean.InviteOnCallPreBean;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/12 17:31
 */
@Component
@Slf4j
public class XmIInviteOnCallProcessor implements IInviteOnCallProcessor {

    @Override
    public boolean isSupport() {
        return false;
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }
}
