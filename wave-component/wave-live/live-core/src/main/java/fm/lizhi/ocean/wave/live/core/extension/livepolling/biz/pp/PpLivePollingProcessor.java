package fm.lizhi.ocean.wave.live.core.extension.livepolling.biz.pp;

import com.google.common.base.Objects;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.data.bean.UserBehaviorType;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.ILiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.pp.PpLiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.manager.LiveHeartbeatManager;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.LivePollingProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.bean.LivePollingPostBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.PpBehaviorKafkaProducer;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.bean.PpAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.bean.PpUserBehaviorBean;
import fm.lizhi.pp.decorate.api.call.CallService;
import fm.lizhi.pp.decorate.protocol.call.LiveCallProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@Slf4j
@Component
public class PpLivePollingProcessor implements LivePollingProcessor {

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private CallService callService;

    @Autowired
    private PpLiveHeartbeatProcessor factory;


    @Override
    public ResultVO<Void> postprocessor(LivePollingPostBean param) {
        if (isOldVersion()) {
            factory.reportLiveHeartbeat(param);
        }


        return ResultVO.success();
    }

    /**
     * 是否是旧版本
     */
    private boolean isOldVersion() {
        try {
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            return clientVersion < commonProviderConfig.getMinHeartbeatClientVersion();
        } catch (Exception e) {
            log.error("pp isOldVersion error: ", e);
            return true;
        }
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.PP;
    }

    @Override
    public CommonLiveConfig getLiveCommonConfig() {
        return liveConfig.getPp();
    }

    @Override
    public String getActualChannelId(long liveId, String currentChannelId) {
        Result<LiveCallProto.ResponseGetLineCode> getLineCodeResult = callService.getLineCode(liveId);
        if(getLineCodeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //获取失败直接置null，抛弃这一次的数据
            log.error("pp getActualChannelId error, liveId={}`currentChannelId={}`rCode={}", liveId, currentChannelId, getLineCodeResult.rCode());
            return null;
        }
        return getLineCodeResult.target().getLineCode();
    }
}
