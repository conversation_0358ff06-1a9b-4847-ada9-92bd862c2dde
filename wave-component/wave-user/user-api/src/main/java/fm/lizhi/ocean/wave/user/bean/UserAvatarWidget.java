package fm.lizhi.ocean.wave.user.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 用户头像框. PC读取动态头像框优先级如下:
 * <ol>
 *     <li>如果pcAniUrl有值, 则采用pcAniUrl</li>
 *     <li>如果svgaMaterialUrl有值且不为zip和pag, 则采用svgaMaterialUrl</li>
 *     <li>否则采用materialUrl和thumbUrl作为兜底</li>
 * </ol>
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAvatarWidget {

    /**
     * 头像框的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;

    /**
     * 物品名称
     */
    private String dressUpName;

    /**
     * svga素材地址
     */
    private String svgaMaterialUrl;

    /**
     * 过渡动效：svga素材地址
     */
    private String preSvgaMaterialUrl;

    /**
     * 素材地址
     */
    private String materialUrl;

    /**
     * 角标、缩略图
     */
    private String thumbUrl;

    /**
     * PC动画地址, 后台配SVGA或PAG素材时同步传的WebP文件, 可能为null
     */
    private String pcAniUrl;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
}
