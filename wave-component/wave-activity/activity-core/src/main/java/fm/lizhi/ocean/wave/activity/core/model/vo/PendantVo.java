package fm.lizhi.ocean.wave.activity.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class PendantVo {
    /**
     * 挂件id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;
    /**
     * 挂件图片
     */
    private String imageUrl;
    /**
     * 挂件动作
     */
    private String actionJson;
    /**
     * 优先级
     */
    private int priority;
    /**
     * 挂件类型，0-普通挂件，1-活动模版挂件
     */
    private int pendantType;

}
