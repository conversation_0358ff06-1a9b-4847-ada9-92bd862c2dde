package fm.lizhi.ocean.wave.live.core.extension.enterlive.biz;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.wave.api.comment.api.UserEnterNoticeService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.bean.EnterInfoBean;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.bean.UserMountInfo;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.bean.VipInfoBean;
import fm.lizhi.ocean.wave.live.core.extension.enterlive.bean.WealthInfoBean;
import fm.lizhi.ocean.wave.live.core.remote.result.mount.GetUserUsingMountResponse;
import fm.lizhi.ocean.wave.user.result.GetUserWealthResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description: 进房消息发送模板类
 * @author: guoyibin
 * @create: 2023/08/29 21:28
 */
@Slf4j
public abstract class AbstractEnterNoticeMsgSender {

    @Autowired
    private UserEnterNoticeService userEnterNoticeService;

    public void genEnterVoStrAndOffer(long liveId, long userId,
                                      GetUserWealthResult wealth, GetUserUsingMountResponse mount, String content, String userCover, VipInfoBean vipInfoVo,
                                      String svgaEffectInfoStr,Integer userRoomVipStatus) {
        EnterInfoBean enterVo = buildEnterNoticeInfo(liveId, userId, wealth, mount, content, userCover, vipInfoVo, svgaEffectInfoStr,userRoomVipStatus);
        fillEnterNoticeInfo(enterVo);
        String notice = JSON.toJSONString(enterVo);
        offerNoticeMsgToQueue(mount, notice);
    }

    /**
     * 将发送的信息存入队列，异步拉取
     * @param mount
     * @param notice
     */
    protected abstract void offerNoticeMsgToQueue(GetUserUsingMountResponse mount, String notice);

    /**
     * 获取发送进房公告的开关
     * @return
     */
    protected abstract boolean isEnterNoticeSwitchOn();

    /**
     * 从队列中获取座驾进房信息
     * @return
     */
    protected abstract String getVehicleNoticeFromQueue();

    /**
     * 从队列中获取进房信息
     * @return
     */
    protected abstract String getNoticeFromQueue();

    /**
     * 获取对应的业务线信息信息
     * @return
     */
    protected abstract BusinessEvnEnum getBusinessEnv();

    protected void getNoticeAndSend() {
        int max = 100;
        List<String> notices = new ArrayList<>(max);
        ContextUtils.setBusinessEvnEnum(this.getBusinessEnv());
        while (true) {
            if (!isEnterNoticeSwitchOn()) {
                return;
            }
            String notice = getNoticeFromQueue();
            if (notice == null) {
                //空了
                if (!notices.isEmpty()) {
                    //非空则发送
                    userEnterNoticeService.sendEnterRoomMsg(notices);
                    notices = new ArrayList<>(max);
                }
                try {
                    TimeUnit.NANOSECONDS.sleep(200);
                } catch (InterruptedException e) {
                    log.error("sleep error:", e);
                    Thread.currentThread().interrupt();
                }
                continue;
            }
            notices.add(notice);
            if (notices.size() == max) {
                userEnterNoticeService.sendEnterRoomMsg(notices);notices = new ArrayList<>(max);
                try {
                    TimeUnit.NANOSECONDS.sleep(200);
                } catch (InterruptedException e) {
                    log.error("sleep error:", e);
                    Thread.currentThread().interrupt();
                }
            }

        }
    }

    protected void getVehicleNoticeAndSend() {
        int max = 50;
        List<String> notices = new ArrayList<>(max);
        ContextUtils.setBusinessEvnEnum(this.getBusinessEnv());
        while (true) {
            if (!isEnterNoticeSwitchOn()) {
                return;
            }
            String notice = getVehicleNoticeFromQueue();
            if (notice == null) {
                //空了
                if (!notices.isEmpty()) {
                    //非空则发送
                    userEnterNoticeService.sendVehicleEnterRoomMsg(notices);
                    notices = new ArrayList<>(max);
                }
                try {
                    TimeUnit.NANOSECONDS.sleep(200);
                } catch (InterruptedException e) {
                    log.error("getVehicleNoticeAndSend sleep error:", e);
                    Thread.currentThread().interrupt();
                }
                continue;
            }
            notices.add(notice);
            if (notices.size() == max) {
                userEnterNoticeService.sendVehicleEnterRoomMsg(notices);
                notices = new ArrayList<>(max);
                try {
                    TimeUnit.NANOSECONDS.sleep(200);
                } catch (InterruptedException e) {
                    log.error("getVehicleNoticeAndSend sleep error:", e);
                    Thread.currentThread().interrupt();
                }
            }

        }
    }

    private EnterInfoBean buildEnterNoticeInfo(long liveId, long userId, GetUserWealthResult wealth, GetUserUsingMountResponse mount, String content, String userCover, VipInfoBean vipInfoVo, String svgaEffectInfoStr,Integer userRoomVipStatus) {
        EnterInfoBean enterVo = new EnterInfoBean();
        WealthInfoBean wealthInfoVo = new WealthInfoBean();
        if (wealth != null) {
            wealthInfoVo.setLevel(wealth.getLevel());
            if (wealth.getLevel() > 0) {
                wealthInfoVo.setAspect((float) wealth.getImageAspect());
                String imgUrl = wealth.getImageUrl();
                wealthInfoVo.setBageIcon(imgUrl);
            }
        }
        if (mount != null && mount.getUserId() > 0) {
            UserMountInfo vo = new UserMountInfo();
            vo.setLevel(mount.getLevel());
            vo.setSvgaAniURL(mount.getSvgaAniUrl());
            enterVo.setUserMountVo(vo);
        }
        if(userRoomVipStatus!=null){
            enterVo.setUserRoomVipStatus(userRoomVipStatus);
        }
        enterVo.setEnterTime(System.currentTimeMillis());
        enterVo.setLiveId(liveId);
        enterVo.setUserId(userId);
        enterVo.setWealthInfoVo(wealthInfoVo);
        enterVo.setContent(content);
        enterVo.setUserCover(userCover);
        enterVo.setExplicit(true);
        enterVo.setVipInfoVo(vipInfoVo);
        enterVo.setCloseFriendEnterRoomEffectJson(svgaEffectInfoStr);
        return enterVo;
    }

    /**
     * 完善一下进房公告
     * @param enterVo
     */
    protected void fillEnterNoticeInfo(EnterInfoBean enterVo) {

    }
}
