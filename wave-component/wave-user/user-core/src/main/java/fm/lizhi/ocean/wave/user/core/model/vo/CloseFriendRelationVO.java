package fm.lizhi.ocean.wave.user.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 密友
 */
@Data
public class CloseFriendRelationVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relationId;
    /**
     * 关系名称
     */
    private String relation;
    private String relationImgUrl;
    /**
     * 关系对方id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 对方名称
     */
    private String userName;
    private String avatar;
    /**
     * 等级
     */
    private int level;
    /**
     * 结成天数
     */
    private int formDay;
    /**
     * 宣言
     */
    private String declaration;

    /**
     * 性别
     */
    private int gender;
    /**
     * 亲密值
     */
    private long intimacyValue;


    /**
     * 关系差异化数据
     * @see fm.lizhi.ocean.wave.user.core.remote.bean.HyCloseFriendRelationExt
     * @see fm.lizhi.ocean.wave.user.core.remote.bean.PpCloseFriendRelationExt
     */
    private String relationExt;

    /**
     * 业务个性化配置
     * 黑叶 @see HyCloseFriendConfigV0
     * pp @see PpCloseFriendConfigV0
     */
    private String extConfigValue;

    @Data
    public static class HyCloseFriendConfigV0 {
        private String relationNameColor;       // 关系名称颜色
        private String nameColor;               // 昵称颜色
        private String levelColor;              // 等级颜色
        private String dayColor;                // 天数颜色
        private String declarationColor;        // 宣言颜色
        private String relationSkin;            // 关系皮肤
        private String avatarWidget;            // 头像框
        private String declarationBackground;   // 宣言背景
        private String relationNameBackground;  // 关系名称背景
        private String memorialName;            // 纪念物名称
    }

    @Data
    public static class PpCloseFriendConfigV0 {
        //关系ID
        private long id;
        private int type;//关系类型： 0 全部 1 常驻 2 活动
        private int priceCoin;//金币价格
        private String iconUrl;//图标URL
        private String bgUrl;//背景URL
        private String headerUrl;//头像框URL
        private String remarkBgUrl;//宣言背景URL
        private String timeBgColor;//时间背景颜色
        private String timeFontColor;//时间字体颜色
        private String nickFontColor;//昵称字体颜色
        private String remarkFontColor;//宣言字体颜色
        private long userPkgId;//用户关系包裹ID
        private int priority;//优先级
        private String patEffect;//拍一拍特效
        private String enterRoomEffect;//进房特效
        private int genderLimit;//性别限制
        private int amountLimit;//数量限制
        private int orderNum;//排序数字
        private String levelBgColor;//等级背景照片
    }

    @Data
    public static class HyCloseFriendRelationExtVO {

        /**
         * 终止发起方
         */
        private Long suspendInitiatorId;

        /**
         * 价值
         */
        private Integer memorialPrice;

        /**
         * 亲密值
         */
        private int intimacyValue;


        /**
         * 纪念物id
         */
        private Long memorialId;


        /**
         * 增加亲密值的最后更新时间
         */
        private long increaseTime;

        /**
         * 关系过期时间
         */
        private long expireTime;

        /**
         * 关系恢复时间
         */
        private long recoverTime;

        /**
         * 发起解除时间
         */
        private long suspendTime;

        /**
         * 类型 (1:普通关系,2:CP关系)
         */
        private Integer type;

        /**
         * 记录修改时间
         */
        private long modifyTime;

        /**
         * 创建时间
         */
        private long createTime;

        /**
         * 状态：1 : 生效 ,0 邀请中 2 冻结   -1 : 失效 , 3:隐藏
         */
        private Integer status;

        private boolean isFollower;

        private int priority;

        private boolean lock;

        private long hiddenOperatorId;

        private String memorialImage;

    }

    @Data
    public static class PpCloseFriendRelationExtVO {

    }
}
