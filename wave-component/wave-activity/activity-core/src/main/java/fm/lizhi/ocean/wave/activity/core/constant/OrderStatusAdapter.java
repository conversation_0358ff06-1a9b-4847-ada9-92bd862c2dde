package fm.lizhi.ocean.wave.activity.core.constant;

import fm.lizhi.pp.social.constant.accompanyorder.AccompanyOrderStatus;
import fm.lizhi.pp.social.constant.accompanyorder.OrderEndType;

/**
 * 点单状态适配器
 */
public class OrderStatusAdapter {

    /**
     * PP业务状态转平台状态
     *
     * @return 状态枚举
     */
    public static OrderStatusEnum ppBizStatus2WaveStatus(int orderStatus, long endType, long endBy, long njId) {
        AccompanyOrderStatus status = AccompanyOrderStatus.getByStatus(orderStatus);
        switch (status) {
            case INIT:
                return OrderStatusEnum.UNKNOWN;
            case WAITING:
                // 初始化 或 等待接单
                return OrderStatusEnum.WAITING;
            case SERVICING:
                // 服务中，需要判断是否有人点击结束服务
                if (endBy <= 0) {
                    return OrderStatusEnum.SERVICING;
                }
                return endBy == njId ? OrderStatusEnum.NJ_CLICK_CLOSE : OrderStatusEnum.USER_CLICK_CLOSE;
            case REVIEWING:
                // 审核中
                return OrderStatusEnum.REVIEWING;
            default:
                // 其他状态都是结束服务了
                if (endType == OrderEndType.NORMAL.getType() || endType == OrderEndType.FEEDBACK_ERROR.getType()
                        || endType == OrderEndType.REVIEW_ERROR.getType()) {
                    return OrderStatusEnum.FINISHED;
                } else if (endType == OrderEndType.USER_CANCEL.getType()) {
                    return OrderStatusEnum.CANCEL;
                } else if (endType == OrderEndType.TIME_OUT.getType()) {
                    return OrderStatusEnum.TIMEOUT_NO_RECEIVE;
                }
                return OrderStatusEnum.UNKNOWN;
        }
    }
}
