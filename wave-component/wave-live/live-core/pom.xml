<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wave</groupId>
        <artifactId>wave-live</artifactId>
        <version>1.5.26</version>
    </parent>

    <artifactId>live-core</artifactId>

    <dependencies>
        <!-- ====================        创作者内部依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>amusement-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-export-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-export-pojo-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>permission-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>user-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>lz-ocean-wave-api</artifactId>
        </dependency>

        <!-- ====================        基础架构的依赖        ==================== -->

        <!-- 大部分由wave-common引入, 不足部分在这里补全 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
        </dependency>


        <!-- ====================        第三方框架依赖        ==================== -->



        <!-- ====================        交易服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-trade-withdrawal-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-trade-query-center-api</artifactId>
        </dependency>


        <!-- ====================        风控服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-account-security-api</artifactId>
        </dependency>


        <!-- ====================        黑叶服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-vip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-social-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-content-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.security</groupId>
            <artifactId>lz-hy-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-data-common</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-hy-amusement-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live.cmpt.behavior.msg</groupId>
            <artifactId>lz-hy-cmpt-behavior-msg-protocol</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.hy.family</groupId>
            <artifactId>lz-hy-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-user-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-push-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lizhi.heiye</groupId>
            <artifactId>app-heiyeprotocol</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-hy-api</artifactId>
        </dependency>

        <!-- ====================        pp服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-vip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-pp-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-security-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-decorate-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-rcmd-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-app-upload-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.pp.family</groupId>
            <artifactId>lz-pp-family-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-push-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-social-api</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>fm.lizhi.pp</groupId>-->
<!--            <artifactId>lz-pp-activity-api</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-common</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.datamining</groupId>
            <artifactId>pp_rec_recommend_platform_api</artifactId>
        </dependency>

        <!-- ====================        西米服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-xm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-decorate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-vip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-common-flow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-push-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-social-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- ====================        创作中心 DC        ==================== -->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
        </dependency>


        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-cmpt-behavior-msg-protocol</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-oss-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>activity-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
