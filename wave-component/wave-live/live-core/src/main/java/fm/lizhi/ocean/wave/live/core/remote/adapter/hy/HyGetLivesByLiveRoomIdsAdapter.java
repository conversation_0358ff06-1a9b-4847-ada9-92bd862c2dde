package fm.lizhi.ocean.wave.live.core.remote.adapter.hy;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.api.LiveNewService;
import fm.lizhi.live.room.hy.protocol.LiveNewProto;
import fm.lizhi.ocean.wave.api.live.constants.hy.HyLiveContentType;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.hy.HyLiveExtraBean;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Description: 批量获取直播间
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Slf4j
@Component
public class HyGetLivesByLiveRoomIdsAdapter implements IRemoteMethodParamAndResultAdapter<List<Long>, LiveNewProto.GetLivesByLiveRoomIdsParams,
        Result<LiveNewProto.ResponseGetLivesByLiveRoomIds>, Result<List<LiveBean>>> {

    @Autowired
    LiveConfig liveConfig;

    @Autowired
    private CommonProviderConfig commonProviderConfig;


    @Override
    public LiveNewProto.GetLivesByLiveRoomIdsParams convertParam(List<Long> liveRoomIds) {
        return LiveNewProto.GetLivesByLiveRoomIdsParams.newBuilder()
                .addAllLiveRoomId(liveRoomIds)
                .build();
    }

    @Override
    public Result<List<LiveBean>> convertResult(Result<LiveNewProto.ResponseGetLivesByLiveRoomIds> result) {
        int rCode = result.rCode();

        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (rCode == LiveNewService.GET_LIVE_NO_CACHE_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.GET_LIVE_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.GET_LIVE_ERROR, null);
        }

        List<LiveNewProto.Live> liveList = result.target().getLiveList();
        List<LiveBean> liveBeanList = new ArrayList<>();

        String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.HEI_YE.getAppId()).getCdnHost();
        for (LiveNewProto.Live live : liveList) {
            HyLiveContentType hyLiveContentType = HyLiveContentType.from(live.getContentType());
            liveBeanList.add(LiveBean.builder()
                    .id(live.getId())
                    .userId(live.getUserId())
                    .liveRoomId(live.getLiveRoomId())
                    .name(live.getName())
                    .introduction(live.getIntroduction())
                    .imageUrl(UrlUtils.addCdnHost(cdnHost, live.getImageUrl()))
                    .startTime(new Date(live.getStartTime()))
                    .endTime(new Date(live.getEndTime()))
                    .actualStartTime(new Date(live.getActualStartTime()))
                    .actualEndTime(new Date(live.getActualEndTime()))
                    .createTime(new Date(live.getCreateTime()))
                    .status(live.getStatus())
                    .shareUrl(live.getShareUrl())
                    .password(live.getPassword())
                    .extraJson(JsonUtil.dumps(HyLiveExtraBean.builder()
                            .tags(live.getTags())
                            .contentType(hyLiveContentType.getValue())
                            .roomType(live.getRoomType())
                            .build()))
                    .welcome(live.getWelcome())
                    .build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, liveBeanList);

    }
}
