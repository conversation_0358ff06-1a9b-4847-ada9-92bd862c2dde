package fm.lizhi.ocean.wave.common.auto.route.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target(ElementType.TYPE)
@Retention(RUNTIME)
public @interface ScanOceanStandardAPI {
    /**
     * 指向的package
     * @return
     */
    String[] packages() default {};

    /**
     * 具体的类
     * @return
     */
    ScanOceanStandardAPI.RegisterProvider[] values() default {};

    @interface RegisterProvider{
        Class<?> interfaceClass();
        int timeout() default 5000;
    }
}
