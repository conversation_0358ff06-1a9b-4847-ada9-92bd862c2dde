package fm.lizhi.ocean.wave.gift.core.extension.effect;

import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.gift.core.model.dto.EffectProcessContext;

public interface EffectPushProcessor extends BusinessEnvAwareProcessor {

    /**
     * 获取推送的Topic
     *
     * @param context 上下文信息
     */
    String getPushTopic(EffectProcessContext<?> context);

    /**
     * 获取特效推送V2的Topic. 更合理的做法应该是context中声明是否全局特效, 并使用统一的判断来决定topic, 而非各业务返回topic.
     * 但因为GiftEffectMsgHandler和GiftEffectMsgProcessor链路较长, 改动影响面较大且本期时间较赶, 因此暂时还是沿用processor的方式.
     *
     * @param context 上下文信息
     * @return 特效推送V2的Topic
     */
    String getEffectPushV2Topic(EffectProcessContext<?> context);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return EffectPushProcessor.class;
    }
}
