package fm.lizhi.ocean.wave.common.manager;

import com.dianping.cat.Cat;
import com.dianping.cat.builder.Metric;
import com.dianping.cat.builder.MetricBuilder;
import fm.lizhi.ocean.wave.common.constant.RpcInvocationCatMetricName;
import fm.lizhi.ocean.wave.common.util.EnvUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * rpc调用打点监控
 *
 * <AUTHOR>
 * @date 2023/07/30
 */
@Slf4j
public class RpcInvocationMonitor {

    /**
     * RPC请求失败监控
     */
    public static void rpcReqFailMonitor(String appName, String serviceName, String methodName, String invokeType) {
        try {
            Metric metric = MetricBuilder.getInstance().addMetric(RpcInvocationCatMetricName.RPC_INVOCATION_FAIL.getName());
            metric.setDataPointCount(1);
            metric.addTag("appName", appName);
            metric.addTag("serviceName", serviceName);
            metric.addTag("methodName", methodName);
            metric.addTag("invokeType", invokeType);
            metric.addTag("localIp", EnvUtils.getLocalIp());
            Cat.logMetric(metric);
        } catch (Exception e) {
            log.warn("rpcReqFailMonitor fail:", e);
        }
    }

}
