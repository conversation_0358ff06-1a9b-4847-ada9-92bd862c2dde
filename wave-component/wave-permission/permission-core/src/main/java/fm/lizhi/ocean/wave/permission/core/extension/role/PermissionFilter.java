package fm.lizhi.ocean.wave.permission.core.extension.role;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import org.apache.commons.collections4.MapUtils;

/**
 * 权限过滤器接口
 * 请注意，实现类的 @Component("xxx"), 必须与数据库中的权限英文名称对应
 * <AUTHOR>
 */
public interface PermissionFilter {

    default boolean isSupport(BusinessEvnEnum app, RolePermissionEnum permissionType){
        return businessEnvSupport(app) && supportPermissionType(permissionType);
    }

    /**
     * 是否支持该业务执行
     */
    boolean businessEnvSupport(BusinessEvnEnum app);

    /**
     * 是否拥有更细粒度的权限。此方法可以扩展更多更细粒度的权限过滤逻辑。
     */
    boolean hasPermission(PermissionFilterDto filterDto);

    /**
     * 支持的权限类型
     * @param permissionType
     * @return
     */
    boolean supportPermissionType(RolePermissionEnum permissionType);

}
