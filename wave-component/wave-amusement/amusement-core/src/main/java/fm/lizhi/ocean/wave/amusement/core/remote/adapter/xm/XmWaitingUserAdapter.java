package fm.lizhi.ocean.wave.amusement.core.remote.adapter.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.amusement.core.remote.param.WaitingUsersParams;
import fm.lizhi.ocean.wave.amusement.core.remote.result.WaitingUsersResult;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IAmusementServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.amusement.pp.protocol.LiveAmusementProto;

/**
 * <AUTHOR>
 * @date 2023/9/11 18:17
 */
@Slf4j
@Component
public class XmWaitingUserAdapter implements IRemoteMethodParamAndResultAdapter<WaitingUsersParams
        , Long
        , Result<LiveAmusementProto.ResponseWaitingUsers>
        , Result<WaitingUsersResult>
        > {

    @Override
    public Long convertParam(WaitingUsersParams args) {
        return args.getLiveId();
    }

    @Override
    public Result<WaitingUsersResult> convertResult(Result<LiveAmusementProto.ResponseWaitingUsers> result) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, WaitingUsersResult.builder()
                .lastModifiedTime(result.target().getLastModifyTime())
                .waitingUserIds(result.target().getUserIdsList())
                .build());
    }

    @Override
    public Result<WaitingUsersResult> convertResult(Result<LiveAmusementProto.ResponseWaitingUsers> result, WaitingUsersParams args) {
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmWaitingUserAdapter,convertResult,rCode={},liveId={}", result.rCode(), args.getLiveId());
            return new Result<>(IAmusementServiceRemote.GET_WAITING_USERS_ERROR, null);
        }
        return convertResult(result);
    }
}
