package fm.lizhi.ocean.wave.user.export.api.model;

import lombok.Data;

/**
 * 用户认证记录
 * <AUTHOR>
 */
@Data
public class UserVerifyRecordMany {
    /**
     * 主键
     */
    private long id;
    /**
     * 用户id
     */
    private long userId;
    /**
     * 应用id
     */
    private long appId;
    /**
     * 子应用id
     */
    private long subAppId;
    /**
     * 业务id
     */
    private long bizId;
    /**
     * 身份证类型
     */
    private int idCardType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idCardNumber;
    /**
     * 身份证正面
     */
    private String idCardFront;
    /**
     * 身份证反面
     */
    private String idCardBack;
    /**
     * 手持身份证
     */
    private String idCardPerson;
    /**
     * 认证类型
     */
    private int verifyType;
    /**
     * 认证状态
     */
    private int verifyStatus;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 审核时间
     */
    private long reviewTime;
    /**
     * 审核备注
     */
    private String reviewNote;
    /**
     * 支付宝业务号
     */
    private String zfbBizNo;
    /**
     * 创建时间
     */
    private long createTime;
    /**
     * 修改时间
     */
    private long modifyTime;
    /**
     * 业务订单号
     */
    private String transactionId;
}
