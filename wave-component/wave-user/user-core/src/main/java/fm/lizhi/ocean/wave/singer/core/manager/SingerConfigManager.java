package fm.lizhi.ocean.wave.singer.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.singer.core.constant.SingerMsgCodes;
import fm.lizhi.ocean.wave.singer.core.convert.SingerConfigConvert;
import fm.lizhi.ocean.wave.singer.core.model.vo.GetSingerPreAuditConfigVO;
import fm.lizhi.ocean.wave.singer.core.model.vo.SingerEnumerateConfigVO;
import fm.lizhi.ocean.wave.singer.core.remote.result.GetSingerPreAuditConfigResponse;
import fm.lizhi.ocean.wave.singer.core.remote.result.SingerEnumerateConfigResponse;
import fm.lizhi.ocean.wave.singer.core.remote.service.SingerConfigServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SingerConfigManager {

    @Autowired
    private SingerConfigServiceRemote singerConfigServiceRemote;

    /**
     * 获取歌手预审配置
     *
     * @param appId 应用ID
     * @return 结果
     */
    public ResultVO<GetSingerPreAuditConfigVO> getSingerAuditConfig(int appId) {
        Result<GetSingerPreAuditConfigResponse> result = singerConfigServiceRemote.getSingerAuditConfig(appId);
        if (RpcResult.isFail(result)) {
            log.warn("getSingerAuditConfig fail, appId: {}, code: {}, message: {}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure(SingerMsgCodes.GET_SINGER_PRE_AUDIT_CONFIG_FAIL);
        }

        return ResultVO.success(SingerConfigConvert.I.auditConfig2Vo(result.target()));
    }

    /**
     * 获取歌手枚举配置
     *
     * @param appId 应用ID
     * @return 结果
     */
    public ResultVO<SingerEnumerateConfigVO> getEnumerateConfig(int appId) {
        Result<SingerEnumerateConfigResponse> result = singerConfigServiceRemote.getEnumerateConfig(appId);
        if (RpcResult.isFail(result)) {
            log.warn("getEnumerateConfig fail, appId: {}, code: {}, message: {}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure(SingerMsgCodes.GET_SINGER_PRE_AUDIT_CONFIG_FAIL);
        }
        SingerEnumerateConfigVO singerEnumerateConfigVO = SingerConfigConvert.I.response2VO(result.target());
        if (singerEnumerateConfigVO != null && singerEnumerateConfigVO.getSongStyle() != null) {
            //过滤掉ALL类型的曲风
            singerEnumerateConfigVO.getSongStyle().removeIf(songStyle -> songStyle.getName().equalsIgnoreCase("ALL"));

        }
        return ResultVO.success(singerEnumerateConfigVO);
    }


}
