package fm.lizhi.ocean.wave.user.core.api.impl;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.user.api.MedalService;
import fm.lizhi.ocean.wave.user.core.dao.redis.UserPropertyRedisDao;
import fm.lizhi.ocean.wave.user.core.model.vo.MedalVo;
import fm.lizhi.ocean.wave.user.core.model.vo.UserMedalVO;
import fm.lizhi.ocean.wave.user.core.remote.service.IMedalServiceRemote;
import fm.lizhi.ocean.wave.user.result.MedalGroupResult;
import fm.lizhi.ocean.wave.user.result.MedalResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 用户勋章服务
 */
@Component
public class MedalServiceImpl implements MedalService {

    @MyAutowired
    private IMedalServiceRemote medalServiceRemote;

    @Autowired
    private UserPropertyRedisDao userPropertyRedisDao;

    @Override
    public Result<List<MedalGroupResult>> medalUserUseList(Long userId) {
        Result<List<UserMedalVO>> result = medalServiceRemote.medalUserUseList(userId);
        if (result.rCode() != 0) {
            return new Result<>(MEDAL_USER_USE_LIST_FAIL, null);
        }

        List<UserMedalVO> medalVOS = result.target();
        List<MedalGroupResult> results = buildMedalGroupResults(medalVOS);
        return new Result<>(MEDAL_USER_USE_LIST_SUCCESS, results);
    }

    @Override
    public Result<List<MedalResult>> getMedalList(long userId) {
        Result<List<MedalVo>> result = medalServiceRemote.getMedalList(userId);
        if (result.rCode() != 0) {
            return new Result<>(GET_MEDAL_LIST_FAIL, null);
        }

        List<MedalVo> medalVos = result.target();
        List<MedalResult> list = medalVos.stream().map(medal ->
                        MedalResult.builder().cover(medal.getIcon()).aspect(medal.getAspect()).build())
                .collect(Collectors.toList());
        return new Result<>(GET_MEDAL_LIST_SUCCESS, list);
    }

    @Override
    public Result<List<MedalGroupResult>> medalUserUseListFromCache(Long userId) {
        // 从缓存中获取勋章信息
        List<UserMedalVO> medalGroupResults = userPropertyRedisDao.getUseUseMedalList(userId);
        if (medalGroupResults == null) {
            Result<List<UserMedalVO>> result = medalServiceRemote.medalUserUseList(userId);
            if (result.rCode() != 0) {
                // 失败了不存缓存
                return new Result<>(MEDAL_USER_USE_LIST_FAIL, null);
            }
            //数据存到缓存中
            medalGroupResults = result.target();
            userPropertyRedisDao.saveUseUseMedalList(userId, medalGroupResults);
        }

        List<MedalGroupResult> results = buildMedalGroupResults(medalGroupResults);
        return RpcResult.success(results);
    }

    @Override
    public Result<List<MedalResult>> getMedalListFromCache(long userId) {
        // 从缓存中获取勋章信息
        List<MedalVo> medalVOS = userPropertyRedisDao.getMedalList(userId);
        if (medalVOS == null) {
            Result<List<MedalVo>> result = medalServiceRemote.getMedalList(userId);
            if (result.rCode() != 0) {
                // 失败了不存缓存
                return new Result<>(GET_MEDAL_LIST_FAIL, null);
            }
            //数据存到缓存中
            medalVOS = result.target();
            userPropertyRedisDao.saveMedalList(userId, medalVOS);
        }

        List<MedalResult> list = medalVOS.stream().map(medal ->
                        MedalResult.builder().cover(medal.getIcon()).aspect(medal.getAspect()).build())
                .collect(Collectors.toList());
        return new Result<>(GET_MEDAL_LIST_SUCCESS, list);
    }

    private List<MedalGroupResult> buildMedalGroupResults(List<UserMedalVO> medalVOS) {
        List<MedalGroupResult> results = new ArrayList<>(medalVOS.size());
        for (UserMedalVO medalVO : medalVOS) {
            MedalGroupResult medalGroupResult = MedalGroupResult.builder()
                    .medalName(medalVO.getMedalName())
                    .medalDesc(medalVO.getMedalDesc())
                    .medalGroupId(medalVO.getMedalGroupId())
                    .medalImageUrl(medalVO.getMedalImageUrl())
                    .actionUrl(medalVO.getActionUrl())
                    .aspect(medalVO.getAspect())
                    .medalInfoId(medalVO.getMedalInfoId())
                    .longMedalImageUrl(medalVO.getLongMedalImageUrl())
                    .build();
            results.add(medalGroupResult);
        }
        return results;
    }
}
