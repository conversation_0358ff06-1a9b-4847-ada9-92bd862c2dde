package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.GenCommentParam;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SendTextCommentGetMsgTypeParam;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SendTextCommentPostBean;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SendTextCommentPreBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.CommentBean;
import fm.lizhi.ocean.wave.common.extension.IProcessor;

/**
 * 发送文本评论处理器
 *
 * <AUTHOR>
 */
public interface ISendTextCommentProcessor extends IProcessor<SendTextCommentPreBean, Void, SendTextCommentPostBean, Long> {

    @Override
    default Class<? extends IProcessor> getBaseBusinessProcessor() {
        return ISendTextCommentProcessor.class;
    }

    /**
     * 获取评论配置
     *
     * @return 平路配置
     */
    CommentCommonConfig getCommentConfig();


    CommentBean genCommentBean(GenCommentParam param);

    /**
     * 获取风控的msgType
     * @return
     */
    String getMsgType(SendTextCommentGetMsgTypeParam param);

    /**
     * 发送成功后置处理
     */
    default void sendSuccessPostProcess(){}

}
