package fm.lizhi.ocean.wave.live.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.model.result.LiveRoom;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.param.AddLiveRoomRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/10
 */
@Slf4j
@Component
public class GetMyRoomManager {

    @MyAutowired
    private ILiveRoomServiceRemote iLiveRoomServiceRemote;
    @Autowired
    private UserService userService;

    public ResultVO<LiveRoom> getMyRoom(long userId) {
        Result<GetSimpleUserResult> getUserResp = userService.getSimpleUserByCache(userId);

        if (getUserResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getMyRoom getSimpleUser error, userId:{}, rCode:{}", userId, getUserResp.rCode());
            return ResultVO.failure("获取用户信息失败，请稍后再试");
        }

        SimpleUser user = getUserResp.target().getSimpleUser();

        Result<GetLiveRoomResult> getLiveRoomResp = iLiveRoomServiceRemote.getLiveRoomByUserId(userId);

        // 直播间存在，直接返回
        if (getLiveRoomResp.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            LiveRoomBean liveRoomBean = getLiveRoomResp.target().getLiveRoomBean();
            return ResultVO.success(LiveRoom.builder().liveRoomId(liveRoomBean.getId()).build());
        }

        // 直播间不存在，先添加再返回
        if (getLiveRoomResp.rCode() == ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_NOT_FOUND) {
            // 直播间不存在，创建直播间
            Result<Void> addLiveRoomResp = iLiveRoomServiceRemote.addLiveRoom(AddLiveRoomRemoteParam.builder()
                    .userId(userId)
                    .userName(user.getNickName())
                    .radioId(user.getRadioId())
                    .build());

            if (addLiveRoomResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("getMyRoom addLiveRoom error, userId:{}, rCode:{}", userId, addLiveRoomResp.rCode());
                return ResultVO.failure("获取直播间信息失败，请稍后再试");
            }

            Result<GetLiveRoomResult> getLiveRoomResp2 = iLiveRoomServiceRemote.getLiveRoomByUserId(userId);
            if (getLiveRoomResp2.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("getMyRoom getLiveRoomByUserId error, userId:{}, rCode:{}", userId, getLiveRoomResp2.rCode());
                return ResultVO.failure("获取直播间信息失败，请稍后再试");
            }

            LiveRoomBean liveRoomBean = getLiveRoomResp2.target().getLiveRoomBean();
            return ResultVO.success(LiveRoom.builder().liveRoomId(liveRoomBean.getId()).build());
        }

        return ResultVO.failure("获取直播间信息失败，请稍后再试");
    }
}
