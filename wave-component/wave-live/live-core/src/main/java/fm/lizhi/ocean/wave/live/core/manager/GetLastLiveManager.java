package fm.lizhi.ocean.wave.live.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.constant.LiveStatus;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.live.core.extension.getlastlivebyuser.IGetLastLiveByUserProcessor;
import fm.lizhi.ocean.wave.live.core.extension.latestbyliveid.ILatestByLiveIdProcessor;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.constants.LiveMsgCodes;
import fm.lizhi.ocean.wave.live.core.facade.LiveAmusementFacade;
import fm.lizhi.ocean.wave.live.core.model.result.GetLastLiveResult;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLastEditLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLatestLiveIdRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveModeRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveModeRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 获取最新一次直播信息
 */
@Component
@Slf4j
public class GetLastLiveManager {

    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;
    @MyAutowired
    private ILiveRoomServiceRemote liveRoomServiceRemote;

    @Autowired
    private LiveAmusementFacade liveAmusementFacade;

    @Autowired
    private ProcessorV2Factory factory;

    public ResultVO<GetLastLiveResult> getLastLiveByUser(long userId) {

        GetLastLiveResult.GetLastLiveResultBuilder builder = GetLastLiveResult.builder();
        Result<String> getAppKeyResp = liveServiceRemote.getNormalVoiceAppKey();
        if (getAppKeyResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure("获取语音appKey失败");
        }
        builder.defaultVoiceAppKey(getAppKeyResp.target());
        builder.njId(userId);


        Integer appId = ContextUtils.getContext().getHeader().getAppId();

        Result<GetLiveRoomResult> roomResult = liveRoomServiceRemote.getLiveRoomByUserId(userId);
        if (roomResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (roomResult.rCode() == ILiveRoomServiceRemote.GET_LIVE_ROOM_BY_USER_ID_NOT_FOUND) {
                return ResultVO.success(builder.build());
            }
            return ResultVO.failure("查询房间信息失败");
        }

        LiveRoomBean liveRoomBean = roomResult.target().getLiveRoomBean();
        GetLastLiveResult.LastLiveRoomResult room = GetLastLiveResult.LastLiveRoomResult.builder()
                .liveRoomId(liveRoomBean.getId())
                .status(liveRoomBean.getStatus().getValue())
                .defaultWelcomeMsg(roomResult.target().getDefaultWelcomeMsg())
                .defaultAnnouncement(roomResult.target().getDefaultAnnouncement())
                .build();
        builder.liveRoom(room);

        GetLatestLiveIdRemoteParam latestLiveIdRemoteParam = GetLatestLiveIdRemoteParam.builder()
                .userId(userId)
                .appId(appId)
                .liveRoomId(room.getLiveRoomId())
                .build();
        Result<Long> liveIdRes = liveServiceRemote.getLastLiveIdByParam(latestLiveIdRemoteParam);
        if (liveIdRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (liveIdRes.rCode() == ILiveServiceRemote.GET_LAST_LIVE_ID_NOT_FOUND) {
                return ResultVO.success(builder.build());
            }
            return ResultVO.failure("查询直播信息失败");
        }


        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLive(GetLiveRemoteParam.builder().liveId(liveIdRes.target()).build());
        if (!Objects.equals(liveResult.rCode(), (int) GeneralRCode.GENERAL_RCODE_SUCCESS)) {
            return ResultVO.failure("查询直播信息失败");
        }

        LiveBean liveBean = liveResult.target().getLiveBean();
        Result<GetLiveModeRemoteResult> liveModeResp = liveServiceRemote.getLiveMode(GetLiveModeRemoteParam.builder()
                .liveId(liveBean.getId())
                .build());

        if (liveModeResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure("获取直播模式失败");
        }

        // 使用IGetLastLiveByUserProcessor检查用户是否可以进入直播间
        IGetLastLiveByUserProcessor getLastLiveByUserProcessor = factory.getProcessor(IGetLastLiveByUserProcessor.class);
        ResultVO<Void> checkCanEnterLiveResult = getLastLiveByUserProcessor.checkCanEnterLive(userId, liveIdRes.target());
        if (!checkCanEnterLiveResult.isOK()) {
            return ResultVO.failure(checkCanEnterLiveResult.getRCode(), checkCanEnterLiveResult.getPrompt().getMsg());
        }

        Result<Long> liveIdIfInRoom = liveServiceRemote.getLiveIdIfInRoom(ContextUtils.getContext().getUserId());
        if (liveIdIfInRoom.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            builder.userCurrentJoinLiveId(liveIdIfInRoom.target());
        }


        GetLastLiveResult.LastLiveResult live = GetLastLiveResult.LastLiveResult.builder()
                .liveId(liveBean.getId())
                .liveName(liveBean.getName())
                .announcement(liveBean.getIntroduction())
                .cover(liveBean.getImageUrl())
                .status(liveBean.getStatus())
                .isLock(StringUtils.isNotBlank(liveBean.getPassword()))
                .welcomeMsg(liveBean.getWelcome())
                .build();
        builder.live(live);
        builder.liveBean(liveBean);
        builder.liveMode(liveModeResp.target().getLiveModeEnum().getLiveMode());
        builder.roomType(liveModeResp.target().getLiveModeEnum().getRoomType());

        return ResultVO.success(builder.build());
    }

    /**
     * 根据直播ID获取最新一次直播信息
     *
     * @param liveId 历史直播ID
     * @return 结果
     */
    public ResultVO<GetLastLiveResult> getLastLiveByLiveId(long liveId) {
        if (liveId <= 0) {
            return ResultVO.failure(LiveMsgCodes.GET_LATEST_LIVE_BY_LIVE_ID_PARAM_ERROR.getCode(), LiveMsgCodes.GET_LATEST_LIVE_BY_LIVE_ID_PARAM_ERROR.getMsg());
        }
        Result<GetLiveRemoteResult> liveRes = liveServiceRemote.getLiveByCache(GetLiveRemoteParam.builder().liveId(liveId).build());
        if (liveRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(LiveMsgCodes.GET_LATEST_LIVE_BY_LIVE_ID_FAIL.getCode(), LiveMsgCodes.GET_LATEST_LIVE_BY_LIVE_ID_FAIL.getMsg());
        }

        // 检查用户是否可以进房
        ILatestByLiveIdProcessor processor = factory.getProcessor(ILatestByLiveIdProcessor.class);
        ResultVO<Void> checkCanEnterLiveResult = processor.checkCanEnterLive(liveId);
        if (!checkCanEnterLiveResult.isOK()) {
            return ResultVO.failure(checkCanEnterLiveResult.getRCode(), checkCanEnterLiveResult.getPrompt().getMsg());
        }

        //根据用户ID查询信息
        return getLastLiveByUser(liveRes.target().getLiveBean().getUserId());
    }

    /**
     * 获取最近在开播的直播节目ID
     *
     * @param userId 用户ID
     * @return 结果
     */
    public ResultVO<Long> getLatestOnAirLiveId(long userId) {
        Result<GetLiveRoomResult> roomResult = liveRoomServiceRemote.getLiveRoomByUserId(userId);
        if (roomResult.rCode() != 0) {
            return ResultVO.failure(LiveMsgCodes.JUMP_LIVE_GET_LIVE_FAIL.getCode(), LiveMsgCodes.JUMP_LIVE_GET_LIVE_FAIL.getMsg());
        }

        Long roomId = roomResult.target().getLiveRoomBean().getId();
        GetLastEditLiveRemoteParam param = GetLastEditLiveRemoteParam.builder().appId(ContextUtils.getContext().getHeader().getAppId()).liveRoomId(roomId).build();
        Result<GetLiveRemoteResult> lastEditLiveRes = liveServiceRemote.getLastEditLive(param);
        if (lastEditLiveRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(LiveMsgCodes.JUMP_LIVE_GET_LIVE_FAIL.getCode(), LiveMsgCodes.JUMP_LIVE_GET_LIVE_FAIL.getMsg());
        }

        if (LiveStatus.ON_AIR.getValue() != lastEditLiveRes.target().getLiveBean().getStatus()) {
            log.warn("LiveRoomManager getLatestOnAirLiveId is null userId={}", userId);
            return ResultVO.failure(LiveMsgCodes.JUMP_LIVE_NO_EXIST_LAST_OPEN_LIVE.getCode(), LiveMsgCodes.JUMP_LIVE_NO_EXIST_LAST_OPEN_LIVE.getMsg());
        }
        return ResultVO.success(lastEditLiveRes.target().getLiveBean().getId());
    }

}
