package fm.lizhi.ocean.wave.user.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量查询用户行为数据请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchUserBehaviorsParam {
    /**
     * 用户ID列表
     */
    private List<Long> userId;

    /**
     * 行为类型
     */
    private List<String> types;
}
