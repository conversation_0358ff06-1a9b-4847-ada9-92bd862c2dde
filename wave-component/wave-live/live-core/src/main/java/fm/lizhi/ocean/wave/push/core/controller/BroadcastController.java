package fm.lizhi.ocean.wave.push.core.controller;

import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.push.core.manager.BroadcastManager;
import fm.lizhi.ocean.wave.push.core.model.param.GetBroadcastListParam;
import fm.lizhi.ocean.wave.push.core.model.result.GetBroadcastListResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 飘屏功能
 */
@RestController
@RequestMapping("/broadcast")
public class BroadcastController {

    @Autowired
    private BroadcastManager broadcastManager;

    /**
     * 根据直播id获取飘屏列表
     *
     * @param param 参数信息
     */
    @GetMapping("/list")
    @VerifyUserToken
    public ResultVO<GetBroadcastListResult> getBroadcastList(@Validated GetBroadcastListParam param) {
        //调用获取飘屏列表
        return broadcastManager.getBroadcastList(param);
    }

}
