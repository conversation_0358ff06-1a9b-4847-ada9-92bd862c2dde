package fm.lizhi.ocean.wave.comment.core.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.LiveRoomMsgConstants;
import fm.lizhi.ocean.wave.comment.core.manager.CommentPushManager;
import fm.lizhi.ocean.wave.comment.core.manager.CommentPushTaskManger;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/6
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${hy.kafka.consumer.enable}")
public class HyEnterNoticeKafkaConsumer {

    @Autowired
    private CommentPushTaskManger commentPushTaskManger;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private CommentPushManager commentPushManager;

    /**
     * hy进房公告推送
     *
     * @param body 消息结构体
     */
    @KafkaHandler(topic = "lz_topic_hy_enter_notice",
            group = "lz_ocean_wave_enter_notice_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleCommentMsg(String body) {
        String msg = null;
        JSONObject object = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            object = JSONObject.parseObject(msg);
        } catch (Exception e) {
            log.error("hy.commentMsgVo json parse error, msg:{}, orgMsg:{}", msg, body, e);
            return;
        }

        long liveId = object.getLongValue("liveId");
        long enterTime = object.getLongValue("sendEnterTime");
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);

        //是否打开评论罗马推送
        if (commentConfig.isCommentRomaPushSwitch()) {
            //推送进房公告
            int code = commentPushManager.pushCommentMsgChange(liveId, BusinessEvnEnum.HEI_YE.getAppId(), LiveRoomMsgConstants.ENTER_NOTICE, enterTime);
            if (code >= GeneralRCode.GENERAL_RCODE_SERVER_BUSY) {
                throw new RuntimeException("pushCommentMsgChange enterNotice fail, ,msg-{}" + msg);
            }
        }

    }

}
