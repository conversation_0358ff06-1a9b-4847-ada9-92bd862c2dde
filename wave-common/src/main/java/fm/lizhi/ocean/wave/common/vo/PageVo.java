package fm.lizhi.ocean.wave.common.vo;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @date 2024/4/10 11:41
 */
@Data
public class PageVo<T> {

    private int total = 0;

    private List<T> list;


    public PageVo() {
    }

    public PageVo(int total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public static <T> PageVo<T> of(int total, List<T> list) {
        return new PageVo<>(total, list);
    }

    public static <T> PageVo<T> empty() {
        return new PageVo<>(0, Collections.emptyList());
    }

}
