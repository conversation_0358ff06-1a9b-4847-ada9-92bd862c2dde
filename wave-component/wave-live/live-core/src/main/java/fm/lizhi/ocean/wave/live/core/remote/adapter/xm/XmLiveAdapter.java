package fm.lizhi.ocean.wave.live.core.remote.adapter.xm;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.remote.bean.PerformanceDataBean;
import fm.lizhi.ocean.wave.live.core.remote.param.HomeRecRequest;
import fm.lizhi.ocean.wave.live.core.remote.result.HomeRecResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.data.protocol.LiveStateProto;

import java.util.List;

@Component
public class XmLiveAdapter {

    @Autowired
    private GuidGenerator guidGenerator;

    @Autowired
    private LiveConfig liveConfig;

    /**
     * 转换首页推荐直播间请求参数
     *
     * @param request 请求参数
     * @return 结果实体
     */
    public LiveStateProto.RankLiveIdParams convertHomeRecRequest(HomeRecRequest request) {
        LiveStateProto.RankLiveIdParams.Builder params = LiveStateProto.RankLiveIdParams.newBuilder();
        String performanceId = request.getPerformanceId();
        PerformanceDataBean performanceData = StringUtils.isBlank(performanceId)
                ? new PerformanceDataBean() : JSONObject.parseObject(performanceId, PerformanceDataBean.class);
        long timestamp = performanceData.getTimestamp();
        // 上次下拉刷新时间
        long refreshTime = performanceData.getRefreshTime();
        if (refreshTime <= 0) {
            refreshTime = System.currentTimeMillis();
            request.setRefreshTime(refreshTime);
        }
        // 请求唯一ID，非登录时随机生成
        long uniqueId = performanceData.getUniqueId();
        if (uniqueId <= 0) {
            uniqueId = request.getUserId() > 0 ? request.getUserId() : guidGenerator.genId();
            request.setUniqueId(uniqueId);
        }

        JSONObject pfmMap = new JSONObject();
        params.setCategory(liveConfig.getXm().getHotTabCategoryId());
        params.setNum(-1);
        if (request.getTimestamp() > 0) {
            pfmMap.put("timestamp", timestamp);
        }
        if (request.getRefreshTime() > 0) {
            pfmMap.put("refreshTime", refreshTime);
        }
        if (request.getUniqueId() > 0) {
            pfmMap.put("uniqueId", uniqueId);
        }
        if (!pfmMap.isEmpty()) {
            params.setPerformanceId(pfmMap.toJSONString());
        }
        return params.build();
    }

    /**
     * 转换首页推荐直播间返回结果
     *
     * @param responseGetRank 推荐平台返回结果
     * @param request         参数
     * @return 结果
     */
    public HomeRecResponse convertHomeRecResponse(LiveStateProto.ResponseGetRankLiveIds responseGetRank,
                                                  HomeRecRequest request) {
        List<Long> liveIdsList = responseGetRank.getLiveIdsList();
        long timestamp = JSONObject.parseObject(responseGetRank.getPerformanceId()).getLong("timestamp");
        int lastDataOffset = 0;
        if (StringUtils.isNotBlank(request.getPerformanceId())) {
            PerformanceDataBean performanceData = JSONObject.parseObject(request.getPerformanceId(), PerformanceDataBean.class);
            lastDataOffset = performanceData.isLastDisasterReq() ? 0 : performanceData.getLastDataOffset();
        }
        int offset = lastDataOffset;
        boolean isLastPage = false;
        if (liveIdsList.isEmpty() || offset >= liveIdsList.size()) {
            isLastPage = true;
            return HomeRecResponse.builder()
                    .liveIdList(CollectionUtil.newArrayList())
                    .lastPage(isLastPage)
                    .build();
        }
        int max = offset + liveConfig.getHomeLivePageSize();
        if (offset + liveConfig.getHomeLivePageSize() > liveIdsList.size()) {
            max = liveIdsList.size();
            isLastPage = true;
        }

        //相当于客户端分页了，dc接口返回的是所有的付费排名数据，无分页
        List<Long> resultLiveIds = liveIdsList.subList(offset, max);
        PerformanceDataBean performanceDataBean = new PerformanceDataBean()
                .setLastDataOffset(offset + resultLiveIds.size())
                .setUniqueId(request.getUniqueId())
                .setLastDisasterReq(false)
                .setRefreshTime(request.getRefreshTime())
                .setTimestamp(timestamp);
        return HomeRecResponse.builder()
                .liveIdList(resultLiveIds)
                .lastPage(isLastPage)
                .performanceData(performanceDataBean)
                .build();
    }
}
