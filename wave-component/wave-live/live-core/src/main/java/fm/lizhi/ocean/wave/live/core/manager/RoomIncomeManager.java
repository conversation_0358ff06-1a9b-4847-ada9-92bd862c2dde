package fm.lizhi.ocean.wave.live.core.manager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.constant.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.util.*;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.constants.LiveMsgCodes;
import fm.lizhi.ocean.wave.live.core.convert.LiveIncomeConvert;
import fm.lizhi.ocean.wave.live.core.dao.redis.PayIncomeRedisDao;
import fm.lizhi.ocean.wave.live.core.extension.income.IRoomIncomeProcessor;
import fm.lizhi.ocean.wave.live.core.facade.AccountQueryServiceFacade;
import fm.lizhi.ocean.wave.live.core.model.bean.IncomeBean;
import fm.lizhi.ocean.wave.live.core.model.dto.RoomIncomeConfigDTO;
import fm.lizhi.ocean.wave.live.core.model.vo.IncomeRecordStatVo;
import fm.lizhi.ocean.wave.live.core.model.vo.RoomIncomeItemVO;
import fm.lizhi.ocean.wave.live.core.model.vo.RoomIncomeVO;
import fm.lizhi.ocean.wave.live.core.remote.result.IncomeRecordStatResponse;
import fm.lizhi.ocean.wave.live.core.remote.service.IIncomeServiceRemote;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.trade.query.center.account.protocol.QueryAccountProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

/**
 * 厅收入
 */
@Slf4j
@Component
public class RoomIncomeManager {

    @Autowired
    private AccountQueryServiceFacade accountQueryServiceFacade;

    @Autowired
    private PayIncomeRedisDao payIncomeRedisDao;

    @Autowired
    private ProcessorV2Factory factory;

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private FamilyService familyService;

    @MyAutowired
    private IIncomeServiceRemote incomeServiceRemote;


    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("payIncomeManager", 6, 8, new ArrayBlockingQueue<>(100));

    /**
     * 获取厅收入
     *
     * @param njId 厅主ID
     * @return 房间收入
     */
    public ResultVO<RoomIncomeVO> getRoomIncome(long njId) {
        if (njId <= 0) {
            return ResultVO.failure(LiveMsgCodes.GET_INCOME_FAIL.getCode(), LiveMsgCodes.GET_INCOME_FAIL.getMsg());
        }

        //判断是否是PGC厅，目前只有PGC厅才展示
        Result<Boolean> result = familyService.checkUserInPGC(njId);
        if (RpcResult.isFail(result) || !result.target()) {
            return ResultVO.success(RoomIncomeVO.of(Collections.emptyList(), "", false));
        }

        List<RoomIncomeItemVO> roomIncomeItemVOS = getRoomIncomeList(njId);
        List<List<RoomIncomeItemVO>> partition = Lists.partition(roomIncomeItemVOS, liveConfig.getRoomIncomeMaxTabSize());
        return ResultVO.success(RoomIncomeVO.of(partition, liveConfig.getRoomIncomeRemark(), true));
    }

    /**
     * 获取房间收入列表
     *
     * @param njId 厅主ID
     * @return 房间收入列表
     */
    public List<RoomIncomeItemVO> getRoomIncomeList(long njId) {
        IRoomIncomeProcessor processor = factory.getProcessor(IRoomIncomeProcessor.class);
        CommonLiveConfig commonLiveConfig = processor.getLiveConfig();
        String configJson = commonLiveConfig.getRoomIncomeConfig();
        if (StringUtils.isBlank(configJson)) {
            return Collections.emptyList();
        }

        List<RoomIncomeConfigDTO> roomIncomeConfigs = JsonUtils.readValue(configJson, new TypeReference<List<RoomIncomeConfigDTO>>() {
        });

        Map<String, IncomeBean> todayRoomIncomeMap = getTodayRoomIncome(njId, roomIncomeConfigs);
        Map<String, IncomeBean> yesterdayRoomIncomeMap = getYesterdayRoomIncome(njId, roomIncomeConfigs);

        //合并数据
        return mergeIncomeMap(todayRoomIncomeMap, yesterdayRoomIncomeMap);
    }

    /**
     * 查询今日房间收入
     *
     * @param njId              厅主ID
     * @param roomIncomeConfigs 配置码列表
     * @return 收入
     */
    public Map<String, IncomeBean> getTodayRoomIncome(long njId, List<RoomIncomeConfigDTO> roomIncomeConfigs) {
        BusinessEvnEnum businessEvn = ContextUtils.getBusinessEvnEnum();
        //之间是左闭右闭的
        Date startTime = DateUtil.getDayStart(new Date());
        Date endTime = DateUtil.getDayEnd(startTime);

        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, 30, roomIncomeConfigs.size());
        // 线程安全的map
        Map<String, IncomeBean> incomeMap = new ConcurrentHashMap<String, IncomeBean>();
        for (RoomIncomeConfigDTO configDTO : roomIncomeConfigs) {
            PaySettleConfigCodeEnum configCodeEnum = PaySettleConfigCodeEnum.from(configDTO.getSettleConfigCode());
            //如果找不到，说明配置有问题，直接返回
            if (configCodeEnum == null) {
                continue;
            }

            incomeMap.put(configDTO.getName(), IncomeBean.of(0L, configDTO.getName(), configDTO.getIndex(), configDTO.getWeightType()));
            countDownLatchWrapper.submit(() -> {
                try {
                    Optional<QueryAccountProto.ResponseQueryAssestSumInRange> result = accountQueryServiceFacade.queryRoomIncome(businessEvn,
                            njId, startTime.getTime(), endTime.getTime(), configCodeEnum);
                    Long income = result.map(QueryAccountProto.ResponseQueryAssestSumInRange::getSum).orElse(0L);
                    incomeMap.get(configDTO.getName()).setIncome(income);
                } catch (Exception e) {
                    log.error("getTodayRoomIncome error: roomId=" + njId, e);
                }
            });
        }

        countDownLatchWrapper.await();
        return incomeMap;
    }

    /**
     * 查询昨天的数据
     *
     * @param njId 厅主ID
     * @return 昨天的收入数据
     */
    public Map<String, IncomeBean> getYesterdayRoomIncome(long njId, List<RoomIncomeConfigDTO> configCodeList) {
        BusinessEvnEnum businessEvn = ContextUtils.getBusinessEvnEnum();
        Date yesterdayDate = DateUtil.getDayBefore(new Date(), 1);
        //之间是左闭右闭的
        Date startTime = DateUtil.getDayStart(yesterdayDate);
        Date endTime = DateUtil.getDayEnd(yesterdayDate);
        //拿到昨天的时间字符串，yyyyMMdd
        String dateStr = DateUtil.formatDateToString(startTime, DateUtil.date);

        // 线程安全的map
        Map<String, IncomeBean> incomeMap = new ConcurrentHashMap<String, IncomeBean>();
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, 30, configCodeList.size());
        for (RoomIncomeConfigDTO configDTO : configCodeList) {
            PaySettleConfigCodeEnum configCodeEnum = PaySettleConfigCodeEnum.from(configDTO.getSettleConfigCode());
            //如果找不到，说明配置有问题，直接返回
            if (configCodeEnum == null) {
                continue;
            }

            incomeMap.put(configDTO.getName(), IncomeBean.of(0L, configDTO.getName(), configDTO.getIndex(), configDTO.getWeightType()));
            countDownLatchWrapper.submit(() -> {
                try {
                    //先去缓存查一遍昨天的数据
                    Long roomIncome = payIncomeRedisDao.getRoomIncome(njId, dateStr, configCodeEnum.getConfigCode());
                    //如果缓存有，直接返回
                    if (roomIncome != null) {
                        incomeMap.get(configDTO.getName()).setIncome(roomIncome);
                        return;
                    }
                    //缓存没有，去查询支付接口的数据
                    Optional<QueryAccountProto.ResponseQueryAssestSumInRange> result = accountQueryServiceFacade.queryRoomIncome(businessEvn,
                            njId, startTime.getTime(), endTime.getTime(), configCodeEnum);
                    roomIncome = result.map(QueryAccountProto.ResponseQueryAssestSumInRange::getSum).orElse(0L);
                    incomeMap.get(configDTO.getName()).setIncome(roomIncome);
                    //保存到缓存
                    payIncomeRedisDao.saveRoomIncome(njId, dateStr, roomIncome, configCodeEnum.getConfigCode());
                } catch (Exception e) {
                    log.error("getTodayRoomIncome error: roomId=" + njId, e);
                }
            });
        }
        countDownLatchWrapper.await();
        return incomeMap;
    }

    /**
     * 合并今日昨日数据
     *
     * @param todayRoomIncomeMap     今日数据
     * @param yesterdayRoomIncomeMap 昨日数据
     * @return 合并后的数据
     */
    private List<RoomIncomeItemVO> mergeIncomeMap(Map<String, IncomeBean> todayRoomIncomeMap, Map<String, IncomeBean> yesterdayRoomIncomeMap) {
        //合并今天和昨天的数据
        List<IncomeBean> incomeList = todayRoomIncomeMap.size() > yesterdayRoomIncomeMap.size() ?
                new ArrayList<>(todayRoomIncomeMap.values()) : new ArrayList<>(yesterdayRoomIncomeMap.values());
        //按index排序
        incomeList.sort(Comparator.comparingInt(IncomeBean::getIndex));

        List<RoomIncomeItemVO> resList = new ArrayList<>(incomeList.size());
        for (IncomeBean incomeBean : incomeList) {
            IncomeBean yesterdayIncome = yesterdayRoomIncomeMap.get(incomeBean.getName());
            IncomeBean todayIncome = todayRoomIncomeMap.get(incomeBean.getName());
            RoomIncomeItemVO roomIncomeItemVO = new RoomIncomeItemVO()
                    .setTodayIncome(todayIncome.getIncome())
                    .setYesterdayIncome(yesterdayIncome.getIncome())
                    .setWeightType(incomeBean.getWeightType())
                    .setName(incomeBean.getName());
            resList.add(roomIncomeItemVO);
        }
        return resList;
    }

    public List<IncomeRecordStatVo> getIncomeRecordByFamilyAndNj(long familyId, Long startTime, Long endTime) {
        List<IncomeRecordStatResponse> list = incomeServiceRemote.getIncomeRecordStat(familyId, startTime, endTime);
        return LiveIncomeConvert.INSTANCE.toVo(list);
    }
}
