package fm.lizhi.ocean.wave.user.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;
import fm.lizhi.ocean.wave.user.core.remote.result.GrowRelationLevelResponse;
import fm.lizhi.ocean.wave.user.core.remote.result.ShowMealRelationResponse;

import java.util.List;

/**
 * 用户成长关系服务
 */
public interface IGrowRelationServiceRemote extends IBaseRemoteServiceInvoker {

    /**
     * 获取成长关系等级配置列表
     *
     * @return 结果
     */
    Result<List<GrowRelationLevelResponse>> getLevelConfigList();

    /**
     * 获取用户成长关系信息
     *
     * @param levelId 等级ID
     * @return 结果
     */
    Result<GrowRelationLevelResponse> getUserGrowRelationByCache(long levelId);

    /**
     * 获取用户成长关系信息
     *
     * @param levelId 等级ID
     * @return 结果
     */
    Result<GrowRelationLevelResponse> getUserGrowRelation(long levelId);

    /**
     * 获取用户成长关系信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    Result<List<ShowMealRelationResponse>> getShowMealRelation(long userId);

    /**
     * 成功
     */
    int GET_LEVEL_CONFIG_LIST_SUCCESS = 0;

    /**
     * 失败
     */
    int GET_LEVEL_CONFIG_LIST_FAIL = 1;


    /**
     * 失败
     */
    int GET_USER_GROW_RELATION_FAIL = 1;

    /**
     * 数据不存在
     */
    int GET_USER_GROW_RELATION_EMPTY = 2;

    /**
     * 成功
     */
    int GET_SHOW_MEAL_RELATION_SUCCESS = 0;

    /**
     * 失败
     */
    int GET_SHOW_MEAL_RELATION_FAIL = 1;

}
