package fm.lizhi.ocean.wave.activity.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.activity.core.remote.bean.*;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

/**
 * <AUTHOR>
 */
public interface IMaskServiceRemote extends IBaseRemoteServiceInvoker {

    /**
     * 获取pk信息
     */
    Result<GetMaskByLiveIdResponse> getMaskByLiveId(long liveId);

    int PK_INFO_FAIL = 1; // 获取失败
}
