package fm.lizhi.ocean.wave.comment.core.filtersetting;

import fm.lizhi.ocean.wave.comment.core.filtersetting.entity.WaveCommentFilterConfig;
import fm.lizhi.ocean.wave.comment.core.filtersetting.mapper.WaveCommentFilterConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/18
 */
@Component
public class WaveCommentFilterConfigDao {

    @Autowired
    private WaveCommentFilterConfigMapper waveCommentFilterConfigMapper;


    public WaveCommentFilterConfig getSetting(long settingId) {
        WaveCommentFilterConfig query = new WaveCommentFilterConfig();
        query.setId(settingId);
        return waveCommentFilterConfigMapper.selectByPrimaryKey(query);
    }

    public List<WaveCommentFilterConfig> getSettingList(int appId) {
        WaveCommentFilterConfig query = new WaveCommentFilterConfig();
        query.setAppId(appId);
        query.setStatus(1);
        return waveCommentFilterConfigMapper.selectMany(query);
    }

}
