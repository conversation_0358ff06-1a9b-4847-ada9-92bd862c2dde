package api.activity.bean.mask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: chenzj
 * @date: 2024-10-22 16:47
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaskMicInfo {
    /**
     * 麦位序号 1开始
     */
    private int micIndex;
    /**
     * 蒙面用户ID
     */
    private long micUserId;
    /**
     * 蒙面ICON
     */
    private String micIcon;
    /**
     * 蒙面昵称
     */
    private String micName;
}
