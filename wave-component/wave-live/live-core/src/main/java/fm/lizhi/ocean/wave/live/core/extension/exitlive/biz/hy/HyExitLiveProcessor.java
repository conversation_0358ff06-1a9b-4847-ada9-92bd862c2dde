package fm.lizhi.ocean.wave.live.core.extension.exitlive.biz.hy;

import com.alibaba.fastjson.JSON;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorMsg;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.*;
import fm.lizhi.live.data.bean.UserBehaviorType;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.ExitLiveProcessor;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.bean.ExitLivePostBean;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.bean.ExitLivePreBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.HyBehaviorKafkaProducer;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyDeviceBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyUserBehaviorBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 黑叶退房差异化处理器
 * @author: guoyibin
 * @create: 2023/05/26 11:43
 */
@Slf4j
@Component
public class HyExitLiveProcessor implements ExitLiveProcessor {

    @Autowired
    private HyBehaviorKafkaProducer hyBehaviorKafkaProducer;

    @Override
    public void exitLivePre(ExitLivePreBean exitLivePreBean) {
        //PP 会做lz_pp_online_state_user_statelz_pp_online_state_user_state的消息发送，黑叶不需要
    }

    @Override
    public void exitLivePost(ExitLivePostBean exitLivePostBean) {
        //发送设备行为消息
        sendDeviceBehaviorMsg(exitLivePostBean);
        //发送主播行为消息
        sendAnchorBehaviroMsg(exitLivePostBean);
        //发送用户行为消息
        sendUserBehaviorMsg(exitLivePostBean);
    }

    private void sendUserBehaviorMsg(ExitLivePostBean exitLivePostBean) {
        hyBehaviorKafkaProducer.sendUserBehavior(HyUserBehaviorBean.builder().userId(exitLivePostBean.getUserId())
                .liveId(exitLivePostBean.getLiveBean().getId()).ip(exitLivePostBean.getIp()).clientVersion(exitLivePostBean.getClientVersion())
                    .njId(exitLivePostBean.getLiveRoomBean().getUserId()).appId(exitLivePostBean.getAppId()).deviceId(exitLivePostBean.getDeviceId())
                        .userBehaviorType(UserBehaviorType.LIVE_LEAVE.getCode()).build());
    }

    private void sendAnchorBehaviroMsg(ExitLivePostBean exitLivePostBean) {
        //仅房主才发送主播行为消息
        hyBehaviorKafkaProducer.sendAnchorBehavior(HyAnchorBehaviorBean.builder().liveBean(exitLivePostBean.getLiveBean())
                .anchorBehaviorType(AnchorBehaviorType.LEAVE_LIVE.getValue()).appId(exitLivePostBean.getAppId())
                    .userId(exitLivePostBean.getUserId()).njId(exitLivePostBean.getLiveRoomBean().getUserId()).build());
    }

    private void sendDeviceBehaviorMsg(ExitLivePostBean exitLivePostBean) {
        hyBehaviorKafkaProducer.sendDeviceBehavior(HyDeviceBehaviorBean.builder().userId(exitLivePostBean.getUserId())
                .deviceBehaviorType(DeviceBehaviorType.LEAVE_ROOM.getValue()).deviceId(exitLivePostBean.getDeviceId())
                    .liveRoomId(exitLivePostBean.getLiveRoomBean().getId()).roomType(RoomType.LIVE_ROOM.getValue())
                        .liveId(exitLivePostBean.getLiveBean().getId()).appId(exitLivePostBean.getAppId())
                            .njId(exitLivePostBean.getLiveRoomBean().getUserId()).build());
    }


    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }
}
