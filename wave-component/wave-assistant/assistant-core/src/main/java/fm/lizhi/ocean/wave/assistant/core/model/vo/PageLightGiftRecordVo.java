package fm.lizhi.ocean.wave.assistant.core.model.vo;

import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageLightGiftRecordVo {

    /**
     * 送礼人
     */
    private SimpleUserVo sendUser;


    /**
     * 收礼人
     */
    private SimpleUserVo recUser;


    /**
     * 礼物ID
     */
    private Long giftId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 礼物数量
     */
    private Integer giftAmount;

    /**
     * 奖励档位
     */
    private Long rewardLadder;

    /**
     * 奖励金额
     */
    private Long rewardAmount;



}
