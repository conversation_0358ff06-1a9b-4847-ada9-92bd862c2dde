package fm.lizhi.ocean.wave.amusement.core.model.param;

import fm.lizhi.ocean.wave.amusement.core.constant.AntiCheatStrategyEnum;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AntiRushParam {

    private long userId;
    private String eventId;
    private String deviceId;
    private String ip;
    /**
     * 损失金币，默认填0。例如：红包场景，红包若被机器人抢走，则损失就是被抢走金币。
     */
    private long loss;

    /**
     * appId
     */
    private int appId;

    private String riskParamsJson;

}
