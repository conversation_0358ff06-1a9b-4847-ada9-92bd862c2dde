package fm.lizhi.ocean.wave.common.datastore.redis;

/**
 * 用户心跳redis key
 *
 * <AUTHOR>
 */
public enum CommonRedisKey implements CacheKeyGenerator.CacheKeyType {
    /**
     * AI问题大全结果
     * 数据结构：LIST
     * key：AI_QUESTION_RESULT_LIST_#{questionConfigId}
     * value: 问题结果
     */
    AI_QUESTION_RESULT_LIST,

    /**
     * AI问题生成限流
     * 数据结构：STR
     * key：AI_QUESTION_RESULT_LIST_#{userId}_#{min}
     * value: 限流次数
     */
    AI_QUESTION_GEN_LIMITING_STR,

    /**
     * AI生成问题分布式锁
     * 数据结构：STR
     * key：AI_GEN_QUESTION_LOCK_#{questionConfigId}
     * value: 限流次数
     */
    AI_GEN_QUESTION_LOCK,

    /**
     * AI润色请求次数
     * 数据结构：STR
     * key：USER_AI_POLISH_REQ_COUNT_#{userId}_#{day}
     * value: 请求次数
     * param: day 日期,格式yyyyMMdd,默认当天, userId 用户ID
     */
    AI_POLISH_REQ_COUNT_STR,
    ;


    @Override
    public String getPrefix() {
        return "USER";
    }

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        switch (this) {
            default:
                sb.append("_");
                sb.append(this.name());
                break;
        }

        for (Object o : args) {
            sb.append("_" + o);
        }
        return sb.toString();
    }
}
