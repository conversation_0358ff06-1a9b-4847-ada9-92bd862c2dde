package fm.lizhi.ocean.wave.live.core.controller;

import fm.lizhi.ocean.wave.live.core.model.param.SearchKickOutRecordParam;
import fm.lizhi.ocean.wave.live.core.model.vo.QueryKickOutUserVO;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.manager.LiveKickOutManager;
import fm.lizhi.ocean.wave.live.core.model.param.KickOutUserParam;
import fm.lizhi.ocean.wave.live.core.model.param.QueryKickOutUserListParam;
import fm.lizhi.ocean.wave.live.core.model.result.KickOutUserListResult;
import fm.lizhi.ocean.wave.live.core.model.vo.KickOutConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Slf4j
@RestController
public class LiveKickOutController {


    @Autowired
    private LiveKickOutManager liveKickOutManager;


    /**
     * 黑名单列表
     *
     * @param param
     * @return
     */
    @GetMapping("/live/userList/kickOutUsers")
    @VerifyUserToken
    public ResultVO<KickOutUserListResult> queryKickOutUsers(QueryKickOutUserListParam param) {
        if (Objects.isNull(param.getLiveId())) {
            return ResultVO.failure();
        }
        return liveKickOutManager.queryKickOutUsers(param);
    }


    /**
     * 操作黑名单
     * @param param
     * @return
     */
    @PostMapping("/live/userOperate/kickOut")
    @VerifyUserToken
    public ResultVO<Void> kickOut(@RequestBody @Validated KickOutUserParam param) {
        if (Objects.isNull(param.getLiveId())
                || (StringUtils.isBlank(param.getBand()) && Objects.isNull(param.getUserId()))
                || Objects.isNull(param.getTickFlag())) {
            return ResultVO.failure();
        }
        long operateUid = ContextUtils.getContext().getUserId();
        param.setOperateUserId(operateUid);

        return liveKickOutManager.kickOut(param);
    }


    /**
     * 踢出校验（二次弹窗）
     * @return
     */
    @PostMapping("/live/userOperate/checkKickOut")
    @VerifyUserToken
    public ResultVO<KickOutConfigVo> checkKickOut(@RequestBody KickOutUserParam param) {
        if (Objects.isNull(param.getLiveId())
                || (StringUtils.isBlank(param.getBand()) && Objects.isNull(param.getUserId()))
                || Objects.isNull(param.getTickFlag())) {
            return ResultVO.failure();
        }

        long operateUid = ContextUtils.getContext().getUserId();
        param.setOperateUserId(operateUid);
        return liveKickOutManager.checkKickOut(param);
    }

    @GetMapping("/live/userOperate/searchKickOutUser")
    @VerifyUserToken
    public ResultVO<QueryKickOutUserVO> searchKickOutUser(@Validated SearchKickOutRecordParam param) {
        return liveKickOutManager.searchKickOutUserRecordList(param);
    }
}
