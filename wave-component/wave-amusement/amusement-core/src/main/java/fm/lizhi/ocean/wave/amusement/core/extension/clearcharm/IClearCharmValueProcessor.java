package fm.lizhi.ocean.wave.amusement.core.extension.clearcharm;

import fm.lizhi.ocean.wave.amusement.core.extension.clearcharm.bean.ClearCharmPostBean;
import fm.lizhi.ocean.wave.amusement.core.extension.clearcharm.bean.ClearCharmPreBean;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.common.extension.IProcessor;

import java.util.Arrays;
import java.util.List;

/**
 * 清理魅力值差异化处理器
 */
public interface IClearCharmValueProcessor extends IProcessor<ClearCharmPreBean, Void, ClearCharmPostBean, Void> {

    @Override
    default Class<? extends IProcessor> getBaseBusinessProcessor() {
        return IClearCharmValueProcessor.class;
    }

    /**
     * 获取当前操作所需要的用户权限标识
     *
     * @return
     */
    default List<PlatformRoleEnum> getPlatformRoleEnum(){
        return Arrays.asList(PlatformRoleEnum.ROLE_HOST);
    }

}
