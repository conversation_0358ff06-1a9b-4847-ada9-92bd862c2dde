package fm.lizhi.ocean.wave.common.auto.route.common.remote;

import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import fm.lizhi.ocean.wave.common.util.SpringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RemoteServiceInvokeSelector implements IRemoteServiceInvokerSelector {

    @Override
    public <T> T getInstance(Class<T> t) {
        IBaseRemoteServiceInvoker selector = null;
        List<T> services = SpringUtils.getImplementationsOfMyInterface(t);
        for (T service : services) {
            if (service instanceof IBaseRemoteServiceInvoker) {
                selector = (IBaseRemoteServiceInvoker) service;

                ServiceContext serviceContext = ContextUtils.getContext();
                if (null == serviceContext) {
                    log.error("service context empty");
                    throw new RuntimeException("service context empty");
                }

                if (null == serviceContext.getBusinessEvnEnum()) {
                    log.error("business env empty");
                    throw new RuntimeException("business env empty");
                }


                if (selector.support(serviceContext.getBusinessEvnEnum())) {
                    return (T) selector;
                }
            }
        }

        throw new RuntimeException("Class no find");
    }
}
