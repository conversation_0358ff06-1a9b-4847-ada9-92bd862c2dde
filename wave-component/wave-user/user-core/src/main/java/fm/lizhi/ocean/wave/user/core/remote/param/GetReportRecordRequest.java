package fm.lizhi.ocean.wave.user.core.remote.param;

import fm.lizhi.ocean.wave.user.core.remote.bean.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetReportRecordRequest {

    /**
     * 举报人id
     */
    private Long userId;

    /**
     * 被举报人信息
     */
    private SimpleUser simpleUser;

}
