package fm.lizhi.ocean.wave.api.live.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/17
 */
public enum LiveStatusEnum {

    /**
     * 预告
     */
    FORENOTICE(0),
    /**
     * 直播中
     */
    ON_AIR(1),
    /**
     * 结束/关播
     */
    CLOSED(2),
    /**
     * 删除
     */
    DELETED(3),
    ;

    private final int value;

    private static final Map<Integer, LiveStatusEnum> MAP = new HashMap<>(8);

    static {
        for (LiveStatusEnum item : LiveStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }
    }

    LiveStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static LiveStatusEnum from(int appId) {
        return MAP.get(appId);
    }

}
