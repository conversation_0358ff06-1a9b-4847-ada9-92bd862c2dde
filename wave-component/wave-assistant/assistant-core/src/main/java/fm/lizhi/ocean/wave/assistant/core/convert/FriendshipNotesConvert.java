package fm.lizhi.ocean.wave.assistant.core.convert;

import fm.lizhi.ocean.wave.assistant.core.model.result.WaveFriendshipNotesResult;
import fm.lizhi.ocean.wave.assistant.core.model.vo.WaveFriendshipNotesVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.WaveFriendshipTabVo;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveFriendshipNotesBean;
import fm.lizhi.ocean.wave.platform.api.user.bean.WaveFriendshipTabBean;
import fm.lizhi.ocean.wave.platform.api.user.response.ResponseFriendshipNotes;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FriendshipNotesConvert {

    FriendshipNotesConvert I = Mappers.getMapper(FriendshipNotesConvert.class);

    WaveFriendshipNotesVo convertWaveFriendshipNotesVo(WaveFriendshipNotesBean target);

    WaveFriendshipNotesResult convertWaveFriendshipNotesResult(ResponseFriendshipNotes target);

    List<WaveFriendshipTabVo> convertWaveFriendshipTabVo(List<WaveFriendshipTabBean> target);
}
