package fm.lizhi.ocean.wave.amusement.core.remote.adapter.xm;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallBizTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallHintContentTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallStatusEnum;
import fm.lizhi.ocean.wave.amusement.core.model.param.HeartbeatCallStartMatchParam;
import fm.lizhi.ocean.wave.amusement.core.remote.bean.VoiceCallHintBean;
import fm.lizhi.ocean.wave.amusement.core.remote.param.RequestCallOrderInfo;
import fm.lizhi.ocean.wave.amusement.core.remote.result.ResponseCallOrderInfo;
import fm.lizhi.ocean.wave.amusement.core.remote.result.ResponseCreateVoiceCall;
import fm.lizhi.ocean.wave.amusement.core.remote.result.ResponseVoiceCallHeartBeat;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.xm.social.bean.req.MatchStartReq;
import fm.lizhi.xm.social.chat.protocol.MatchProto;
import fm.lizhi.xm.social.chat.protocol.PpVoiceCallBaseProto;
import fm.lizhi.xm.social.chat.protocol.PpVoiceCallProto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class XmVoiceCallAdapter {

    @Autowired
    private AmusementConfig config;

    /**
     * 转换心跳结果
     *
     * @param heartBeat 心跳结果
     * @return 转换结果
     */
    public ResponseVoiceCallHeartBeat convertHeartBeatResult(PpVoiceCallProto.ResponseVoiceCallHeartBeat heartBeat) {
        List<PpVoiceCallBaseProto.VoiceCallHint> hintsList = heartBeat.getHintsList();
        ResponseVoiceCallHeartBeat.ResponseVoiceCallHeartBeatBuilder builder = ResponseVoiceCallHeartBeat.builder();
        if (CollectionUtils.isNotEmpty(hintsList)) {
            List<VoiceCallHintBean> hints = new ArrayList<>(hintsList.size());
            for (PpVoiceCallBaseProto.VoiceCallHint hint : hintsList) {
                VoiceCallHintBean hintBean = VoiceCallHintBean.builder()
                        .content(hint.getContent())
                        .color(hint.getColor())
                        .size(hint.getSize())
                        .timestamp(hint.getTimestamp())
                        .contentType(VoiceCallHintContentTypeEnum.SYSTEM.getContentType())
                        .build();
                hints.add(hintBean);
            }
            builder.hints(hints);
        }
        builder.callWaveType(heartBeat.getCallBizType())
                .callStatus(VoiceCallStatusEnum.getWaveCallStatus(heartBeat.getCallStatus(), BusinessEvnEnum.XIMI))
                .playerHangUp(heartBeat.getPlayerHangUp())
                .playerId(heartBeat.getPlayerId())
                .build();
        return builder.build();
    }

    public ResponseCreateVoiceCall convertCreateVoiceCallResult(PpVoiceCallProto.ResponseCreateVoiceCall responseCreateVoiceCall, long userId) {
        return new ResponseCreateVoiceCall().setChannelId(responseCreateVoiceCall.getVoiceCall().getCallChannel().getChannelId())
                .setAppKey(config.getXm().getAgoraAppKey())
                //业务侧的呼叫者是陪玩，在PC侧统一呼叫者是消费者，接听者是陪玩，所以赋值要反过来
                .setCalleeVoiceId(findCallerVoiceId(responseCreateVoiceCall.getVoiceCall(), userId))
                .setCallId(responseCreateVoiceCall.getVoiceCall().getCallId())
                .setVoiceCallBizType(VoiceCallBizTypeEnum.bizValue2Wave(responseCreateVoiceCall.getVoiceCall().getBizType().getNumber()))
                .setVoiceCallWaveStatus(VoiceCallStatusEnum.bizValue2WaveStatus(responseCreateVoiceCall.getVoiceCall().getCallStatus().getNumber()));
    }

    public MatchProto.GetMatchOrderInfoRequest convertCallOrderParam(RequestCallOrderInfo request) {
        int bizType = VoiceCallBizTypeEnum.waveValue2biz(request.getCallBizType());
        return MatchProto.GetMatchOrderInfoRequest.newBuilder()
                .setUserId(request.getUserId())
                .setBizId(request.getMatchId())
                .setBizType(bizType)
                .build();
    }

    /**
     * 转换通话订单信息结果
     *
     * @param matchOrderInfoJson 结果json
     * @return 转换结果
     */
    public ResponseCallOrderInfo convertCallOrderInfoResult(String matchOrderInfoJson) {
        JSONObject orderObject = JSONObject.parseObject(matchOrderInfoJson);
        return ResponseCallOrderInfo.builder()
                .income(orderObject.getLongValue("income"))
                .liveId(orderObject.getLongValue("liveId"))
                .hangUpContent(orderObject.getString("hangUpContent"))
                .userId(orderObject.getLongValue("userId"))
                .targetUid(orderObject.getLongValue("targetUid"))
                .player(orderObject.getBoolean("player"))
                .relation(orderObject.getBoolean("relation"))
                .price(orderObject.getIntValue("price"))
                .outOrderId(orderObject.getLongValue("outOrderId"))
                .build();
    }

    private long findCallerVoiceId(PpVoiceCallBaseProto.VoiceCall voiceCall, long userId) {
        if (voiceCall.getCalleeUid() == userId) {
            return voiceCall.getCallChannel().getCalleeId();
        }
        return voiceCall.getCallChannel().getCallerId();
    }

    /**
     * 转换心动连线发起匹配参数
     * @param param
     * @param currentUserId
     * @return
     */
    public String convertMatchStartParam(HeartbeatCallStartMatchParam param, long currentUserId) {
        MatchStartReq matchStartReq = new MatchStartReq().setAppId((long) BusinessEvnEnum.XIMI.getAppId())
                .setBizId(config.getXm().getVoiceCallSceneId())
                .setType(VoiceCallBizTypeEnum.waveValue2biz(param.getType()))
                .setUserId(currentUserId)
                .setTargetId(param.getTargetUserId());
        return JSONObject.toJSONString(matchStartReq);
    }

}
