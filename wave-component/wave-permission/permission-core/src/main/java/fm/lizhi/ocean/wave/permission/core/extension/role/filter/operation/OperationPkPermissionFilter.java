package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import api.activity.api.LivePkService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PK权限过滤器
 * <AUTHOR>
 */
@Slf4j
@Component("operation_pk")
public class OperationPkPermissionFilter extends AbstractOperationPermissionFilter {

    @Autowired
    private LivePkService livePkService;
    @Autowired
    private PermissionConfig config;

    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.PP.equals(app) || BusinessEvnEnum.HEI_YE.equals(app);
    }


    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {
        // 黑叶需要在旧版隐藏跨房PK入口
        int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
        BusinessEvnEnum businessEvnEnum = ContextUtils.getContext().getBusinessEvnEnum();
        if (businessEvnEnum.equals(BusinessEvnEnum.HEI_YE) && config.getHy().getLivePkMinVersion() > 0 && clientVersion < config.getHy().getLivePkMinVersion()) {
            return false;
        }

        Result<Boolean> result = livePkService.checkUserPkAuthority(filterDto.getUserId(), filterDto.getLiveId(), filterDto.getNjId());
        if (RpcResult.isFail(result)){
            log.warn("OperationPkPermissionFilter hasPermission is fail. permission:{}, userId: {}, liveId: {}, njId: {}",
                    filterDto.getPermission(), filterDto.getUserId(), filterDto.getLiveId(), filterDto.getNjId());
            // 默认不放行
            return false;
        }

        return result.target();
    }
}
