package fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.standard;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
public class ApolloStandardProviderConfig {

    private List<BusinessStandardProviderConfig> businessStandardProviderConfigs = new ArrayList<>();

    public static ApolloStandardProviderConfig getInstance(String apolloConfig){
        ApolloStandardProviderConfig config = new ApolloStandardProviderConfig();
        if(StringUtils.isEmpty(apolloConfig)){
            return config;
        }
        try {
            config.setBusinessStandardProviderConfigs(JsonUtil.loadsArray(apolloConfig, BusinessStandardProviderConfig.class));
        }catch (Exception e){
            log.error("get apollo pojo provider config error.`apolloConfig={}", apolloConfig, e);
        }

        return config;
    }

    @Data
    public static class BusinessStandardProviderConfig{
        /**
         * {@link BusinessEvnEnum}
         */
        private String businessName;
        private List<String> providerSimpleNames = new ArrayList<>();
        public static BusinessStandardProviderConfig getInstance(String businessName, List<String> providerSimpleNames){
            BusinessStandardProviderConfig config = new BusinessStandardProviderConfig();
            config.setBusinessName(businessName);
            config.setProviderSimpleNames(providerSimpleNames);
            return config;
        }
    }
}
