package fm.lizhi.ocean.wave.live.core.remote.adapter.hy;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.room.hy.protocol.LiveNewProto;
import fm.lizhi.ocean.wave.api.live.constants.hy.HyLiveContentType;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.hy.HyLiveExtraBean;

import java.util.Date;

/**
 * @description: 业务与平台live转换器
 * @author: guoyibin
 * @create: 2023/08/23 19:49
 */
public class HyLiveBeanAdapter {

    public static LiveBean convert(LiveNewProto.Live live, String cdnHost) {
        HyLiveContentType hyLiveContentType = HyLiveContentType.from(live.getContentType());
        return LiveBean.builder()
                .id(live.getId())
                .userId(live.getUserId())
                .liveRoomId(live.getLiveRoomId())
                .name(live.getName())
                .introduction(live.getIntroduction())
                .imageUrl(UrlUtils.addCdnHost(cdnHost, live.getImageUrl()))
                .startTime(new Date(live.getStartTime()))
                .endTime(new Date(live.getEndTime()))
                .actualStartTime(new Date(live.getActualStartTime()))
                .actualEndTime(new Date(live.getActualEndTime()))
                .createTime(new Date(live.getCreateTime()))
                .modifyTime(new Date(live.getModifyTime()))
                .status(live.getStatus())
                .shareUrl(live.getShareUrl())
                .password(live.getPassword())
                .welcome(live.getWelcome())
                .extraJson(JsonUtil.dumps(HyLiveExtraBean.builder()
                        .tags(live.getTags())
                        .contentType(hyLiveContentType.getValue())
                        .roomType(live.getRoomType())
                        .build()))
                .build();

    }
}
