package fm.lizhi.ocean.wave.live.core.extension.income.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.income.IRoomIncomeProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HyRoomIncomeProcessor implements IRoomIncomeProcessor {

    @Autowired
    private LiveConfig config;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public CommonLiveConfig getLiveConfig() {
        return config.getHy();
    }
}
