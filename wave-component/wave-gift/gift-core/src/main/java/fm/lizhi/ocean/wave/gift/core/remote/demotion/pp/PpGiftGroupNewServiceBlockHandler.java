package fm.lizhi.ocean.wave.gift.core.remote.demotion.pp;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.blockhandler.rpc.IRpcFaceBlockHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.gift.api.GiftGroupNewService;
import pp.fm.lizhi.live.gift.protocol.GiftGroupNewProto;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpGiftGroupNewServiceBlockHandler implements IRpcFaceBlockHandler<DegradeException> {
    @Override
    public Class<DegradeException> getExceptionClass() {
        return DegradeException.class;
    }

    @Override
    public Class<?> getTarget() {
        return GiftGroupNewService.class;
    }


    /**
     * 获取分组
     */
    public Result<GiftGroupNewProto.ResponseQueryGiftGroup> queryGiftGroup(GiftGroupNewProto.QueryGiftGroupRequest request){
        log.info("PpGiftGroupNewServiceBlockHandler.queryGiftGroup block. scene={}", request.getScene());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GiftGroupNewProto.ResponseQueryGiftGroup.newBuilder().addAllGiftGroup(Collections.emptyList()).build());
    }
}
