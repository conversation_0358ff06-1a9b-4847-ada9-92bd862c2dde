package fm.lizhi.ocean.wave.api.amusement.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendOperationAPIParam {
    /**
     * 直播Id
     */
    private long liveId;
    /**
     * 发送用户Id
     */
    private long userId;
    /**
     * 替换字符
     */
    private String replaceStr;
    /**
     * 房间角色
     *
     * @see fm.lizhi.ocean.wave.api.amusement.constants.LiveRoomRoleConstants
     */
    private int liveRoomRole;
    /**
     * 操作消息类型
     *
     * @see fm.lizhi.ocean.wave.api.amusement.constants.OperationMessageTypeConstants
     */
    private int operationMessageType;

}
