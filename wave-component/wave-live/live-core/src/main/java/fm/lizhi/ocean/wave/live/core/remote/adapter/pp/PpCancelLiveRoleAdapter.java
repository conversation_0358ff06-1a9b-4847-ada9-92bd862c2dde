package fm.lizhi.ocean.wave.live.core.remote.adapter.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.pp.protocol.LiveRoomRoleProto;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.live.core.model.param.CancelRoleManagerParam;
import fm.lizhi.ocean.wave.live.core.remote.param.CancelRoleManagerRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoleServiceRemote;
import org.springframework.stereotype.Component;

@Component
public class PpCancelLiveRoleAdapter implements IRemoteMethodParamAndResultAdapter<CancelRoleManagerParam, CancelRoleManagerRemoteParam,
        Result<LiveRoomRoleProto.ResponseUpdateLiveRoomRole>, Result<Void>> {


    @Override
    public CancelRoleManagerRemoteParam convertParam(CancelRoleManagerParam args) {
        return CancelRoleManagerRemoteParam.builder()
                .operateUserId(args.getOperateUserId())
                .liveRoomId(args.getLiveRoomId())
                .liveUserId(args.getTargetUserId())
                .build();
    }

    @Override
    public Result<Void> convertResult(Result<LiveRoomRoleProto.ResponseUpdateLiveRoomRole> result) {
        Result<Void> resp = new Result<>(result.rCode(), null);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return new Result<>(ILiveRoleServiceRemote.CANCEL_MANAGER_ERROR, null);
        }
        resp.setMessage(result.getMessage());
        return resp;
    }

}
