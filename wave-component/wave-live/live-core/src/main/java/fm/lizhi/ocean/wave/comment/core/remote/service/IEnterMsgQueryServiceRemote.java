package fm.lizhi.ocean.wave.comment.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetNewFanNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetVehicleEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.*;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

/**
 * 进房公告适配器接口
 *
 * <AUTHOR>
 */
public interface IEnterMsgQueryServiceRemote extends IBaseRemoteServiceInvoker {
    /**
     * 获取座驾进房公告
     *
     * @param param 获取座驾进房公告参数
     * @return 座驾进房公告
     */
    Result<GetVehicleEnterNoticesResult> getVehicleEnterNoticesByRange(GetVehicleEnterNoticesByRangeParam param);

    /**
     * 获取新粉丝进房公告
     *
     * @param param 获取新粉丝进房公告参数
     * @return 新粉丝进房公告
     */
    Result<GetNewFanNoticesResult> getNewFanNoticesByRange(GetNewFanNoticesByRangeParam param);

    /**
     * 获取进房公告
     *
     * @param param 获取进房公告参数
     * @return 进房公告
     */
    Result<GetEnterNoticesResult> getEnterNoticesByRange(GetEnterNoticesByRangeParam param);


    int GET_ENTER_NOTICE_STATUS_ERROR = 1;

}
