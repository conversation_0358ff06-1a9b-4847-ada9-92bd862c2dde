package fm.lizhi.ocean.wave.assistant.core.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 收光奖励配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WaveCheckInLightGiftConfigVo {

    /**
     * 是否开启
     */
    private Boolean enabled;

    /**
     * 是否开启转让
     */
    private Boolean canTransfer;

    /**
     * 收光阶梯配置,json配置
     */
    private List<LadderBean> ladderList;


    /**
     * 阶梯
     */
    @Data
    @Accessors(chain = true)
    public static class LadderBean {

        /**
         * >= 魅力值
         */
        private Integer charmGte;

        /**
         * 奖励金额
         */
        private Integer rewardAmount;
    }
}
