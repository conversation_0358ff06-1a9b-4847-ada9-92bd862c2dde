package fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.hy;

import com.google.protobuf.InvalidProtocolBufferException;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.amusement.hy.api.HyNewLiveAmusementService;
import fm.lizhi.live.amusement.hy.protocol.LiveAmusementProto;
import fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.IInviteOnCallProcessor;
import fm.lizhi.ocean.wave.amusement.core.extension.inviteoncall.bean.InviteOnCallPreBean;
import fm.lizhi.ocean.wave.amusement.core.facade.AmusementPermissionServiceFacade;
import fm.lizhi.ocean.wave.amusement.core.remote.service.IAmusementServiceRemote;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import hy.fm.lizhi.live.data.api.UserBehaviorService;
import hy.fm.lizhi.live.data.protocol.LiveBehaviorProto;
import hy.fm.lizhi.live.pp.user.api.UserInviteService;
import hy.fm.lizhi.live.pp.user.protocol.UserInviteProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class HyInviteOnCallProcessor implements IInviteOnCallProcessor {


    @Autowired
    UserInviteService userInviteService;

    @Autowired
    UserBehaviorService userBehaviorService;

    @Autowired
    HyNewLiveAmusementService hyNewLiveAmusementService;

    @Autowired
    private AmusementPermissionServiceFacade amusementPermissionServiceFacade;

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public ResultVO<Void> preprocessor(InviteOnCallPreBean data) {
        long userId = data.getUserId();
        long targetUserId = data.getTargetUserId();
        long liveId = data.getLiveId();

        // 判断房主和管理权限
        if (userId != data.getNjId()) {
            boolean isCompere = amusementPermissionServiceFacade.havePermission(userId, data.getNjId(),
                    data.getLiveRoomId(), BusinessEvnEnum.HEI_YE.getAppId(), PlatformRoleEnum.ROLE_MANAGER);
            if (!isCompere) {
                return ResultVO.failure("不是房主或管理员，无法操作");
            }
        }

        //弹窗10秒内检验 重复点击不发推送
        Result<UserInviteProto.ResponseGetInviteOnCallCheck> callCheck = userInviteService.getInviteOnCallCheck(userId, targetUserId);
        int checkRCode = callCheck.rCode();
        // rCode=0:还在冷却中; rCode=1:冷却释放
        if (0 == checkRCode) {
            log.info("HyInviteOnCallProcessor callCheck userId={},inviteTargetUserId={}", userId, targetUserId);
            return ResultVO.failure(IAmusementServiceRemote.INVITE_ON_CALL_ERROR,"你邀请太频繁啦，请稍后再试哦");
        }

        Result<UserInviteProto.ResponseGetInviteOnCallClose> invite = userInviteService.getInviteOnCallClose(targetUserId);
        // rCode=0:还在冷却中; rCode=1:冷却释放
        int rCode = invite.rCode();
        if (0 == rCode) {
            log.info("HyInviteOnCallProcessor callClose userId={},inviteTargetUserId={}", userId, targetUserId);
            return ResultVO.failure(IAmusementServiceRemote.INVITE_ON_CALL_ERROR,"你邀请太频繁啦，请稍后再试哦");
        }

        //查询是否在直播间
        Long targetLiveId = this.getLiveIdIfInRoom(targetUserId);
        log.info("HyInviteOnCallProcessor getLiveIdIfInRoom userId={},targetUserId={},liveId={},targetUserLiveId={}",
                userId, targetUserId, data.getLiveId(), targetLiveId);
        if (Objects.nonNull(targetLiveId)) {
            //去了其他直播间,不能抱他上麦
            if (targetLiveId != liveId) {
                log.info("已经在其他直播间,liveId:{},targetLiveId:{},userid:{}", liveId, targetLiveId, userId);
                return ResultVO.failure(IAmusementServiceRemote.INVITE_ON_CALL_ERROR,"去了其他直播间,不能抱他上麦");

            }
            //退出直播间,不能抱他上麦
        } else {
            log.info("退出直播间,liveId:{},targetLiveId:{},userid:{}", liveId, targetLiveId, userId);
            return ResultVO.failure(IAmusementServiceRemote.INVITE_ON_CALL_ERROR,"退出直播间,不能抱他上麦");
        }

        //查询是否有空席
        boolean countFlag = this.fetchGuestCount(userId, liveId);
        if (countFlag) {
            log.info("HyInviteOnCallProcessor fetchGuestCount userId={},inviteTargetUserId={}", userId, targetUserId);
            return ResultVO.failure(IAmusementServiceRemote.INVITE_ON_CALL_ERROR,"麦位已满，暂无法邀请哦");
        }

        return IInviteOnCallProcessor.super.preprocessor(data);
    }

    /**
     * 判断房间麦上是否有空坐席
     * @param userId
     * @param liveId
     * @return
     */
    public boolean fetchGuestCount(long userId,long liveId) {
        try {
            Result<LiveAmusementProto.ResponseGuestCount> result = hyNewLiveAmusementService.fetchGuestCount(liveId);
            if (result.rCode() == 0 ) {
                long count = result.target().getCount();
                //小于8意思是有空位
                if (count < 8) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } catch (Exception e) {
            log.error("LiveAmusementManager fetchGuestCount error userId={},liveId={},error={}",userId,liveId,e);
            return true;
        }
    }

    /**
     * 查询用户行为状态。
     */
    private LiveBehaviorProto.ResponseGetUserBehaviorsNoCache getUserBehaviors(Long uid) {
        Result<LiveBehaviorProto.ResponseGetUserBehaviorsNoCache> result = userBehaviorService.getUserBehaviorsNoCache(LiveBehaviorProto.UserBehaviorsParams.newBuilder().setUserId(uid).build());
        int rCode = result.rCode();
        if (rCode != 0) {
            log.warn("getBatchUserBehaviorsRCode={}, uid={}", rCode, uid);
            return null;
        }
        return result.target();
    }

    /**
     * 是否在直播间中。
     *
     * @return liveId
     */
    public Long getLiveIdIfInRoom(Long uid) {
        try {
            LiveBehaviorProto.ResponseGetUserBehaviorsNoCache behaviors = getUserBehaviors(uid);
            if (behaviors == null) {
                return null;
            }
            Long liveIdFromUserBehavior = getLiveIdFromUserBehavior(behaviors.getBehaviorsList());
            return liveIdFromUserBehavior;
        } catch (Exception e) {
            log.warn("getLiveIdIfInRoomError={},uid={}", e.toString(), uid);
        }
        return null;
    }

    private Long getLiveIdFromUserBehavior(List<LiveBehaviorProto.UserBehavior> behaviors) throws InvalidProtocolBufferException {
        if(behaviors == null) {
            return null;
        }
        for (LiveBehaviorProto.UserBehavior userBehavior : behaviors) {
            int typeNum = userBehavior.getType().getNumber();
            if (typeNum == LiveBehaviorProto.BehaviorType.LIVE_LISTEN_VALUE) {
                LiveBehaviorProto.LiveListenBehaviorData liveListenBehaviorData = LiveBehaviorProto.LiveListenBehaviorData.parseFrom(userBehavior.getData());
                return liveListenBehaviorData.getLiveId();
            } else if (typeNum == LiveBehaviorProto.BehaviorType.LIVE_OPEN_VALUE) {
                LiveBehaviorProto.LiveOpenBehaviorData liveOpenBehaviorData = LiveBehaviorProto.LiveOpenBehaviorData.parseFrom(userBehavior.getData());
                return liveOpenBehaviorData.getLiveId();
            }
        }
        return null;
    }

}
