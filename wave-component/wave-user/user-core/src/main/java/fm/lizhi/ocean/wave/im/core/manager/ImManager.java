package fm.lizhi.ocean.wave.im.core.manager;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.remote.AntiCheatServiceRemote;
import fm.lizhi.ocean.wave.im.core.bean.*;
import fm.lizhi.ocean.wave.im.core.config.ImProviderConfig;
import fm.lizhi.ocean.wave.im.core.constant.ImBlockOpEnum;
import fm.lizhi.ocean.wave.im.core.constant.ImMsgCodes;
import fm.lizhi.ocean.wave.im.core.extension.IInviteUserEnterRoomProcessor;
import fm.lizhi.ocean.wave.im.core.extension.IUserOperateUserBlockProcessor;
import fm.lizhi.ocean.wave.im.core.extension.ImProcessor;
import fm.lizhi.ocean.wave.im.core.remote.service.ICloseFriendRelationServiceRemote;
import fm.lizhi.ocean.wave.im.core.remote.service.IUserListInviteRoomServiceRemote;
import fm.lizhi.ocean.wave.im.core.remote.service.ImPrivateReportRemote;
import fm.lizhi.ocean.wave.im.core.remote.service.UserBlockRemote;
import fm.lizhi.ocean.wave.im.core.remote.service.bean.ImBlockListParam;
import fm.lizhi.ocean.wave.im.core.remote.service.bean.PrivateReportParam;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import fm.lizhi.ocean.wave.user.core.model.param.InviteEnterRoomParam;
import fm.lizhi.ocean.wave.user.core.remote.service.VipPrivilegeServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ImManager {

    @MyAutowired
    ICloseFriendRelationServiceRemote closeFriendRelationServiceRemote;

    @Autowired
    UserService userService;

    @Autowired
    LiveService liveService;

    @Autowired
    LiveRoomService liveRoomService;

    @MyAutowired
    private IUserListInviteRoomServiceRemote userListInviteRoomServiceRemote;

    @Autowired
    private AntiCheatServiceRemote antiCheatServiceRemote;

    @Autowired
    private ProcessorV2Factory factory;

    @MyAutowired
    private UserBlockRemote userBlockRemote;


    @MyAutowired
    private ImPrivateReportRemote imPrivateReportRemote;

    @Autowired
    private ImProviderConfig imProviderConfig;

    @MyAutowired
    private VipPrivilegeServiceRemote vipPrivilegeServiceRemote;


    /**
     * 发送戳一戳消息
     *
     * @param receiverId 戳一戳消息接收者id
     * @return 发送成功或者失败
     */
    public ResultVO<Void> pokeMsg(Long receiverId) {
        if (receiverId <= 0) {
            return ResultVO.failure(ImMsgCodes.SEND_POKE_MSG_FAIL.getCode(), ImMsgCodes.SEND_POKE_MSG_FAIL.getMsg());
        }
        long userId = ContextUtils.getContext().getUserId();
        log.info("param senderId ={},receiverId={}", userId, receiverId);
        Result<Void> result = closeFriendRelationServiceRemote.sendPokeMsg(userId, receiverId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(ImMsgCodes.SEND_POKE_MSG_FAIL.getCode(), ImMsgCodes.SEND_POKE_MSG_FAIL.getMsg());
        }
        return ResultVO.success();

    }

    /**
     * 邀请用户进入当前房间
     *
     * @param param 邀请参数
     * @return 邀请结果
     */
    public ResultVO<Void> inviteUserEnterCurrentRoom(InviteEnterRoomParam param) {
        long userId = ContextUtils.getContext().getUserId();
        log.info("param senderId ={},receiverId={}", userId, param.getTargetUserId());
        if (param.getTargetUserId() <= 0 || param.getLiveId() <= 0 || StringUtils.isBlank(param.getMsgSource())) {
            return ResultVO.failure(ImMsgCodes.INVITE_USER_ENTER_ROOM_PARAM_FAIL.getCode(), ImMsgCodes.INVITE_USER_ENTER_ROOM_PARAM_FAIL.getMsg());
        }

        Result<Void> inviteRes = userListInviteRoomServiceRemote.userListInviteRoom(userId, param.getTargetUserId(), param.getLiveId(), param.getMsgSource());
        if (inviteRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("invite user enter room fail,userId={},targetUserId={},liveId={},msgSource={},rCode={}", userId, param.getTargetUserId(), param.getLiveId(), param.getMsgSource(), inviteRes.rCode());
            return ResultVO.failure(ImMsgCodes.INVITE_USER_ENTER_ROOM_FAIL.getCode(), ImMsgCodes.INVITE_USER_ENTER_ROOM_FAIL.getMsg());
        }
        IInviteUserEnterRoomProcessor processor = factory.getProcessor(IInviteUserEnterRoomProcessor.class);
        //邀请成功后的处理
        processor.successAfterProcessor(param);
        return ResultVO.success();
    }


    /**
     * 用户拉黑状态
     *
     * @param userId
     * @param targetUserId
     * @return
     */
    public ResultVO<ImGetUserBlockStatusResq> getUserBlockStatus(Long userId, Long targetUserId) {
        boolean blockStatus = userBlockRemote.checkBlockStatus(userId, targetUserId);
        return ResultVO.success(ImGetUserBlockStatusResq.builder().blockStatus(blockStatus).build());
    }

    /**
     * 双方拉黑状态
     *
     * @param userId
     * @param targetUserId
     * @return
     */
    public ResultVO<ImGetBlockRelationStatusResq> getBlockRelationStatus(Long userId, Long targetUserId) {
        boolean blockStatus = userBlockRemote.checkBlockStatus(userId, targetUserId);
        boolean targetBlockStatus = userBlockRemote.checkBlockStatus(targetUserId, userId);
        // 0：互相未拉黑。  1：a拉黑b。  2：b拉黑a。  3：互相拉黑
        int blockRelationStatus = 0;
        if (blockStatus && targetBlockStatus) {
            blockRelationStatus = 3;
        } else if (blockStatus) {
            blockRelationStatus = 1;
        } else if (targetBlockStatus) {
            blockRelationStatus = 2;
        }

        return ResultVO.success(ImGetBlockRelationStatusResq.builder().blockStatus(blockRelationStatus).build());
    }


    /**
     * 获取用户的拉黑列表
     *
     * @param userId
     * @return
     */
    public ImBlockPageListVo getUserBlockList(Long userId, int pageNum, int pageSize) {
        ImBlockListParam imBlockListParam = new ImBlockListParam();
        imBlockListParam.setUserId(userId);
        imBlockListParam.setPageNum(pageNum);
        imBlockListParam.setPageSize(pageSize);

        ImBlockPageListVo result = userBlockRemote.getBlockList(imBlockListParam);
        if (CollectionUtils.isEmpty(result.getRows())) {
            return result;
        }

        List<ImBlockUserVo> imBlockUserVoList = result.getRows();
        // 是否查询用户在线状态
        if (imBlockUserVoList.size() > imProviderConfig.getNoQueryUserStatusMaxSize()) {
            return result;
        }

        List<Long> userIds = imBlockUserVoList.stream().map(ImBlockUserVo::getUserId).collect(Collectors.toList());
        Result<Map<Long, Boolean>> getUserOnlineStatusResult = userService.batchGetUserOnlineStatus(userIds);
        if (getUserOnlineStatusResult.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && !getUserOnlineStatusResult.target().isEmpty()) {
            Map<Long, Boolean> statusMap = getUserOnlineStatusResult.target();
            // 构建用户的在线状态
            buildUserOnlineStatus(statusMap, imBlockUserVoList);
            result.setRows(imBlockUserVoList);
        }
        return result;
    }

    private void buildUserOnlineStatus(Map<Long, Boolean> statusMap, List<ImBlockUserVo> blockList) {
        for (ImBlockUserVo userVo : blockList) {
            Boolean status = statusMap.getOrDefault(userVo.getUserId(), false);
            OnlineStatusEnum onlineStatusEnum = status ? OnlineStatusEnum.ONLINE : OnlineStatusEnum.OFFLINE;
            userVo.setOnlineStatus(status);
            userVo.setOnlineStatusDesc(onlineStatusEnum.getName());
        }
    }


    /**
     * @param userId
     * @param targetUserId
     * @param imBlockOpEnum
     * @return
     */
    public ResultVO<Void> opBlock(Long userId, Long targetUserId, ImBlockOpEnum imBlockOpEnum) {

        UserOperateBlockParam param = UserOperateBlockParam.builder()
                .imBlockOpEnum(imBlockOpEnum)
                .userId(userId)
                .targetUserId(targetUserId)
                .build();
        boolean userOperateBlockResult = userBlockRemote.userOperateBlock(param);
        if (!userOperateBlockResult) {
            return ResultVO.failure(ImMsgCodes.OP_BLOCK_ERROR.getCode(), ImMsgCodes.OP_BLOCK_ERROR.getMsg());
        }
        IUserOperateUserBlockProcessor processor = factory.getProcessor(IUserOperateUserBlockProcessor.class);
        Result<Void> afterProcessorResult = processor.successAfterProcessor(param);
        if (afterProcessorResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(ImMsgCodes.OP_BLOCK_AFTER_ERROR.getCode(), ImMsgCodes.OP_BLOCK_AFTER_ERROR.getMsg());
        }
        return ResultVO.success();
    }

    /**
     * im的私信举报
     *
     * @param userId 举报人id
     * @param req
     * @return
     */
    public ResultVO<Void> privateReport(Long userId, ImPrivateReportReq req) {
        Result<Void> imPrivateReportResult = imPrivateReportRemote.imPrivateReport(
                PrivateReportParam.builder()
                        .content(req.getContent())
                        .msgId(req.getMsgId())
                        .msgType(req.getMsgType())
                        .targetUserId(req.getTargetUserId())
                        .userId(userId)
                        .build()
        );
        if (imPrivateReportResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(ImMsgCodes.OP_PRIVATE_REPORT_ERROR);
        }
        return ResultVO.success();
    }

    /**
     * 获取用户是否开启消息已读回执
     * @param userId 用户id
     * @return true:已开启，false:未开启
     */
    public boolean checkUserReadReceipt(long userId) {
        ImProcessor processor = factory.getProcessor(ImProcessor.class);
        ResultVO<Boolean> result = processor.checkOpenChatReadReceipt(userId);
        if (result.getRCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return false;
        }

        return result.getData();
    }

}

