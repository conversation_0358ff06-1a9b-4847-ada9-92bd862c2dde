package fm.lizhi.ocean.wave.live.core.remote.adapter.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.api.LiveNewService;
import fm.lizhi.live.room.hy.protocol.LiveNewProto;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Slf4j
@Component
public class HyGetLiveAdapter implements IRemoteMethodParamAndResultAdapter<
        GetLiveRemoteParam, LiveNewProto.GetLiveParams,
        Result<LiveNewProto.ResponseGetLiveNoCache>,
        Result<GetLiveRemoteResult>
        > {

    @Autowired
    LiveConfig liveConfig;

    @Autowired
    private CommonProviderConfig commonProviderConfig;


    @Override
    public LiveNewProto.GetLiveParams convertParam(GetLiveRemoteParam params) {
        return LiveNewProto.GetLiveParams.newBuilder()
                .setLiveId(params.getLiveId())
                .build();
    }

    @Override
    public Result<GetLiveRemoteResult> convertResult(Result<LiveNewProto.ResponseGetLiveNoCache> result) {
        int rCode = result.rCode();

        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (rCode == LiveNewService.GET_LIVE_NO_CACHE_ERR_NOT_EXIST) {
                return new Result<>(ILiveServiceRemote.GET_LIVE_NOT_FOUND, null);
            }
            return new Result<>(ILiveServiceRemote.GET_LIVE_ERROR, null);
        }

        LiveNewProto.Live live = result.target().getLive();
        String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.HEI_YE.getAppId()).getCdnHost();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GetLiveRemoteResult.builder()
                .defaultWelcomeMsg(liveConfig.getHy().defaultLiveWelcomeMsg)
                .liveBean(HyLiveBeanAdapter.convert(live, cdnHost)).build());
    }
}
