package fm.lizhi.ocean.wave.im.core.extension.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.im.core.bean.UserOperateBlockParam;
import fm.lizhi.ocean.wave.im.core.extension.IUserOperateUserBlockProcessor;
import org.springframework.stereotype.Component;

@Component
public class XmUserOperateUserBlockProcessor implements IUserOperateUserBlockProcessor {


    @Override
    public Result<Void> successAfterProcessor(UserOperateBlockParam param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,null);
    }


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

}
