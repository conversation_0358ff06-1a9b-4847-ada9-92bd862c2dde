package fm.lizhi.ocean.wave.assistant.core.model.bean;

import lombok.Data;

@Data
public class CheckInTotalDataBean {

    /**
     * 打卡主播总数
     */
    private Integer checkNjCount;

    /**
     * 总收入
     */
    private Integer totalIncome;

    /**
     * 总魅力值
     */
    private Integer totalCharmValue;

    /**
     * 总打卡次数
     */
    private Integer totalCheckInCount;

    public static CheckInTotalDataBean noData() {
        CheckInTotalDataBean checkInTotalDataBean = new CheckInTotalDataBean();
        checkInTotalDataBean.setCheckNjCount(0);
        checkInTotalDataBean.setTotalIncome(0);
        checkInTotalDataBean.setTotalCharmValue(0);
        checkInTotalDataBean.setTotalCheckInCount(0);
        return checkInTotalDataBean;
    }

    public static CheckInTotalDataBean of(Integer checkNjCount, Integer totalIncome, Integer totalCharmValue, Integer totalCheckInCount) {
        CheckInTotalDataBean checkInTotalDataBean = new CheckInTotalDataBean();
        checkInTotalDataBean.setCheckNjCount(checkNjCount);
        checkInTotalDataBean.setTotalIncome(totalIncome);
        checkInTotalDataBean.setTotalCharmValue(totalCharmValue);
        checkInTotalDataBean.setTotalCheckInCount(totalCheckInCount);
        return checkInTotalDataBean;
    }

}
