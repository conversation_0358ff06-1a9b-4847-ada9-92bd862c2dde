package fm.lizhi.ocean.wave.live.core.extension.exitlive.bean;

import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 退房后置入参
 * @author: guoyibin
 * @create: 2023/05/25 22:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExitLivePostBean {

    private LiveBean liveBean;

    private LiveRoomBean liveRoomBean;

    private long appId;

    private String deviceId;

    private long userId;

    private String ip;

    private String clientVersion;

}
