package fm.lizhi.ocean.wave.activity.core.extension.pk;

import api.activity.api.LivePkService;
import api.activity.constants.WaveLivePkOperateEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.pp.constants.LiveStatus;
import fm.lizhi.ocean.wave.activity.core.config.ActivityConfig;
import fm.lizhi.ocean.wave.activity.core.constant.ActivityMsgCodes;
import fm.lizhi.ocean.wave.activity.core.constant.LivePkCannotInviteDescEnum;
import fm.lizhi.ocean.wave.activity.core.constant.PpLiveMatchModelEnum;
import fm.lizhi.ocean.wave.activity.core.extension.pk.bean.*;
import fm.lizhi.ocean.wave.activity.core.model.param.PkSearchRoomParam;
import fm.lizhi.ocean.wave.activity.core.model.vo.pk.PkFamilyRoomVo;
import fm.lizhi.ocean.wave.activity.core.model.vo.pk.PkInfoVo;
import fm.lizhi.ocean.wave.activity.core.model.vo.pk.PkSearchRoomVo;
import fm.lizhi.ocean.wave.activity.core.remote.adapter.pp.PpLivePkAdapter;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementService;
import fm.lizhi.ocean.wave.api.amusement.constants.UserPlayStatusEnum;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.bean.Live;
import fm.lizhi.ocean.wave.api.live.bean.LiveOnlineUser;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.constants.LiveStatusEnum;
import fm.lizhi.ocean.wave.api.live.param.GetLatestLiveParam;
import fm.lizhi.ocean.wave.api.live.param.GetLiveModeRequestParam;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.*;
import fm.lizhi.ocean.wave.api.permission.api.WaveRolePermissionService;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveModeRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveModeRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.constant.MsgCodes;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.WaveAssert;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.param.GetSimpleUserParam;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import org.springframework.util.CollectionUtils;
import pp.fm.lizhi.pp.match.api.PpMatchCourseService;
import pp.fm.lizhi.pp.match.protocol.PpMatchCourseProto;
import pp.fm.lizhi.pp.mutex.api.MutexService;
import pp.fm.lizhi.pp.mutex.protocol.MutexProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.tuple.Pair;
import pp.fm.lizhi.pp.pk.api.PkMatchManageService;
import pp.fm.lizhi.pp.pk.protocol.PkMatchManageProto;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static api.activity.constants.WaveLivePkOperateEnum.*;
import static api.activity.constants.WaveLivePkOperateEnum.PK_REJECT_INVITE;

@Component
@Slf4j
public class PpPkProcessor implements IPkOperateProcessor {
    private final ExecutorService executor = ThreadUtils.getTtlExecutors(getClass().getSimpleName());

    @Autowired
    private LiveService liveService;
    @Autowired
    private WaveRolePermissionService rolePermissionService;
    @Autowired
    private MutexService mutexService;
    @Autowired
    private PpMatchCourseService ppMatchCourseService;
    @Autowired
    private ActivityConfig activityConfig;
    @Autowired
    private AmusementService amusementService;
    @Autowired
    private LivePkService livePkService;
    @Autowired
    private FamilyService familyService;
    @Autowired
    private UserService userService;
    @Autowired
    private LiveRoomService liveRoomService;
    @Autowired
    private PkMatchManageService pkMatchManageService;
    @Autowired
    private PpLivePkAdapter livePkAdapter;
    @MyAutowired
    private ILiveServiceRemote iLiveServiceRemote;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public ResultVO<Void> beforePkInviteCheck(PkInviteCheckContext context) {
        Long liveId = context.getLiveId();
        Long userId = context.getUserId();
        BusinessEvnEnum app = context.getApp();
        if (liveId == null || liveId <= 0 || userId == null || userId <= 0 || app == null) {
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }
        int appId = context.getApp().getAppId();
        //基础校验
        ResultVO<Pair<Long, PlatformRoleEnum>> baseCheck = pkBaseCheck(liveId, userId, appId);
        if (!baseCheck.isOK()) {
            return ResultVO.failure(baseCheck.getRCode(), baseCheck.getPrompt());
        }
        //玩法互斥
        ResultVO<Void> mutex = checkRoomFeatureMutex(liveId);
        if (!mutex.isOK()) {
            return mutex;
        }

        // 校验1v1
        Result<UserPlayStatusEnum> playSateResult = amusementService.getUserPlaySate(userId);
        if (RpcResult.isFail(playSateResult)) {
            log.warn("PpPkProcessor.beforePkInviteCheck, getUserPlaySate is fail. userId:{}, rCode: {}", userId, playSateResult.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        if (UserPlayStatusEnum.VOICE_CALL_ING.equals(playSateResult.target())) {
            return ResultVO.failure(ActivityMsgCodes.PK_MANAGE_GUEST_VOICE_CALLING);
        }
        return checkUserAuthority(liveId, baseCheck.getData().getKey(), userId);
    }

    @Override
    public ResultVO<Void> pkInvitePostProcess(PkInvitePostProcessContext context) {
        return ResultVO.success();
    }

    @Override
    public ResultVO<PkOperateCheckResult> beforePkOperateCheck(PkOperateCheckContext context) {
        if (context.getLiveId() == null || context.getLiveId() <= 0
                || context.getUserId() == null || context.getUserId() <= 0 || context.getApp() == null) {
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }
        PkOperateCheckResult checkResult = new PkOperateCheckResult();
        WaveLivePkOperateEnum operateEnum = context.getOperateEnum();
        if (operateEnum == PK_JOIN_LINE || operateEnum == PK_EXIT_LINE
                || PK_DO_FINISH == operateEnum || PK_CANCEL_INVITE == operateEnum) {
            //基础校验
            ResultVO<Pair<Long, PlatformRoleEnum>> baseCheck = pkBaseCheck(context.getLiveId(), context.getUserId(),
                    context.getApp().getAppId());
            if (!baseCheck.isOK()) {
                return ResultVO.failure(baseCheck.getRCode(), baseCheck.getPrompt());
            }
            checkResult.setRole(baseCheck.getData().getRight());
        } else if (operateEnum == PK_ACCEPT_INVITE) {
            ResultVO<Void> vo = checkRoomFeatureMutex(context.getLiveId());
            if (!vo.isOK()) {
                return ResultVO.failure(vo.getRCode(), vo.getPrompt());
            }
        }
        return ResultVO.success(checkResult);
    }

    @Override
    public ResultVO<Void> pkOperatePostProcess(PkOperatePostProcessContext context) {
        return ResultVO.success();
    }

    @Override
    public String getCannotInvitePkDesc(LivePkCannotInviteDescEnum key) {
        return "";
    }

    /**
     * 非点唱厅支持PK
     *
     * @param liveId
     * @return
     */
    @Override
    public boolean isLiveNotSupportPk(long liveId) {
        if (liveId <= 0) {
            return true;
        }

        try {
            Result<GetLiveModeResult> result = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(liveId).build());
            return RpcResult.isSuccess(result)
                    && result.target().getLiveModeEnum().getLiveMode() == LiveModeEnum.NORMAL_VOCAL_ROOM.getLiveMode();
        } catch (Exception e) {
            //异常不阻塞PK推送
            log.warn("PpPkProcessor isLiveSupportPk fail. liveId={}", liveId, e);
            return false;
        }
    }

    @Override
    public ResultVO<List<PkSearchRoomVo>> searchPkRoom(PkSearchRoomParam param) {
        PkSearchRoomVo vo = new PkSearchRoomVo();
        Long curLiveId = param.getLiveId();

        // 查询用户
        Result<GetSimpleUserResult> userResult = userService.getSimpleUser(GetSimpleUserParam.builder().band(String.valueOf(param.getBand())).build());
        if (RpcResult.isFail(userResult) || userResult.target() == null) {
            log.warn("searchPkRoom userResult is null. band: {}", param.getBand());
            return ResultVO.success();
        }

        SimpleUser user = userResult.target().getSimpleUser();
        long targetUserId = user.getUserId();
        // 自己搜索自己
        if (param.getCurUserId() == targetUserId) {
            return ResultVO.success();
        }

        // 查询搜索对象的直播信息
        Live targetLive;
        Result<GetLatestLiveResult> liveResult = liveService.getLatestLive(
                GetLatestLiveParam.builder().userId(targetUserId).build());
        if (liveResult.rCode() == LiveService.GET_LAST_LIVE_NOT_FOUND) {
            targetLive = null;
        } else if (RpcResult.isFail(liveResult)) {
            log.error("getLatestLive error, targetUserId={}, rCode={}", targetUserId, liveResult.rCode());
            return ResultVO.failure(ActivityMsgCodes.PK_LIVE_SEARCH_ERROR);
        } else {
            targetLive = liveResult.target().getLive();
        }

        // 未开播
        if (targetLive == null || LiveStatusEnum.ON_AIR.getValue() != targetLive.getStatus()) {
            vo.setStatus(false)
                    .setStatusDes("房间未开播，无法邀请")
                    .setName(user.getNickName())
                    .setIcon(user.getAvatar())
                    .setNumber(0);
            return ResultVO.success(CollUtil.newArrayList(vo));
        }

        // 在直播中则判断模式
        PpLiveMatchModelEnum curMatchMode = getLiveMatchMode(curLiveId);
        PpLiveMatchModelEnum targetMatchMode = getLiveMatchMode(targetLive.getId());
        if (curMatchMode == null || targetMatchMode == null) {
            return ResultVO.failure(ActivityMsgCodes.PK_LIVE_SEARCH_ERROR);
        }
        // 直播模式不同，不支持PK
        if (curMatchMode != targetMatchMode) {
            return ResultVO.success();
        }

        // 对方直播间pk权限校验
        Result<Boolean> result = livePkService.checkUserPkAuthority(0, targetLive.getId(), targetUserId);
        if (RpcResult.isFail(result)) {
            log.error("checkUserPkAuthority error, liveId={}, njId={}", targetLive.getId(), targetUserId);
            return ResultVO.failure(ActivityMsgCodes.PK_LIVE_SEARCH_ERROR);
        }
        // 没有pk权限
        if (!result.target()) {
            return ResultVO.success();
        }

        vo.setLiveId(targetLive.getId())
                .setStatus(true)
                .setName(targetLive.getName())
                .setIcon(targetLive.getImageUrl())
                .setNumber(getLiveTotalUser(targetLive.getId()));
        return ResultVO.success(CollUtil.newArrayList(vo));
    }

    @Override
    public ResultVO<List<PkFamilyRoomVo>> getPkFamilyRoom(long curLiveId) {
        Result<GetLiveResult> liveResult = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(curLiveId).build());
        if (RpcResult.isFail(liveResult)) {
            log.warn("getPkFamilyRoom liveResult is fail. curLiveId: {}", curLiveId);
            return ResultVO.failure(liveResult.rCode(), liveResult.getMessage());
        }

        Live curLive = liveResult.target().getLive();
        long curUserId = curLive.getUserId();
        PpLiveMatchModelEnum curMatchMode = getLiveMatchMode(curLiveId);

        // 获取厅主所在工会下的家族列表
        Result<List<Long>> sameSocietyFamilyRes = familyService.getSameSocietyFamily(curUserId);
        if (RpcResult.isFail(sameSocietyFamilyRes)) {
            log.warn("getPkFamilyRoom sameSocietyFamilyRes is fail. curLiveId: {}, curUserId: {}", curLiveId, curUserId);
            return ResultVO.failure(liveResult.rCode(), "服务异常");
        }
        // 没有家族，返回空列表
        if (CollectionUtils.isEmpty(sameSocietyFamilyRes.target())) {
            return ResultVO.success(Lists.newArrayList());
        }

        // 同工会下主播id集合
        Set<Long> njIds = Sets.newHashSet();
        Sets.newHashSet(sameSocietyFamilyRes.target()).forEach(familyId -> {
            // 获取家族下签约的主播列表
            Result<List<Long>> njIdsRes = familyService.getFamilySignNjIds(0L, familyId);
            if (RpcResult.isFail(njIdsRes)) {
                return;
            }
            njIds.addAll(njIdsRes.target().stream().filter(njId -> njId != curUserId).collect(Collectors.toList()));
        });

        // 收集所有符合条件的直播信息
        List<Live> targetLiveList = Collections.synchronizedList(Lists.newArrayList());
        // 每50个主播id分为一组，然后转换为CompletableFuture，异步执行
        CompletableFuture[] futures = Lists.partition(Lists.newArrayList(njIds), activityConfig.getPp().getPkNjPartitionCount())
                .stream().map(list -> CompletableFuture.runAsync(() -> {
                    // 批量查直播信息
                    Result<GetUsersLatestLiveResult> res = liveService.getUsersLatestLive(list);
                    if (RpcResult.isFail(res)) {
                        return;
                    }
                    targetLiveList.addAll(
                            res.target().getLives().stream()
                                    // 过滤未开播、模式不一致
                                    .filter(live -> LiveStatusEnum.ON_AIR.getValue() == live.getStatus() && getLiveMatchMode(live.getId()) == curMatchMode)
                                    .collect(Collectors.toList())
                    );
                }, executor)).toArray(CompletableFuture[]::new);
        // 等待所有CompletableFuture执行完毕
        CompletableFuture.allOf(futures).join();

        List<Long> targetLiveIdList = targetLiveList.stream().map(Live::getId).collect(Collectors.toList());
        // 直播间在线人数map
        Map<Long, Integer> onlineUserMap;
        // 批量获取直播间人气
        Result<List<LiveOnlineUser>> onlineUserResult = liveService.batchGetLiveTotalUser(targetLiveIdList);
        if (RpcResult.isFail(onlineUserResult)) {
            // 打印日志即可，热度失败不影响主流程数据
            log.error("getPkFamilyRoom onlineUserResult is fail. liveIds: {}", targetLiveIdList);
            onlineUserMap = Maps.newHashMap();
        } else {
            onlineUserMap = onlineUserResult.target().stream().collect(Collectors.toMap(LiveOnlineUser::getLiveId, LiveOnlineUser::getNum));
        }
        // 直播间根据人气降序并取前16个
        List<Live> resLiveList = targetLiveList.stream()
                .sorted(Comparator.comparingInt(live -> onlineUserMap.getOrDefault(((Live) live).getId(), 1)).reversed())
                .limit(16).collect(Collectors.toList());

        return ResultVO.success(resLiveList.stream().map(e -> {
            PkFamilyRoomVo vo = new PkFamilyRoomVo();
            vo.setLiveId(e.getId());
            vo.setNumber(MapUtil.getInt(onlineUserMap, e.getId()));
            vo.setIcon(e.getImageUrl());
            vo.setName(e.getName());
            vo.setStatus(true);
            return vo;
        }).collect(Collectors.toList()));
    }

    @Override
    public void fillInviteInfo(PkInfoVo pkInfoVo, long reqUserId, long liveId) {
        // 获取pk邀请信息
        Result<PkMatchManageProto.ResponsePkInviteInfo> result = pkMatchManageService.pkInviteInfo(liveId, reqUserId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("pp pkInviteInfo fail. liveId: {}, reqUserId: {}, rCode: {}", liveId, reqUserId, result.rCode());
            return;
        }
        PkMatchManageProto.ResponsePkInviteInfo inviteInfo = result.target();
        if (inviteInfo == null) {
            return;
        }
        // 邀请方信息
        if (inviteInfo.hasInviteSourceInfo()) {
            pkInfoVo.setPkInvitationData(livePkAdapter.convertPkInvitation(inviteInfo.getInviteSourceInfo(), liveId));
            // 被邀请方信息
        } else if (inviteInfo.hasInviteTargetInfo()) {
            pkInfoVo.setInviterList(livePkAdapter.convertPkInviterList(inviteInfo.getInviteTargetInfo()));
        }
    }

    /**
     * 获取pk模式
     *
     * @param liveId
     * @return
     */
    private PpLiveMatchModelEnum getLiveMatchMode(long liveId) {
        Result<GetLiveModeRemoteResult> result = iLiveServiceRemote.getLiveMode(
                GetLiveModeRemoteParam.builder().liveId(liveId).build());
        if (RpcResult.isFail(result)) {
            log.error("getLiveMatchMode error, liveId={}, rCode={}", liveId, result.rCode());
            return null;
        }
        return PpLiveMatchModelEnum.fromLiveMode(result.target().getBizLiveModeId());
    }


    private ResultVO<Void> checkRoomFeatureMutex(long liveId) {
        Result<MutexProto.ResponseRoomFeatureMutex> result = mutexService.roomFeatureMutex(MutexProto.FeatureMutex.newBuilder()
                .setLiveId(liveId)
                .setFeature(MutexProto.Feature.PK)
                .build());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("PpMutexService roomFeatureMutex fail,liveId={},feature=pk ,rCode={}", liveId, result.rCode());
            WaveAssert.rpcSuccess(result, ActivityMsgCodes.PK_OPERATION_FAIL);
        }

        if (result.target().getIsMutex()) {
            return ResultVO.failure(result.target().getMsg());
        }
        return ResultVO.success();
    }

    private ResultVO<Void> checkUserAuthority(long liveId, long njId, long userId) {
        PpMatchCourseProto.CheckUserLiveMatchAuthorityQuery.Builder checkAuthorityBuilder = PpMatchCourseProto.CheckUserLiveMatchAuthorityQuery.newBuilder();
        checkAuthorityBuilder.setLiveId(liveId);
        checkAuthorityBuilder.setNjId(njId);
        checkAuthorityBuilder.setUserId(userId);
        Result<PpMatchCourseProto.ResponseCheckUserLiveMatchAuthority> checkAuthorityRet = ppMatchCourseService.checkUserLiveMatchAuthority(checkAuthorityBuilder.build());
        if (checkAuthorityRet.rCode() != 0) {
            log.info("PpMatchCourseService checkUserLiveMatchAuthority error rCode:{},liveId:{},njId:{},userId:{}", checkAuthorityRet.rCode(), liveId, njId, userId);
            WaveAssert.rpcSuccess(checkAuthorityRet, ActivityMsgCodes.PK_OPERATION_FAIL);
        }
        if (!checkAuthorityRet.target().getLiveMatchAuthority()) {
            log.info("PpMatchCourseService checkUserLiveMatchAuthority liveMatchAuthority false liveId:{},njId:{},userId:{}", liveId, njId, userId);
            return ResultVO.failure(ActivityMsgCodes.PK_NO_IN_WHITE_MEMBER_FAIL);
        }
        return ResultVO.success();
    }


    private ResultVO<Pair<Long, PlatformRoleEnum>> pkBaseCheck(long liveId, long userId, int appId) {
        // 是否开播 这里调用了带缓存的live接口
        Result<GetLiveResult> getLiveResult = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        if (getLiveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(ActivityMsgCodes.PK_NOT_IN_LIVE);
        }
        GetLiveResult result = getLiveResult.target();
        if (result == null || result.getLive() == null) {
            return ResultVO.failure(ActivityMsgCodes.PK_NOT_IN_LIVE);
        }

        Live live = result.getLive();
        Long njId = live.getUserId();
        //是否关播
        if (live.getStatus() != LiveStatus.ON_AIR.getValue()) {
            return ResultVO.failure(ActivityMsgCodes.PK_LIVE_NO_ON_AIR_FAIL);
        }

       /* // pp的点唱厅不能开启pk玩法
        LiveModeEnum liveMode = LiveModeEnum.NORMAL_LIVE;
        Result<GetLiveModeResult> modelResult = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(liveId).build());
        if (RpcResult.isSuccess(modelResult)) {
            liveMode = modelResult.target().getLiveModeEnum();
        } else {
            log.warn("PpPkProcessor getLiveMode fail. rCode={}, liveId={}", modelResult.rCode(), liveId);
        }
        if(liveMode == LiveModeEnum.NORMAL_VOCAL_ROOM && !WaveLivePkOperateEnum.PK_DO_FINISH.equals(operateEnum)) {
            return ResultVO.failure(ActivityMsgCodes.PK_LIVE_MODEL_FAIL);
        }*/

        // 操作权限拦截
        PlatformRoleEnum userRole = null;
        for (PlatformRoleEnum role : pkOperatorRoles()) {
            if (role == PlatformRoleEnum.ROLE_OWNER && Objects.equals(userId, njId)) {
                userRole = role;
                break;
            }
            Boolean had = rolePermissionService.havePermission(PermissionCheckParam.builder()
                    .appId(appId)
                    .njId(njId)
                    .roomId(live.getLiveRoomId())
                    .platformRoleId(role.getRoleId())
                    .userId(userId)
                    .build());
            if (had) {
                userRole = role;
                break;
            }
        }
        if (userRole == null) {
            return ResultVO.failure(ActivityMsgCodes.PK_NO_AUTH);
        }
        return ResultVO.success(Pair.of(njId, userRole));
    }

    private Integer getLiveTotalUser(Long liveId) {
        Result<Integer> result = liveService.getLiveTotalUser(liveId);
        if (RpcResult.isFail(result)){
            log.warn("getLiveTotalUser is fail. liveId: {}", liveId);
            return 0;
        }

        return result.target();
    }
}
