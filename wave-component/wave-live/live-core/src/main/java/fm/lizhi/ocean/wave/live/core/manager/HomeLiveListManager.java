package fm.lizhi.ocean.wave.live.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.constants.HomeFreshTypeEnum;
import fm.lizhi.ocean.wave.live.core.constants.HomeTabTypeEnum;
import fm.lizhi.ocean.wave.live.core.constants.LiveMsgCodes;
import fm.lizhi.ocean.wave.live.core.extension.livelist.IHomeLiveListProcessor;
import fm.lizhi.ocean.wave.live.core.extension.recommend.RecommendProcessor;
import fm.lizhi.ocean.wave.live.core.extension.recommend.bean.GetTabNameParam;
import fm.lizhi.ocean.wave.live.core.manager.handler.HomeLiveHandlerFactory;
import fm.lizhi.ocean.wave.live.core.manager.handler.IHomeLiveHandler;
import fm.lizhi.ocean.wave.live.core.model.dto.HomeLiveDTO;
import fm.lizhi.ocean.wave.live.core.model.result.HomeLiveDataResult;
import fm.lizhi.ocean.wave.live.core.model.vo.HomeLiveVo;
import fm.lizhi.ocean.wave.live.core.model.vo.LiveRoomCardVo;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetHomeLiveListRequestParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLivePopularParam;
import fm.lizhi.ocean.wave.live.core.remote.result.*;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.IPopularityServiceRemote;
import fm.lizhi.ocean.wave.user.api.SingerService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.param.CheckSingerPermissionParam;
import fm.lizhi.ocean.wave.user.param.GetSimpleUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/30
 */
@Slf4j
@Component
public class HomeLiveListManager {

    @MyAutowired
    private ILiveServiceRemote iLiveServiceRemote;

    @MyAutowired
    private IPopularityServiceRemote iPopularityServiceRemote;

    @Autowired
    private UserService userService;

    @Autowired
    private ProcessorV2Factory processorV2Factory;

    @Autowired
    private HomeLiveHandlerFactory homeLiveHandlerFactory;

    @Autowired
    private SingerService singerService;

    public List<HomeLiveVo> getLiveList(long userId) {
        //获取首页直播列表
        Result<GetHomeLiveListResult> resp = iLiveServiceRemote.getHomeLiveRoomList(GetHomeLiveListRequestParam.builder()
                .userId(userId)
                .build());

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("HomeLiveListManager getHomeLiveRoomList fail, userId={}`rCode={}", userId, resp.rCode());
            return new ArrayList<>();
        }

        List<Long> officialRoomUserIds = getOfficialChannel();
        // 获取返回列表的房主id
        if (CollectionUtils.isEmpty(resp.target().getRoomUserIds()) && CollectionUtils.isEmpty(officialRoomUserIds)) {
            return new ArrayList<>();
        }

        List<Long> roomUserIds = resp.target().getRoomUserIds().stream().distinct().collect(Collectors.toList());
        roomUserIds.addAll(officialRoomUserIds);
        // 批量获取最近一次修改的直播间
        Result<BatchGetLastEditLivesByUserIdsResult> lastEditListResult = iLiveServiceRemote.batchGetLastEditLivesByUserIds(roomUserIds);
        if (lastEditListResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("HomeLiveListManager batchGetLastEditLivesByUserIds fail, userId={}`roomUserIds.size={}`rCode={}",
                    userId, roomUserIds.size(), resp.rCode());
            return new ArrayList<>();
        }

        List<LiveBean> liveBeanList = lastEditListResult.target().getLiveBeanList();
        List<HomeLiveVo> res = new ArrayList<>(liveBeanList.size());
        //开始拼接直播信息
        for (LiveBean liveBean : liveBeanList) {
            Result<GetSimpleUserResult> userResp = userService.getSimpleUser(GetSimpleUserParam.builder()
                    .userId(liveBean.getUserId())
                    .build());

            if (userResp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || Objects.isNull(userResp.target())) {
                continue;
            }
            //拼接用户信息
            SimpleUser simpleUser = userResp.target().getSimpleUser();
            HomeLiveVo.UserVo userVo = HomeLiveVo.UserVo.builder().userId(simpleUser.getUserId())
                    .avatar(simpleUser.getAvatar())
                    .nickName(simpleUser.getNickName())
                    .build();
            //拼接直播信息
            HomeLiveVo.LiveRoomVo liveRoomVo = HomeLiveVo.LiveRoomVo.builder().liveRoomId(liveBean.getLiveRoomId()).build();
            HomeLiveVo.LiveVo liveVo = HomeLiveVo.LiveVo.builder()
                    .liveId(liveBean.getId())
                    .liveName(liveBean.getName())
                    .cover(liveBean.getImageUrl())
                    .status(liveBean.getStatus())
                    // pp没有锁房
                    .isLock(StringUtils.isNotBlank(liveBean.getPassword()))
                    .popularity(getLivePopularity(liveBean.getUserId(), liveBean.getId()))
                    .build();

            res.add(HomeLiveVo.builder()
                    .liveRoom(liveRoomVo)
                    .live(liveVo)
                    .user(userVo)
                    .build());
        }
        return res;
    }

    /**
     * 获取推荐直播列表
     *
     * @param liveId 当前直播id
     * @return 推荐直播列表
     */
    public List<LiveRoomCardVo> getRecommendLiveList(long liveId) {
        RecommendProcessor processor = processorV2Factory.getProcessor(RecommendProcessor.class);
        // 获取推荐直播列表
        Result<GetRecommendLiveListResponse> recommendResult = iLiveServiceRemote.getRecommendLiveList(liveId);
        if (recommendResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getRecommendLiveList error, liveId={}`rCode`{}", liveId, recommendResult.rCode());
            return Collections.emptyList();
        }

        // 根据liveId获取直播信息
        List<Long> liveIds = recommendResult.target().getLiveList()
                .stream().map(GetRecommendLiveListResponse.RecommendLive::getLiveId).collect(Collectors.toList());
        List<GetRecommendLiveListResponse.RecommendLive> list = recommendResult.target().getLiveList();

        // 打乱
        Collections.shuffle(liveIds);
        Result<List<LiveBean>> resultLives = iLiveServiceRemote.getLives(liveIds);
        if (resultLives.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getRecommendLiveList getLives error, liveIds={}`rCode`{}", liveIds, resultLives.rCode());
            return Collections.emptyList();
        }

        // 批量获取到房主信息，拿到波段号
        List<Long> userIds = resultLives.target().stream().map(LiveBean::getUserId).collect(Collectors.toList());
        Result<BatchGetSimpleUserResult> userResult = userService.batchGetSimpleUserByCache(
                BatchGetUserParam.builder().userIdList(userIds).build());
        if (userResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getRecommendLiveList batchGetSimpleUser error, userIds.size={}`rCode`{}", userIds.size(), userResult.rCode());
            return Collections.emptyList();
        }
        List<SimpleUser> userList = userResult.target().getUserList();
        Map<Long, String> userIdMapBand = userList.stream().collect(Collectors.toMap(SimpleUser::getUserId, SimpleUser::getBand));

        //获取过滤后的分类
        Map<Long, String> tabNameMap = processor.batchGetTabName(userIds);
        // 组装返回结果
        List<LiveRoomCardVo> cardVos = new ArrayList<>();
        for (LiveBean liveBean : resultLives.target()) {
            String band = userIdMapBand.get(liveBean.getUserId());
            String tabName = processor.getTabName(GetTabNameParam.builder()
                    .recommendLiveList(list).tabNameMap(tabNameMap).livebean(liveBean).build());
            cardVos.add(LiveRoomCardVo.builder()
                    .njId(liveBean.getUserId())
                    .liveName(liveBean.getName())
                    .cover(liveBean.getImageUrl())
                    .onlineUserCount(getTotalUserCount(liveBean.getId()))
                    .liveId(liveBean.getId())
                    .tabName(tabName)
                    .band(band).build());
        }
        return cardVos;
    }

    /**
     * 查询首页记录
     *
     * @param userId        用户ID
     * @param tabType       tab类型
     * @param freshType     刷新类型
     * @param performanceId 分页信息
     * @return 结果
     */
    public ResultVO<HomeLiveDataResult> getHomeLiveList(long userId, int tabType, int freshType, String performanceId) {
        //参数校验
        if (userId <= 0 || !HomeTabTypeEnum.isExistType(tabType) || !HomeFreshTypeEnum.isExistType(freshType)) {
            return ResultVO.failure(LiveMsgCodes.GET_HOME_LIVE_LIST_PARAM.getCode(), LiveMsgCodes.GET_HOME_LIVE_LIST_PARAM.getMsg());
        }

        IHomeLiveListProcessor processor = processorV2Factory.getProcessor(IHomeLiveListProcessor.class);
        IHomeLiveHandler handler = homeLiveHandlerFactory.getHandler(tabType);
        //根据tab类型查询不同的直播间列表
        ResultVO<HomeLiveDTO> homeLiveListRes = handler.getHomeLiveList(userId, freshType, performanceId, processor.getCommonLiveConfig());
        if (!homeLiveListRes.isOK()) {
            return ResultVO.failure(homeLiveListRes.getRCode(), homeLiveListRes.getPrompt().getMsg());
        }

        //根据ID查询直播信息
        HomeLiveDTO homeLiveData = homeLiveListRes.getData();
        ResultVO<List<LiveBean>> liveInfoRes = getLiveInfoByIdList(homeLiveData, userId);
        if (!liveInfoRes.isOK()) {
            //不成功直接返回结果
            return ResultVO.failure(liveInfoRes.getRCode(), liveInfoRes.getPrompt().getMsg());
        }

        List<LiveBean> liveBeanList = liveInfoRes.getData();
        ResultVO<Map<Long, SimpleUser>> mapResultRes = batchGetUserInfo(liveBeanList);
        if (!mapResultRes.isOK()) {
            //不成功直接返回结果
            return ResultVO.failure(mapResultRes.getRCode(), mapResultRes.getPrompt().getMsg());
        }

        //删除当前用户的直播间
        liveBeanList = liveBeanList.stream().filter(liveBean -> liveBean.getUserId() != userId).collect(Collectors.toList());

        // 查询歌手认证申请权限
        Map<Long, Boolean> hasSingerAuditMap = getHasSingerAuditMap(userId, tabType, liveBeanList);;

        //构建结果数据
        List<HomeLiveVo> homeLiveVos = buildHomeLiveVO(liveBeanList, mapResultRes.getData(), hasSingerAuditMap);
        //对结果列表进行排序
        handler.sortLiveByPopularity(homeLiveVos);
        String performanceIdJson = homeLiveData.getPerformanceData() == null ? "" : JSONObject.toJSONString(homeLiveData.getPerformanceData());
        return ResultVO.success(
                HomeLiveDataResult.builder()
                        .isLastPage(homeLiveData.isLastPage())
                        .performanceId(performanceIdJson)
                        .emptyDataContent(homeLiveData.getEmptyDataContent())
                        .liveList(homeLiveVos)
                        .build());
    }

    /**
     * 查询歌手认证申请权限
     */
    private Map<Long, Boolean> getHasSingerAuditMap(long userId, int tabType, List<LiveBean> liveBeanList) {
        if (tabType == HomeTabTypeEnum.SIGN_LIVE.getType()) {
            // 是签约列表, 才查询，正常情况下，这个列表只有一条数据
            return liveBeanList.stream()
                    .collect(Collectors.toMap(
                            LiveBean::getId,
                            liveBean -> {
                                // 检查权限
                                Result<Boolean> result = singerService.checkSingerPermissionCache(
                                        new CheckSingerPermissionParam()
                                                .setLiveId(liveBean.getId())
                                                .setAppId(ContextUtils.getContext().getBusinessEvnEnum().getAppId())
                                                .setNjId(liveBean.getUserId())
                                                .setUserId(userId)
                                );

                                // 处理RPC调用失败
                                if (RpcResult.isFail(result)) {
                                    log.warn("get checkSingerPermission fail, userId={}, liveId={}", userId, liveBean.getId());
                                    return false;
                                }
                                return result.target();
                            }
                    ));
        }
        return new HashMap<>();
    }

    /**
     * 根据ID查询直播信息
     *
     * @param homeLiveDTO 查询结果
     * @return 直播信息
     */
    private ResultVO<List<LiveBean>> getLiveInfoByIdList(HomeLiveDTO homeLiveDTO, long userId) {
        List<LiveBean> liveBeanList = new ArrayList<>();
        List<Long> liveIds = homeLiveDTO.getLiveIdList();
        List<Long> roomUserIds = homeLiveDTO.getRoomUserIdList();
        //主播节目ID和主播ID，哪个不空用哪个查询
        if (CollectionUtils.isNotEmpty(roomUserIds)) {
            //去重
            roomUserIds = roomUserIds.stream().distinct().collect(Collectors.toList());
            // 批量获取最近一次修改的直播间
            Result<BatchGetLastEditLivesByUserIdsResult> lastEditListResult = iLiveServiceRemote.batchGetLastEditLivesByUserIds(roomUserIds);
            if (lastEditListResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("getHomeLiveList.batchGetLastEditLivesByUserIds fail, userId={}`roomUserIds.size={}`rCode={}",
                        userId, roomUserIds.size(), lastEditListResult.rCode());
                return ResultVO.failure(LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getCode(), LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getMsg());
            }
            liveBeanList = lastEditListResult.target().getLiveBeanList();
        } else if (CollectionUtils.isNotEmpty(liveIds)) {
            //去重
            liveIds = liveIds.stream().distinct().collect(Collectors.toList());
            // 批量获取直播信息
            Result<List<LiveBean>> liveBeanListResult = iLiveServiceRemote.getLives(liveIds);
            if (liveBeanListResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("getHomeLiveList.getLives fail, userId={}`liveIds.size={}`rCode={}",
                        userId, liveIds.size(), liveBeanListResult.rCode());
                return ResultVO.failure(LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getCode(), LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getMsg());
            }
            liveBeanList = liveBeanListResult.target();
        }
        return ResultVO.success(liveBeanList);
    }

    /**
     * 批量获取用户信息
     *
     * @param liveBeanList 直播间信息列表
     * @return 用户信息
     */
    private ResultVO<Map<Long, SimpleUser>> batchGetUserInfo(List<LiveBean> liveBeanList) {
        List<Long> roomUserIds = liveBeanList.stream().map(LiveBean::getUserId).collect(Collectors.toList());
        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(roomUserIds).build();
        Result<BatchGetSimpleUserResult> batchSimpleUserCacheRes = userService.batchGetSimpleUserByCache(userParam);
        if (batchSimpleUserCacheRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || batchSimpleUserCacheRes.target() == null) {
            log.warn("getHomeLiveList.batchGetSimpleUserByCache fail, userId={}`roomUserIds.size={}`rCode={}",
                    ContextUtils.getContext().getUserId(), roomUserIds.size(), batchSimpleUserCacheRes.rCode());
            return ResultVO.failure(LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getCode(), LiveMsgCodes.GET_HOME_LIVE_LIST_FAIL.getMsg());
        }

        Map<Long, SimpleUser> simpleUserMap = batchSimpleUserCacheRes.target().getUserList().stream()
                .collect(Collectors.toMap(SimpleUser::getUserId, b -> b));
        return ResultVO.success(simpleUserMap);
    }

    private int getLivePopularity(long njId, long liveId) {
        Result<GetLivePopularityResponse> resp = iPopularityServiceRemote.getPopularity(new GetLivePopularParam(njId, liveId));
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getPopularity error, liveId:{}, rCode:{}", liveId, resp.rCode());
            //虽然现在无rCode != 0的情况，但是为了防止以后有，所以这里返回0
            return 0;
        }
        return resp.target().getTotalUserCount();

    }

    /**
     * 从缓存中获取人气数据
     *
     * @param njId   主播ID
     * @param liveId 直播节目ID
     * @return 人气值
     */
    private int getLivePopularityByCache(long njId, long liveId) {
        Result<GetLivePopularityResponse> resp = iPopularityServiceRemote.getPopularityByLocalCache(new GetLivePopularParam(njId, liveId));
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getLivePopularityByCache error, liveId:{}, rCode:{}", liveId, resp.rCode());
            //虽然现在无rCode != 0的情况，但是为了防止以后有，所以这里返回0
            return 0;
        }
        return resp.target().getTotalUserCount();

    }

    private int getTotalUserCount(long liveId) {
        Result<GetLiveTotalUserResponse> result = iPopularityServiceRemote.getLiveTotalUser(liveId);
        if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return result.target().getTotalUserCount();
        }
        return 0;
    }

    /**
     * 获取官方频道信息
     *
     * @return 结果列表
     */
    private List<Long> getOfficialChannel() {
        IHomeLiveListProcessor processor = processorV2Factory.getProcessor(IHomeLiveListProcessor.class);
        CommonLiveConfig commonLiveConfig = processor.getCommonLiveConfig();
        List<Long> officialRoomNjIdList = commonLiveConfig.getOfficialRoomNjIdList();
        List<Long> roomUserIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(officialRoomNjIdList)) {
            return roomUserIds;
        }
        return officialRoomNjIdList;
    }

    /**
     * 构建直播首页列表记过
     *
     * @param liveList          直播列表
     * @param userMap           用户信息
     * @param hasSingerAuditMap
     * @return 结果
     */
    private List<HomeLiveVo> buildHomeLiveVO(List<LiveBean> liveList, Map<Long, SimpleUser> userMap, Map<Long, Boolean> hasSingerAuditMap) {
        List<HomeLiveVo> res = new ArrayList<>(liveList.size());
        //开始拼接直播信息
        for (LiveBean liveBean : liveList) {
            //拼接用户信息
            SimpleUser simpleUser = userMap.get(liveBean.getUserId());
            if (simpleUser == null) {
                log.warn("getHomeLiveList.getSimpleUser fail, userId={}", liveBean.getUserId());
                continue;
            }

            HomeLiveVo.UserVo userVo = HomeLiveVo.UserVo.builder().userId(simpleUser.getUserId())
                    .avatar(simpleUser.getAvatar())
                    .nickName(simpleUser.getNickName())
                    .build();
            //拼接直播信息
            HomeLiveVo.LiveRoomVo liveRoomVo = HomeLiveVo.LiveRoomVo.builder().liveRoomId(liveBean.getLiveRoomId()).build();
            HomeLiveVo.LiveVo liveVo = HomeLiveVo.LiveVo.builder()
                    .liveId(liveBean.getId())
                    .liveName(liveBean.getName())
                    .cover(liveBean.getImageUrl())
                    .status(liveBean.getStatus())
                    // pp没有锁房
                    .isLock(StringUtils.isNotBlank(liveBean.getPassword()))
                    .popularity(getLivePopularityByCache(liveBean.getUserId(), liveBean.getId()))
                    .build();

            Boolean hasSingerAudit = false;
            if (CollUtil.isNotEmpty(hasSingerAuditMap)){
                hasSingerAudit = MapUtil.get(hasSingerAuditMap, liveBean.getId(), Boolean.class, false);
            }

            res.add(HomeLiveVo.builder()
                    .hasSingerAudit(hasSingerAudit)
                    .liveRoom(liveRoomVo)
                    .live(liveVo)
                    .user(userVo)
                    .build());
        }


        return res;
    }
}
