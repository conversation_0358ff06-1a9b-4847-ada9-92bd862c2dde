package fm.lizhi.ocean.wave.comment.core.extension.comment.bean;

import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发送拍一拍评论前置处理器参数bean
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendPatCommentBean {
    /**
     * 发起拍一拍的 用户信息
     */
    private SimpleUser userInfo;


    /**
     * 被拍一拍的 用户信息
     */
    private SimpleUser targetUserInfo;


    private Long njId;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论id
     */
    private Long commentId;
}
