package fm.lizhi.ocean.wave.permission.core.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 权限表
 *
 * @date 2023-05-22 06:11:22
 */
@Table(name = "`wave_permission`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WavePermission {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 权限英文
     */
    @Column(name= "`permission`")
    private String permission;

    /**
     * 权限名称
     */
    @Column(name= "`permission_name`")
    private String permissionName;

    /**
     * 权限类型 1 操作权限 2 页面模块权限
     */
    @Column(name= "`permission_type`")
    private Integer permissionType;

    /**
     * 权限创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 权限修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", permission=").append(permission);
        sb.append(", permissionName=").append(permissionName);
        sb.append(", permissionType=").append(permissionType);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}