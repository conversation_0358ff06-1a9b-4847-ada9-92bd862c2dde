package fm.lizhi.ocean.wave.live.core.remote.service.xm;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.xm.dto.req.GetRoomUserAndVipUserListReq;
import fm.lizhi.live.room.xm.dto.resp.GetRoomUserAndVipUserListResp;
import fm.lizhi.live.room.xm.services.LiveRoomSpringService;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.remote.param.RoomOnlineUserListParam;
import fm.lizhi.ocean.wave.live.core.remote.result.RoomUserListResult;
import fm.lizhi.ocean.wave.live.core.remote.result.RoomUserResult;
import fm.lizhi.ocean.wave.live.core.remote.service.IRoomUserServiceRemote;
import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class XmRoomUserServiceRemote extends RemoteServiceInvokeFacade implements IRoomUserServiceRemote {


    @Autowired
    private LiveRoomSpringService liveRoomSpringService;


    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }


    @Override
    public Result<RoomUserListResult> getRoomVipUserList(RoomOnlineUserListParam param) {
        long njId = param.getNjId();
        long userId = param.getUserId();
        GetRoomUserAndVipUserListResp roomUserAndVipUserList = getRoomUserAndVipUserList(njId, userId, param.getPerformanceId());
        List<fm.lizhi.live.room.xm.dto.liveroom.roomuser.RoomUserDto> roomVipUserList = roomUserAndVipUserList.getRoomVipUserList();

        String performanceId = roomUserAndVipUserList.getPerformanceId();
        List<RoomUserResult> roomUserResults = roomVipUserList.stream().map(u -> RoomUserResult.builder()
                .userId(u.getUserId())
                .userName(u.getUserName())
                .avatar(u.getAvatar())
                .roomVipUrls(u.getRoomVipTagUrl())
                .status(statusAdapter(u.getStatus()).getValue())
                .build()).collect(Collectors.toList());

        boolean lastPage = roomUserAndVipUserList.isLastPage();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, RoomUserListResult.builder()
                .isLastPage(lastPage)
                .roomUserList(roomUserResults)
                .performanceId(performanceId)
                .build());
    }


    /**
     * 获取房间的普通在线列表和vip坐席列表
     *
     * @param njId
     * @param userId
     * @param performanceId
     * @return
     */
    public GetRoomUserAndVipUserListResp getRoomUserAndVipUserList(long njId, long userId, String performanceId) {
        GetRoomUserAndVipUserListReq request = new GetRoomUserAndVipUserListReq();
        request.setNjId(njId);
        request.setCurrUserId(userId);
        request.setPerformanceId(performanceId);
        Result<GetRoomUserAndVipUserListResp> result = liveRoomSpringService.getRoomUserAndVipUserList(request);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("xm getRoomUserAndVipUserList fail rcode={}", result.rCode());
            GetRoomUserAndVipUserListResp response = new GetRoomUserAndVipUserListResp();
            response.setRoomVipUserList(Lists.newArrayList());
            response.setRoomNormalUserList(Lists.newArrayList());
            response.setLastPage(true);
            return response;
        }
        return result.target();
    }


    @Override
    public Result<RoomUserListResult> getRoomUserList(RoomOnlineUserListParam param) {
        long njId = param.getNjId();
        long userId = param.getUserId();
        GetRoomUserAndVipUserListResp roomUserAndVipUserList = getRoomUserAndVipUserList(njId, userId, param.getPerformanceId());
        List<fm.lizhi.live.room.xm.dto.liveroom.roomuser.RoomUserDto> roomVipUserList = roomUserAndVipUserList.getRoomNormalUserList();

        String performanceId = roomUserAndVipUserList.getPerformanceId();
        List<RoomUserResult> roomUserResults = roomVipUserList.stream().map(u -> RoomUserResult.builder()
                .userId(u.getUserId())
                .userName(u.getUserName())
                .avatar(u.getAvatar())
                .status(statusAdapter(u.getStatus()).getValue())
                .build()).collect(Collectors.toList());

        boolean lastPage = roomUserAndVipUserList.isLastPage();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, RoomUserListResult.builder()
                .isLastPage(lastPage)
                .roomUserList(roomUserResults)
                .performanceId(performanceId)
                .build());
    }

    /**
     * 状态适配
     *
     * @param status 业务状态
     * @return 平台状态
     */
    private OnlineStatusEnum statusAdapter(int status) {
        // 业务侧状态，1:离线,2:在房间,3:直播中,4:在麦上
        switch (status) {
            case 0:// UserStatusEnum.ONLINE.getValue()
                return OnlineStatusEnum.ONLINE;
            case 1://UserStatusEnum.OFFLINE.getValue()
                return OnlineStatusEnum.OFFLINE;
            case 2://UserStatusEnum.LISTEN.getValue()
                return OnlineStatusEnum.LISTEN;
            case 3://UserStatusEnum.OPEN.getValue()
                return OnlineStatusEnum.LIVING;
            case 4://UserStatusEnum.SEAT.getValue()
                return OnlineStatusEnum.ON_MIC;
            default:
                return OnlineStatusEnum.LISTEN;
        }
    }
}
