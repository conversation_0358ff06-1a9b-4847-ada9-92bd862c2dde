package fm.lizhi.ocean.wave.amusement.core.api.impl;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.amusement.core.dao.redis.AmusementRedisDao;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementInfoManager;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementHeartbeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AmusementHeartbeatServiceImpl implements AmusementHeartbeatService {

    @Autowired
    private AmusementInfoManager amusementInfoManager;

    @Autowired
    private AmusementRedisDao amusementRedisDao;


    @Override
    public Result<Void> asyncReportOnMicData(Long liveId, long userId) {

        if (null == liveId || liveId <= 0) {
            log.warn("asyncReportOnMicData, liveId is null, userId:{}", userId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        // 验证用户是否在麦位上
        try {
            boolean isUserOnMic = amusementRedisDao.checkUserInMicByLive(liveId, userId);
            if (!isUserOnMic) {
                log.info("用户不在麦位上，跳过心跳上报, liveId:{}, userId:{}", liveId, userId);
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            }
            amusementInfoManager.asyncReportOnMicData(liveId, userId);
        } catch (Exception e) {
            log.error("检查用户麦位状态失败, liveId:{}, userId:{}, error:{}", liveId, userId, e.getMessage(), e);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }
}
