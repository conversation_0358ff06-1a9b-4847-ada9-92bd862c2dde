package fm.lizhi.ocean.wave.user.core.remote.service.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.manager.RedisClientManager;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.core.config.PpUserProviderConfig;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.constant.DressUpTypeMapping;
import fm.lizhi.ocean.wave.user.core.dao.redis.UserRedisKey;
import fm.lizhi.ocean.wave.user.core.remote.param.GetUserDressUpUsingRequest;
import fm.lizhi.ocean.wave.user.core.remote.param.OperateDressUpRequest;
import fm.lizhi.ocean.wave.user.core.remote.result.*;
import fm.lizhi.ocean.wave.user.core.remote.service.UserDressServiceRemote;
import fm.lizhi.pp.mall.api.BubbleMallService;
import fm.lizhi.pp.mall.protocol.BubbleMallProto;
import fm.lizhi.pp.oss.api.CommentStyleManagerService;
import fm.lizhi.pp.oss.protocol.CommentStyleProto;
import fm.lizhi.pp.vip.api.AvatarWidgetService;
import fm.lizhi.pp.vip.api.DecorateUserService;
import fm.lizhi.pp.vip.bean.DecoratePreviewBo;
import fm.lizhi.pp.vip.bean.req.UseDecorateStockReq;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;
import fm.lizhi.pp.vip.constant.UserDecorateStatusEnum;
import fm.lizhi.pp.vip.protocol.AvatarWidgetProto;
import fm.lizhi.pp.vip.protocol.DecorateUserProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class PpUserDressServiceRemote extends RemoteServiceInvokeFacade implements UserDressServiceRemote {


    @Autowired
    private AvatarWidgetService avatarWidgetService;

    @Autowired
    private CommentStyleManagerService commentStyleManagerService;

    @Autowired
    private DecorateUserService decorateUserService;

    @Autowired
    private BubbleMallService bubbleMallService;

    @Autowired
    private RedisClientManager redisClientManager;


    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private UserProviderConfig config;

    public static final String EXPIRE_CN_POSTFIX = "后过期";


    /**
     * 因为业务缓存问题，暂时所有请求都返回 成功状态码
     *
     * @param req
     * @return
     */
    @Override
    public Result<Void> operateDressUp(OperateDressUpRequest req) {
        long userId = req.getUserId();
        Integer type = req.getType();
        Integer op = req.getOp();
        Long decorateId = req.getDecorateId();

        //PP的气泡和头像框使用的依然是旧的装扮系统，所以需要调用旧的接口
        if (DecorateTypeEnum.BUBBLE.getType().equals(type)) {
            if (op == 1) {
                // 佩戴
                return useBubble(req);
            }
            return cancelUseBubble(req);
        }

        if (op == 0) {
            Result<DecorateUserProto.ResponseCancelUseDecorate> cancelResult = decorateUserService.cancelUseDecorate(userId, decorateId, type);
            return RpcResult.success();
        }

        if (DecorateTypeEnum.AVATAR_WIDGET.getType().equals(type)) {
            return operateAvatar(req);
        }

        UseDecorateStockReq useReq = new UseDecorateStockReq();
        useReq.setOwnerId(userId);
        useReq.setDecorateId(decorateId);
        useReq.setType(type);
        Result<Void> result = decorateUserService.useDecorateStock(JsonUtil.dumps(useReq));

        if (result.rCode() != 0) {
            log.error("pp operateDressUp error, rCode={}`userId={}`decorateId={}`type={}",
                    result.rCode(), userId, decorateId, type);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    private Result<Void> useBubble(OperateDressUpRequest req) {
        long userId = req.getUserId();
        Long decorateId = req.getDecorateId();
        Result<BubbleMallProto.ResponseUseBubble> result = bubbleMallService.useBubble(userId, decorateId);
        if (RpcResult.isFail(result)) {
            log.warn("useBubble req={} rCode={}", JSONObject.toJSONString(req), result.rCode());
            return RpcResult.success();
        }
        return RpcResult.success();
    }

    private Result<Void> cancelUseBubble(OperateDressUpRequest req) {
        long userId = req.getUserId();
        Result<BubbleMallProto.ResponseCancelUseBubble> result = bubbleMallService.cancelUseBubble(userId);
        if (RpcResult.isFail(result)) {
            log.warn("cancelUseBubble req={} rCode={}", JSONObject.toJSONString(req), result.rCode());
            return RpcResult.success();
        }
        return RpcResult.success();
    }


    private Result<Void> operateAvatar(OperateDressUpRequest req) {
        long userId = req.getUserId();
        Long decorateId = req.getDecorateId();
        Result<AvatarWidgetProto.ResponseUseWidget> result = avatarWidgetService.useWidget(userId, decorateId);
        if (RpcResult.isFail(result)) {
            log.warn("operateAvatar req={} rCode={}", JSONObject.toJSONString(req), result.rCode());
            return RpcResult.success();
        }
        return RpcResult.success();
    }

    @Override
    public Result<List<DressUpInfoResponse>> getUserDressUpListByType(long userId, Integer type) {
        // 获取用户所有装扮
        Result<DecorateUserProto.ResponseGetUserDecorateListByType> result =
                decorateUserService.getUserDecorateListByType(userId, type);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp getUserDressUpListByType error rCode={} userId={},type={}", result.rCode(), userId, type);
            return new Result<>(GET_USER_DRESS_UP_LIST_ERROR, null);
        }

        List<DressUpInfoResponse> responses = new ArrayList<>();

        String decoratePreviewListStr = result.target().getDecoratePreviewListStr();
        List<DecoratePreviewBo> decoratePreviewBos = JSONObject.parseArray(decoratePreviewListStr, DecoratePreviewBo.class);
        for (DecoratePreviewBo bo : decoratePreviewBos) {
            long remainTime = bo.getEndTime() - System.currentTimeMillis();
            DressUpInfoResponse.DressUpInfoResponseBuilder builder = DressUpInfoResponse.builder()
                    .id(bo.getDecorateId())
                    .name(bo.getName())
                    .remainTime(remainTime)
                    .cover(bo.getPreviewUrl());
            builder.useStatus(bo.getStatus() == UserDecorateStatusEnum.USING.getStatus() ? 1 : 0);
            builder.remainTimeDesc(getUserTreasureDesc(System.currentTimeMillis(), bo.getEndTime()));
            if (bo.getStatus() == UserDecorateStatusEnum.USING.getStatus() || bo.getStatus() == UserDecorateStatusEnum.UNUSED.getStatus()) {
                if (bo.getEndTime() < System.currentTimeMillis()) {
                    builder.status(-1);
                    builder.showButton(false);
                } else {
                    builder.showButton(true);
                    builder.status(0);
                }
            } else if (bo.getStatus() == UserDecorateStatusEnum.EXPIRED.getStatus()) {
                //已过期
                builder.showButton(false);
                builder.status(-1);
            }
            responses.add(builder.build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, responses);
    }

    public String getUserTreasureDesc(long now, long expireTime) {
        //已过期
        if (expireTime < now) {
            return "已过期";
        }
        //计算期限
        long restSec = (expireTime - now) / 1000L;
        StringBuilder bdr = new StringBuilder();
        if (restSec >= 86400) {
            long restDay = restSec / 86400;
            int dressUpPermanentTime = config.getPp().getDressUpPermanentTime();
            if (restDay >= dressUpPermanentTime) {
                // 永久
                return "永久";
            }
            bdr.append(restDay);
            bdr.append("天");
            restSec %= 86400;
        }
        if (restSec >= 3600) {
            bdr.append(restSec / 3600);
            bdr.append("小时");
            restSec %= 3600;
        }
        if (restSec >= 60) {
            bdr.append(restSec / 60);
            bdr.append("分钟");
            restSec %= 60;
        }

        if (bdr.length() == 0) {
            bdr.append("1分钟");
        }
        bdr.append(EXPIRE_CN_POSTFIX);
        return bdr.toString();
    }

    @Override
    public Result<GetUserTailEffectResponse> getUserTailEffect(long userId) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }


    @Override
    public Result<List<DressUpTabResponse>> getUserDressTab(long userId, long targetId) {
        List<DressUpTabResponse> responses = Arrays.asList(
                buildDressUpTabResponse(DecorateTypeEnum.AVATAR_WIDGET),
                buildDressUpTabResponse(DecorateTypeEnum.BUBBLE),
                buildDressUpTabResponse(DecorateTypeEnum.MOUNT)
        );
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, responses);
    }

    private DressUpTabResponse buildDressUpTabResponse(DecorateTypeEnum decorateTypeEnum) {
        return DressUpTabResponse.builder()
                .dressName(decorateTypeEnum.getName())
                .type(decorateTypeEnum.getType())
                .build();
    }

    /**
     * 默认的头像框
     */
    private GetAvatarWidgetResponse defaultAvatarWidget = GetAvatarWidgetResponse.builder().id(-1).build();

    @Override
    public Result<GetAvatarWidgetResponse> getUserAvatarWidget(GetUserDressUpUsingRequest req) {
        long uid = req.getUserId();
        Integer type = DressUpTypeMapping.getBizType(BusinessEvnEnum.PP, req.getType());
        if (type == null) {
            log.warn("pp getUserDressUsing error bizType is null, uid={}, type={}", uid, req.getType());
            return new Result<>(GET_DRESS_UP_PARAM_FAIL, null);
        }
        PpUserProviderConfig configPp = config.getPp();
        // 假设 userId 和 type 已经定义
        final String key = UserRedisKey.AVATAR.getKey(BusinessEvnEnum.PP.getName(), uid, type);
        String avatar = redisClientManager.redisCacheClient().get(key);
        GetAvatarWidgetResponse response = null;
        if (StringUtils.isNotEmpty(avatar)) {
            response = JSONObject.parseObject(avatar, GetAvatarWidgetResponse.class);
            if (response.getId() == defaultAvatarWidget.getId()) {
                // 如果是默认的 直接返回 null
                return new Result<>(GET_USER_DRESS_USING_NOT_FOUND_ERROR, null);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
        }

        Result<AvatarWidgetProto.ResponseGetUsersWidget> result = avatarWidgetService.getUsersWidget(Arrays.asList(uid), 0, 1);
        if (result.rCode() != 0) {
            log.warn("pp getUserDressUsing RCode={},uid={},typeId={}", result.rCode(), uid, type);
            return new Result<>(GET_USER_DRESS_USING_ERROR, null);
        }
        List<AvatarWidgetProto.UserWidget> userWidgetList = result.target().getUserWidgetsList();
        for (AvatarWidgetProto.UserWidget userWidget : userWidgetList) {
            if (userWidget.getUserId() == uid && userWidget.getExpireDuration() > 0) {
                response = GetAvatarWidgetResponse.builder()
                        .id(userWidget.getWidget().getId())
                        .thumbUrl(userWidget.getWidget().getThumbUrl())
                        .materialUrl(userWidget.getWidget().getMaterialUrl())
                        .svgaMaterialUrl(userWidget.getWidget().getMaterialSvgaUrl()).build();
                break;
            }
        }
        if (response == null) {
            // 防止缓存穿透
            redisClientManager.redisCacheClient().setex(key, configPp.getDressUpCacheTime(), JSONObject.toJSONString(defaultAvatarWidget));
            return new Result<>(GET_USER_DRESS_USING_NOT_FOUND_ERROR, null);
        }

        redisClientManager.redisCacheClient().setex(key, configPp.getDressUpCacheTime(), JSONObject.toJSONString(response));

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }


    /**
     * 默认的气泡
     */
    private GetUserBubbleResponse defaultBubble = GetUserBubbleResponse.builder().id(-1).build();

    @Override
    public Result<GetUserBubbleResponse> getUserBubbleUsing(GetUserDressUpUsingRequest req) {
        long userId = req.getUserId();

        PpUserProviderConfig configPp = config.getPp();
        // 假设 userId 和 type 已经定义
        final String key = UserRedisKey.BUBBLE.getKey(BusinessEvnEnum.PP.getName(), userId, req.getType());
        String bubbleStr = redisClientManager.redisCacheClient().get(key);
        GetUserBubbleResponse response = null;
        if (StringUtils.isNotEmpty(bubbleStr)) {
            response = JSONObject.parseObject(bubbleStr, GetUserBubbleResponse.class);
            if (response.getId() == defaultBubble.getId()) {
                // 如果是默认的 直接返回 null
                return new Result<>(GET_USER_DRESS_USING_NOT_FOUND_ERROR, null);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
        }

        Result<BubbleMallProto.ResponseGetUserBubbles> result = bubbleMallService.getUserBubbles(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("pp getUserBubbles error userId={},rCode={}", userId, result.rCode());
            return new Result<>(GET_USER_DRESS_USING_ERROR, null);
        }
        List<BubbleMallProto.MyBubble> bubblesList = result.target().getBubblesList();
        for (BubbleMallProto.MyBubble myBubble : bubblesList) {
            if (myBubble.getUsing()) {
                String previewImage = myBubble.getPreviewImage();
                String cdn = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost();
                previewImage = UrlUtils.addCdnHost(cdn, previewImage);

                GetUserBubbleResponse.GetUserBubbleResponseBuilder builder = GetUserBubbleResponse.builder()
                        .id(myBubble.getId())
                        .imageUrl(previewImage);

                //fix 获取字体颜色
                String fontColor = getFontColor(myBubble.getId());
                if (StringUtils.isNotBlank(fontColor)) {
                    builder.fontColor(fontColor);
                }

                response = builder.build();
                break;
            }
        }

        if (response == null) {
            // 防止缓存穿透
            redisClientManager.redisCacheClient().setex(key, configPp.getDressUpCacheTime(), JSONObject.toJSONString(defaultBubble));
            return new Result<>(GET_USER_DRESS_USING_NOT_FOUND_ERROR, null);
        }

        redisClientManager.redisCacheClient().setex(key, configPp.getDressUpCacheTime(), JSONObject.toJSONString(response));
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.PP.equals(evnEnum);
    }

    /**
     * 获取字体颜色
     *
     * @param styleId
     * @return
     */
    private String getFontColor(long styleId) {
        //先从缓存获取, 下游没有缓存
        String key = UserRedisKey.BUBBLE_FONT.getKey(BusinessEvnEnum.PP.getName(), styleId);
        String fontColor = redisClientManager.redisCacheClient().get(key);
        if (fontColor != null) {
            return fontColor;
        }

        Result<CommentStyleProto.ResponseCommentStyle> styleResult = commentStyleManagerService.commentStyle(styleId);
        if (RpcResult.isSuccess(styleResult)) {
            fontColor = styleResult.target().getCommentStyle().getIosColor();
        } else {
            fontColor = "";
        }

        redisClientManager.redisCacheClient().setex(key, config.getPp().getBubbleFontCacheTime(), fontColor);
        return fontColor;
    }

    @Override
    public Result<Map<Long, GetAvatarWidgetResponse>> batchGetUserAvatarWidget(List<Long> userIds, Integer type) {
        Result<AvatarWidgetProto.ResponseGetUsersWidget> result = avatarWidgetService.getUsersWidget(userIds, 0, 1);
        if (result.rCode() != 0) {
            log.warn("pp getUserDressUsing RCode={},uid={},typeId={}", result.rCode(), userIds, type);
            return new Result<>(GET_USER_DRESS_USING_ERROR, null);
        }

        Map<Long, GetAvatarWidgetResponse> map = new HashMap<>(userIds.size());
        List<AvatarWidgetProto.UserWidget> userWidgetList = result.target().getUserWidgetsList();
        for (AvatarWidgetProto.UserWidget userWidget : userWidgetList) {
            if (userWidget.getExpireDuration() > 0) {
                GetAvatarWidgetResponse response = GetAvatarWidgetResponse.builder()
                        .id(userWidget.getWidget().getId())
                        .thumbUrl(userWidget.getWidget().getThumbUrl())
                        .materialUrl(userWidget.getWidget().getMaterialUrl())
                        .userId(userWidget.getUserId())
                        .svgaMaterialUrl(userWidget.getWidget().getMaterialSvgaUrl()).build();
                map.put(userWidget.getUserId(), response);
            }
        }

        for (Long userId : userIds) {
            if (!map.containsKey(userId)) {
                map.put(userId, GetAvatarWidgetResponse.builder().id(-1).userId(userId).build());
            }
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, map);
    }


}
