package fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.generic;

import com.google.common.collect.Maps;
import com.google.protobuf.*;
import com.googlecode.protobuf.format.JsonFormat;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.dubbo.generic.GenericInvokeParams;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.generic.serialization.ProtoClassInfo;
import fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.generic.serialization.WaveProtoReqSerializer;
import fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.generic.serialization.WaveProtoRespSerializer;
import fm.lizhi.ocean.wave.common.exception.GenericInvocationException;
import fm.lizhi.ocean.wave.common.manager.RpcInvocationMonitor;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.common.util.LoggerUtil;
import fm.lizhi.ocean.wave.common.util.ProtobufUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 基于domain和op的泛化调用类, 参考: <a href="https://lizhi2021.feishu.cn/wiki/wikcnNo88TwIz3GqHyFMpH7fhpX">泛化调用</a>.
 */
@Slf4j
public class LzDomainOpInvoker implements LzGenericInvoker {

    @Override
    public boolean supports(LzInvokingContext context, LzInvokingParam param) {
        return param.getMethod().isAnnotationPresent(Service.class);
    }

    @Override
    public Object invoke(LzInvokingContext context, LzInvokingParam param) {
        Logger logger = LoggerFactory.getLogger(context.getServiceInterface().getName() + "." + param.getMethod().getName());
        StopWatch stopWatch = StopWatch.createStarted();
        String actualServiceName = context.findActualServiceName();
        GenericInvokeParams genericInvokeParams = buildInvokeParams(context, param, actualServiceName);
        Result<Map<String, Object>> genericResult;
        int rCode;
        Map<String, Object> responseMap;
        byte[] rawData;
        String attachment;
        Message protoTarget;
        try {
            genericResult = context.getGenericService().invoke(genericInvokeParams);
            rCode = genericResult.rCode();
            responseMap = genericResult.target();
            rawData = genericResult.rawData();
            attachment = genericResult.getAttachment();
            protoTarget = buildProtoTarget(context, param, responseMap, actualServiceName);
        } catch (Throwable e) {
            String paramJson = JsonUtils.toJsonString(genericInvokeParams);
            log.warn("failRpcInvoke.lzDomainOp;actualServiceName: {};genericInvokeParams: {}", context.getServiceInterface().getCanonicalName(), paramJson);
            RpcInvocationMonitor.rpcReqFailMonitor(genericInvokeParams.getToBusinessEnv(),
                    context.getServiceInterface().getCanonicalName(), param.getMethod().getName(), "lzDomainOp");
            throw new GenericInvocationException(paramJson, e);
        }

        stopWatch.stop();
        long userId = ContextUtils.getContext().getUserId();
        //打印日志
        LoggerUtil.printRpcLog(logger,
                "finishRpcInvoke.lzDomainOp"
                , userId
                , stopWatch.getTime(TimeUnit.MILLISECONDS)
                , genericInvokeParams.getToBusinessEnv()
                , context.getServiceInterface().getCanonicalName()
                , param.getMethod()
                , rCode
                , param.getArgs()
                , responseMap
        );

        Result<Message> protoResult = new Result<>(rCode, protoTarget, rawData);
        protoResult.setAttachment(attachment);
        return protoResult;
    }

    /**
     * 构建泛化调用参数
     *
     * @param context 上下文
     * @param param   参数
     * @return 泛化调用参数
     */
    private GenericInvokeParams buildInvokeParams(LzInvokingContext context, LzInvokingParam param, String actualServiceName) {
        Method method = param.getMethod();
        Service serviceAnnotation = method.getAnnotation(Service.class);
        BusinessEvnEnum businessEvnEnum = param.getBusinessEvnEnum();
        Map<String, Object> requestMap;
        if (context.getRelocation()) {
            requestMap = buildRequestMapWithoutProtoClass(param);
        } else {
            requestMap = buildRequestMap(param);
        }
        GenericInvokeParams genericInvokeParams = new GenericInvokeParams();
        genericInvokeParams.setDomain(serviceAnnotation.domain());
        genericInvokeParams.setOp(serviceAnnotation.op());
        genericInvokeParams.setServiceName(actualServiceName);
        genericInvokeParams.setMethodName(method.getName());
        genericInvokeParams.setToRegion(businessEvnEnum.getRegion());
        genericInvokeParams.setToBusinessEnv(businessEvnEnum.getBusinessEnv());
        genericInvokeParams.setRequest(requestMap);
        return genericInvokeParams;
    }

    /**
     * 构建泛化调用请求Map对象. 该实现偷懒使用了JSON字符串做中间转换, 如果protobuf对象中有Map对象则会处理失败, 因为protobuf-java-format
     * 库会将Map转换为[{"key":"", "value":""}]的格式, 这不是常规的JSON转换规则.
     * <p><br>
     * 后续完善处理, 应当借助protobuf描述对象递归构建请求Map对象.
     *
     * @param param 泛化调用参数
     * @return 泛化调用请求Map对象
     */
    private Map<String, Object> buildRequestMap(LzInvokingParam param) {
        Method method = param.getMethod();
        Object[] args = param.getArgs();
        Class<?> requestClass = method.getAnnotation(Service.class).request();
        Descriptors.Descriptor descriptor = ProtobufUtils.getDescriptor(requestClass);
        GeneratedMessage.Builder<?> builder = ProtobufUtils.newBuilder(requestClass);
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Object arg = args[i];
            if (arg != null) {
                String name = parameter.getAnnotation(Attribute.class).name();
                Descriptors.FieldDescriptor fieldDescriptor = descriptor.findFieldByName(name);
                if (arg.getClass().equals(byte[].class)) {
                    ByteString byteString = ByteString.copyFrom((byte[]) arg);
                    builder.setField(fieldDescriptor, byteString);
                } else {
                    builder.setField(fieldDescriptor, arg);
                }
            }
        }
        Message message = builder.build();
        String jsonString = JsonFormat.printToString(message);
        @SuppressWarnings("unchecked")
        Map<String, Object> requestMap = JsonUtils.fromJsonString(jsonString, Map.class);
        return requestMap;
    }


    /**
     * 借助protobuf类信息反射递归构建请求Map对象
     *
     * @param param
     * @return
     */
    private Map<String, Object> buildRequestMapWithoutProtoClass(LzInvokingParam param) {
        Map<String, Object> requestMap = Maps.newHashMap();
        Method method = param.getMethod();
        Object[] args = param.getArgs();
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Attribute annotation = parameter.getAnnotation(Attribute.class);
            String name = annotation.name();
            Class<?> type = parameter.getType();
            Class<?> genericType = annotation.genericType() == Object.class ? null : annotation.genericType();
            ProtoClassInfo protoReqClassInfo = ProtoClassInfo.valueOf(type, genericType);
            Optional<Object> o = WaveProtoReqSerializer.buildParamValue(protoReqClassInfo, args[i]);
            o.ifPresent(obj -> requestMap.put(name, obj));
        }
        return requestMap;
    }

    /**
     * 构建泛化调用响应protobuf对象. 同上方法, 该实现偷懒使用了JSON字符串做中间转换, 如果protobuf对象中有Map对象则会处理失败.
     *
     * @param context     上下文
     * @param param       参数
     * @param responseMap 响应Map对象
     * @return 泛化调用响应protobuf对象
     */
    private Message buildProtoTarget(LzInvokingContext context, LzInvokingParam param, Map<String, Object> responseMap, String actualServiceName) {
        if (context.getRelocation()) {
            return buildProtoTargetWithReflect(context, param, responseMap, actualServiceName);
        }

        String methodName = param.getMethod().getName();
        Class<?> responseClass = param.getMethod().getAnnotation(Service.class).response();
        if (responseMap == null || Objects.equals(responseClass, MessageLite.class)) {
            // 当接口返回值为Result<Void>时, responseClass为MessageLite.class
            return null;
        }
        String jsonString = JsonUtils.toJsonString(responseMap);
        GeneratedMessage.Builder<?> builder = ProtobufUtils.newBuilder(responseClass);
        try {
            JsonFormat.merge(jsonString, builder);
            return builder.build();
        } catch (JsonFormat.ParseException | RuntimeException e) {
            throw new IllegalStateException(String.format("泛化调用结果Map转Protobuf对象异常, " +
                    "service: %s, method: %s", actualServiceName, methodName), e);
        }
    }

    /**
     * 不经过proto的descriptor信息，根据Map对象，借助protobuf类信息递归构建请求
     */
    private Message buildProtoTargetWithReflect(LzInvokingContext context, LzInvokingParam param, Map<String, Object> responseMap, String actualServiceName) {
        String methodName = param.getMethod().getName();
        Class<?> responseClass = param.getMethod().getAnnotation(Service.class).response();
        if (responseMap == null || Objects.equals(responseClass, MessageLite.class)) {
            // 当接口返回值为Result<Void>时, responseClass为MessageLite.class
            return null;
        }
        try {
            ProtoClassInfo classInfo = ProtoClassInfo.valueOf(responseClass);
            Optional<Object> o = WaveProtoRespSerializer.buildObjectValue(classInfo, responseMap);
            return (Message) o.orElse(null);
        } catch (RuntimeException e) {
            throw new IllegalStateException(String.format("泛化调用结果Map转Protobuf对象异常, " +
                    "service: %s, method: %s", actualServiceName, methodName), e);
        }
    }

}
