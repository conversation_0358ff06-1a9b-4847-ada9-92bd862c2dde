package fm.lizhi.ocean.wave.activity.core.extension.order;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.activity.core.constant.ActivityMsgCodes;
import fm.lizhi.ocean.wave.activity.core.constant.OperateTypeMappingEnum;
import fm.lizhi.ocean.wave.activity.core.model.param.OrderCreateVoiceParam;
import fm.lizhi.ocean.wave.activity.core.model.param.OrderOperateParam;
import fm.lizhi.ocean.wave.activity.core.model.result.OrderCreateVoiceResult;
import fm.lizhi.ocean.wave.activity.core.remote.param.OrderOperateRequest;
import fm.lizhi.ocean.wave.activity.core.remote.result.OrderOperateResponse;
import fm.lizhi.ocean.wave.activity.core.remote.service.OrderServiceRemote;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementVoiceCallService;
import fm.lizhi.ocean.wave.api.amusement.bean.AmusementVoiceCallInfo;
import fm.lizhi.ocean.wave.api.amusement.constants.WaveVoiceCallOperate;
import fm.lizhi.ocean.wave.api.amusement.param.OperateVoiceCallParam;
import fm.lizhi.ocean.wave.api.amusement.result.GetCallTargetResult;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HyOrderOperateProcessor implements IOrderOperateProcessor {

    @MyAutowired
    private OrderServiceRemote orderServiceRemote;
    @Autowired
    private AmusementVoiceCallService amusementVoiceCallService;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean hasPermissionOperate(OrderOperateParam param) {
        return true;
    }

    @Override
    public ResultVO<Void> createVoicePreCheck(OrderCreateVoiceParam param) {
        //检查发起人是否正在通话中
        Result<GetCallTargetResult> result = amusementVoiceCallService.getCallTarget(ContextUtils.getContext().getUserId());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(ActivityMsgCodes.USER_CALL_STATUS_FAIL);
        }

        boolean calling = result.target().isCalling();
        return calling ? ResultVO.failure(ActivityMsgCodes.CREATE_VOICE_EXIST) : ResultVO.success();
    }

    @Override
    public ResultVO<OrderCreateVoiceResult> createVoice(OrderCreateVoiceParam param) {

        OrderOperateRequest orderOpParam = new OrderOperateRequest();
        orderOpParam.setOrderId(param.getOrderId());
        orderOpParam.setUserId(param.getUserId());
        orderOpParam.setOperateType(OperateTypeMappingEnum.REQUEST_SERVICE.getWaveType());
        orderOpParam.setNjId(ContextUtils.getContext().getUserId());

        //请求开始订单 获取通话ID
        Result<OrderOperateResponse> orderOpRes = orderServiceRemote.orderOperate(orderOpParam);
        if (RpcResult.isFail(orderOpRes)) {
            return ResultVO.failure(ActivityMsgCodes.ORDER_OPERATE_FAIL);
        }
        OrderOperateResponse orderOp = orderOpRes.target();
        if (!orderOp.isSuccess()) {
            return ResultVO.failure(ActivityMsgCodes.ORDER_OPERATE_FAIL.getCode(), orderOp.getErrorMsg());
        }
        Long callId = orderOp.getCallId();

        //发起通话
        OperateVoiceCallParam.OperateVoiceCallParamBuilder builder = OperateVoiceCallParam.builder()
                .operate(WaveVoiceCallOperate.INVITE.getValue())
                .calleeId(param.getUserId())
                .callId(callId)
                .userId(ContextUtils.getContext().getUserId());
        Result<AmusementVoiceCallInfo> callResult = amusementVoiceCallService.operateVoiceCall(builder.build());
        if (RpcResult.isFail(callResult)) {
            return ResultVO.failure(ActivityMsgCodes.CREATE_VOICE_FAIL);
        }
        AmusementVoiceCallInfo callInfo = callResult.target();

        return ResultVO.success(new OrderCreateVoiceResult()
                .setChannelId(callInfo.getChannelId())
                .setAppKey(callInfo.getAppKey())
                .setCallId(callId)
                .setCallerVoiceId(callInfo.getCallerVoiceId())
                .setCalleeVoiceId(callInfo.getCalleeVoiceId())
        );
    }

}
