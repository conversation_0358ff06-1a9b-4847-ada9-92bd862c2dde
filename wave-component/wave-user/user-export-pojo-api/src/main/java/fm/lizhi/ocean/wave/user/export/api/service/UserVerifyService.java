package fm.lizhi.ocean.wave.user.export.api.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.user.export.api.param.GetUserVerifyConfigParam;
import fm.lizhi.ocean.wave.user.export.api.param.GetUserVerifyCountParam;
import fm.lizhi.ocean.wave.user.export.api.param.GetUserVerifyRecordManyParam;
import fm.lizhi.ocean.wave.user.export.api.param.SaveUserVerifyRecordManyParam;
import fm.lizhi.ocean.wave.user.export.api.result.GetUserVerifyConfigResult;
import fm.lizhi.ocean.wave.user.export.api.result.GetUserVerifyCountResult;
import fm.lizhi.ocean.wave.user.export.api.result.GetUserVerifyRecordManyResult;
import fm.lizhi.ocean.wave.user.export.api.result.SaveUserVerifyRecordManyResult;

public interface UserVerifyService {

    /**
     * 获取用户认证次数
     * @param param 参数
     * @return 结果
     */
    Result<GetUserVerifyCountResult> getUserVerifyCount(GetUserVerifyCountParam param);

    /**
     * 保存用户多次认证记录
     * @param param 参数
     * @return 结果
     */
    Result<SaveUserVerifyRecordManyResult> saveUserVerifyRecordMany(SaveUserVerifyRecordManyParam param);

    /**
     * 获取用户认证记录
     * @param param 参数
     * @return 结果
     */
    Result<GetUserVerifyRecordManyResult> getUserVerifyRecordMany(GetUserVerifyRecordManyParam param);

    /**
     * 获取用户认证配置
     * @param param 参数
     * @return 结果
     */
    Result<GetUserVerifyConfigResult> getUserVerifyConfig(GetUserVerifyConfigParam param);
}
