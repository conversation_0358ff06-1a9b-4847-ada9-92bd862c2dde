package fm.lizhi.ocean.wave.permission.core.manager.beancondition.pp;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.user.api.SingerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 歌曲录制condition
 */
@Component
@Slf4j
public class AudioRecordCondition extends AbstractBeanCondition {
    @Autowired
    private SingerService singerService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        // 是否为认证歌手
        Result<Boolean> result = singerService.userIsSinger(context.getRequest().getAppId(), context.getUser().getUserId());
        if (RpcResult.isFail(result)) {
            return false;
        }
        return result.target();
    }

    @Override
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        Long userId = context.getUser().getUserId();
        if (userId == null) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(String.valueOf(userId));
    }
}
