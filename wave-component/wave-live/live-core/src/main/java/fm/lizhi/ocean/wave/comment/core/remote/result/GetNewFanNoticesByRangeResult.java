package fm.lizhi.ocean.wave.comment.core.remote.result;

import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import lombok.Data;

import java.util.List;

/**
 * 获取新粉丝进房公告结果
 * <AUTHOR>
 */
@Data
public class GetNewFanNoticesByRangeResult {
    /**
     * 直播ID
     */
    private Long liveId;
    /**
     * 开始时间戳
     */
    private Long startTimeStamp;
    /**
     * 结束时间戳
     */
    private Long endTimeStamp;
    /**
     * 进房公告信息列表
     */
    private List<EnterNoticeEntry> enterNotices;
}
