package fm.lizhi.ocean.wave.generic.domainop.api.xm;

import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.generic.domainop.WaveDomainOpGenericService;
import fm.lizhi.ocean.wave.generic.domainop.protocol.RelocationProto;

/**
 * <AUTHOR>
 * @date 2024/3/14 08:55
 */
public interface CommentWaveService extends WaveDomainOpGenericService {
    @Override
    @Service(domain = 20019, op = 20, request = RelocationProto.InvokerRequest.class, response = RelocationProto.ResultPb.class)
    @Return(resultType = RelocationProto.ResultPb.class)
    Result<RelocationProto.ResultPb> invoke(@Attribute(name = "param") RelocationProto.ParamPb param);
}
