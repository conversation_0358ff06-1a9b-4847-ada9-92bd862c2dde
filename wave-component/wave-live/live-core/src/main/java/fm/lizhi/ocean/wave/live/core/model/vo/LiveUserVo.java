package fm.lizhi.ocean.wave.live.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveUserVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private long userId;

    private String nickName;

    private String avatar;

    /**
     * 用户直播间角色1：房主2：管路员
     */
    private List<Integer> roles;

    private Boolean isBanComment;

    private Boolean isKickOut;

}
