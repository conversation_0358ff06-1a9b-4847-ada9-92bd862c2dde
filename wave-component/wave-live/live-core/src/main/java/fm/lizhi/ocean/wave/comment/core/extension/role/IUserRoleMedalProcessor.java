package fm.lizhi.ocean.wave.comment.core.extension.role;

import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;

import java.util.List;

/**
 * 房间角色权限差异化处理器
 */
public interface IUserRoleMedalProcessor extends BusinessEnvAwareProcessor {

    /**
     * 过滤不需要的房间角色
     */
    List<LiveRoomUserRole> filterLiveRoomRoles(List<LiveRoomUserRole> liveRoomRoles);


    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IUserRoleMedalProcessor.class;
    }
}