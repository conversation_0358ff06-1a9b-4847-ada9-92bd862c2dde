package fm.lizhi.ocean.wave.activity.core.model.param;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class PkInfoParam {

    /**
     * 直播id
     */
    @NotNull
    @Range(min = 1, message = "参数错误")
    private Long liveId;


    /**
     * pkMatchId
     */

    private Long matchId;


    /**
     * njId
     */
    @NotNull
    @Range(min = 1, message = "参数错误")
    private Long njId;
}
