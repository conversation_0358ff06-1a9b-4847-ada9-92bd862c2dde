package fm.lizhi.ocean.wave.live.core.extension.roomUser.biz.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.live.core.extension.roomUser.IRoomUserProcessor;
import fm.lizhi.ocean.wave.live.core.remote.param.RoomUserListParam;
import fm.lizhi.ocean.wave.live.core.remote.result.RoomSeatUserListResult;
import fm.lizhi.ocean.wave.live.core.remote.result.RoomSeatUserResult;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class XmRoomUserProcessor implements IRoomUserProcessor {

    @Override
    public RoomSeatUserListResult getRoomUserCommonUserList(RoomUserListParam param) {
        return null;
    }

    @Override
    public RoomSeatUserListResult getRoomUserVipUserList(RoomUserListParam param) {
        return null;
    }


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
