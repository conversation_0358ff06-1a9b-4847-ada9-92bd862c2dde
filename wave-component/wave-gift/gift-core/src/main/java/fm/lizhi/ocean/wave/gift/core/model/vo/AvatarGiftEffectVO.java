package fm.lizhi.ocean.wave.gift.core.model.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 形象礼物特效VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AvatarGiftEffectVO {

    /**
     * 虚拟形象特效详细配置数据，json格式，样例：
     * {
     *      "actionJson": "{\"action\":{\"duration\":0,\"name\":\"ZEGOQ_Man_Action_Gift_0001\"}}",
     *      "actionConfig": Object{...},
     *      "avatarImageUrl": "https://cdnoffice.lizhi.fm/user/2024/05/27/3078577782138444802.png",
     *      "decorateJson": "{\"modelID\":\"qhuman.bundle\",\"modelPath\":\"AssetBundles/bundle/qbody/Boy.prefab\",\"package\":{\"pants\":\"ZEGOQ_Share_Pants_0002_0003\",\"irises\":\"ZEGOQ_Share_Eye_0001_Basecolor\",\"eyelash\":\"ZEGOQ_Man_Eyelash_0001_0003_BaseColor\",\"browshape\":\"ZEGOQ_Browshape_01_Man\",\"shoes\":\"ZEGOQ_Share_Shoes_0002_0001\",\"hair\":\"ZEGOQ_Share_Hair_0007\",\"shirt\":\"ZEGOQ_Share_Tshirt_0002\",\"eyeshape\":\"ZEGOQ_EyeShape_TaoHua_Man\",\"noseshape\":\"ZEGOQ_Nosehape_Straight_Man\"},\"property\":{\"bodyshape_chest_Zsize\":0.59,\"bodyshape_head_size\":0.12,\"bodyshape_big_leg_Ysize\":0.52,\"faceshape_lip_lower_size_x\":0.59,\"bodyshape_biceps_size\":0.685,\"skin_color\":\"255,252,252,252\",\"bodyshape_arm_Xsize\":0.525,\"faceshape_eye_size\":0.255,\"bodyshape_buttock_size\":0.45,\"hair_end_color\":\"255,248,203,203\",\"faceshape_mouth_all_y\":0.745,\"bodyshape_neck_XZsize\":0.255,\"faceshape_lipcorner_y\":0.22,\"lip_color_v2\":\"255,250,143,136\",\"faceshape_jaw_y\":0.62,\"faceshape_brow_size_y\":0.65,\"faceshape_jaw_all_size_x\":0.515,\"bodyshape_neck_Ysize\":0.355,\"faceshape_brow_size_x\":0.42,\"faceshape_brow_all_y\":0.22,\"faceshape_cheekbone_z\":0.1,\"faceshape_nose_all_roll_y\":0.68,\"bodyshape_chest_Xsize\":0.61,\"bodyshape_foot_size\":0.565,\"faceshape_cheek_all_size_x\":0.31,\"faceshape_cheekbone_x\":0.37,\"bodyshape_shoulder_Zsize\":0.55,\"faceshape_lip_upper_size_x\":0.585,\"faceshape_brow_all_roll_z\":0.495,\"bodyshape_big_arm_Zsize\":0.535,\"faceshape_eye_roll_y\":0.675,\"faceshape_eye_roll_z\":0.435,\"bodyshape_waist_Ysize\":0.56,\"bodyshape_lower_arm_Zsize\":0.53,\"faceshape_nostril_roll_y\":0.355,\"bodyshape_leg_Ysize\":0.66,\"bodyshape_arm_Zsize\":0.565,\"bodyshape_lower_leg_Ysize\":0.675,\"bodyshape_waist_Xsize\":0.56,\"bodyshape_big_arm_Xsize\":0.56,\"bodyshape_lower_leg_XZsize\":0.505,\"faceshape_nose_all_x\":0.445,\"bodyshape_leg_XZsize\":0.56,\"faceshape_eye_x\":0.34,\"faceshape_lip_all_size_y\":0.59,\"faceshape_nose_all_y\":0.505,\"hair_root_color\":\"255,248,203,203\",\"bodyshape_shoulder_Xsize\":0.37}}"
     * }
     */
    private JSONObject avatarData;

    /**
     * 平台虚拟形象特效类型，see {@link fm.lizhi.ocean.wave.gift.core.constants.effect.AvatarEffectTypeEnum}
     */
    private String avatarType;

    /**
     * 特效播放配置
     */
    private ConfigVO config;

    /**
     * 特效位置配置
     */
    private FrameVO frame;

    /**
     * 送礼人形象动态资源
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FrameVO {
        /**
         * 宽度
         */
        private int width;

        /**
         * x坐标
         */
        private int x;

        /**
         * y坐标
         */
        private int y;

        /**
         * 高度
         */
        private int height;

        /**
         * 理想UI尺寸
         */
        private ShowViewSize showViewSize;

    }

    @Data
    @Builder
    public static class ConfigVO {
        /**
         * 自定义展示时间
         */
        public boolean customShowTime;
        /**
         * 时间配置
         */
        public TimeConfigVO timeConfig;
    }

    @Data
    @Builder
    public static class TimeConfigVO {
        /**
         * 开始时间
         */
        @JsonSerialize(using = ToStringSerializer.class)
        public long startTime;
        /**
         * 结束时间
         */
        @JsonSerialize(using = ToStringSerializer.class)
        public long endTime;
    }

    /**
     * 设计理想的UI尺寸
     */
    @Data
    @Builder
    public static class ShowViewSize {

        public int width;

        public int height;
    }


}
