package fm.lizhi.ocean.wave.comment.core.model.dto;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.remote.bean.CommentBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评论类型DTO
 *
 * <AUTHOR>
 * @date 2023/7/28 5:08 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentTypeDTO {

    /**
     * 是否是新用户
     */
    private Boolean isFreshUser;

    /**
     * 评论bean
     */
    private CommentBean commentBean;

    /**
     * 公共配置
     */
    private CommentCommonConfig commentConfig;

    /**
     * 发送评论状态码
     */
    private int sendCommentCode;
}
