package fm.lizhi.ocean.wave.live.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.live.core.model.result.RoleManageAndSuperListResult;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.constants.LiveMsgCodes;
import fm.lizhi.ocean.wave.live.core.manager.LiveRoleManager;
import fm.lizhi.ocean.wave.live.core.model.param.HostRoleManagerParam;
import fm.lizhi.ocean.wave.live.core.model.param.QueryRoleManagerListParam;
import fm.lizhi.ocean.wave.live.core.model.param.RoleManagerParam;
import fm.lizhi.ocean.wave.live.core.model.result.RoleManageListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
public class LiveRoleController {


    @Autowired
    private LiveRoleManager liveRoleManager;

    /**
     * 管理员列表
     *
     * @param param
     * @return
     */
    @GetMapping("/live/userList/admins")
    @VerifyUserToken
    public ResultVO<RoleManageAndSuperListResult> queryManagerList(QueryRoleManagerListParam param) {
        if (Objects.isNull(param.getLiveId())) {
            return ResultVO.failure((int) GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, "缺少必要参数");
        }
        return liveRoleManager.queryManagerOrSuperManagerList(param, PlatformRoleEnum.ROLE_MANAGER);
    }


    /**
     * 超级管理员列表（ximi独有）
     *
     * @param param
     * @return
     */
    @GetMapping("/live/userList/superAdmins")
    @VerifyUserToken
    public ResultVO<RoleManageAndSuperListResult> querySuperManagerList(QueryRoleManagerListParam param) {
        if (Objects.isNull(param.getLiveId())) {
            return ResultVO.failure((int) GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, "缺少必要参数");
        }
        return liveRoleManager.queryManagerOrSuperManagerList(param, PlatformRoleEnum.ROLE_SUPER_MANAGER);
    }


    /**
     * 操作超级管理员
     *
     * @param param
     * @return
     */
    @PostMapping("/live/userOperate/superAdmin")
    @VerifyUserToken
    public ResultVO<Void> operateSuperManager(@RequestBody @Validated RoleManagerParam param) {
        if (Objects.isNull(param.getLiveId())
                || (StringUtils.isBlank(param.getBand()) && Objects.isNull(param.getUserId()))
                || Objects.isNull(param.getType())) {
            //  code
            return ResultVO.failure((int) GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, "缺少必要参数");
        }
        long operateUid = ContextUtils.getContext().getUserId();
        param.setOperateUserId(operateUid);
        return liveRoleManager.operateSuperManager(param);
    }

    /**
     * 操作管理员
     *
     * @param param
     * @return
     */
    @PostMapping("/live/userOperate/admin")
    @VerifyUserToken
    public ResultVO<Void> operateManager(@RequestBody @Validated RoleManagerParam param) {
        if (Objects.isNull(param.getLiveId())
                || (StringUtils.isBlank(param.getBand()) && Objects.isNull(param.getUserId()))
                || Objects.isNull(param.getType())) {
            //  code
            return ResultVO.failure((int) GeneralRCode.GENERAL_RCODE_SERVICE_NOT_FOUND, "缺少必要参数");
        }
        long operateUid = ContextUtils.getContext().getUserId();
        param.setOperateUserId(operateUid);
        return liveRoleManager.operateManager(param);
    }

    /**
     * 操作管理主持人
     *
     * @param param
     * @return
     */
    @PostMapping("/live/userOperate/host")
    @VerifyUserToken
    public ResultVO<Void> operateManagerHost(@RequestBody @Validated HostRoleManagerParam param) {
        //参数校验
        if (param.getType() <= 0
                || (StringUtils.isBlank(param.getBand()) && Objects.isNull(param.getUserId()))
                || (param.getType() != HostRoleManagerParam.OperationType.OPERATION_SET
                && param.getType() != HostRoleManagerParam.OperationType.OPERATION_CANCEL)) {
            return ResultVO.failure(LiveMsgCodes.OPERATE_HOST_PARAM_ERROR.getCode(), LiveMsgCodes.OPERATE_HOST_PARAM_ERROR.getMsg());
        }

        if(param.getLiveId()<=0){
            return ResultVO.failure("请升级最新版本开播助手！");
        }

        return liveRoleManager.operateHost(param);
    }

    /**
     * 管理员列表
     *
     * @param param
     * @return
     */
    @GetMapping("/live/userList/host")
    @VerifyUserToken
    public ResultVO<RoleManageListResult> queryHostList(QueryRoleManagerListParam param) {
        if (Objects.isNull(param.getLiveId()) || param.getPageSize() <= 0 || param.getPageNum() <= 0) {
            return ResultVO.failure(LiveMsgCodes.GET_ROOM_HOST_PARAM_ERROR.getCode(), LiveMsgCodes.GET_ROOM_HOST_PARAM_ERROR.getMsg());
        }
        return liveRoleManager.queryHostList(param);
    }
}
