package fm.lizhi.ocean.wave.amusement.core.remote.param;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: 创建通话入参
 * @author: guoyi<PERSON>
 * @create: 2024/03/01 15:51
 */
@Data
@Accessors(chain = true)
public class RequestCreateVoiceCall {

    /**
     * 主叫ID
     */
    private Long callerId;

    /**
     * 被叫ID
     */
    private Long calleeId;

    /**
     * 通话类型 ,当前仅有1V1场景
     * @see fm.lizhi.ocean.wave.api.amusement.constants.WaveVoiceCallType
     */
    private Integer type;

    /**
     * 匹配ID 或者 订单ID
     */
    private Long bizId;
}
