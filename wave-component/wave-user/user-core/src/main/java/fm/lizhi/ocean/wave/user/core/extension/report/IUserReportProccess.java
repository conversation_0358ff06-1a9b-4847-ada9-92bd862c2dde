package fm.lizhi.ocean.wave.user.core.extension.report;

import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.core.remote.param.UserReportRequest;

public interface IUserReportProccess  extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor(){
        return IUserReportProccess.class;
    }


    /**
     * 参数检测
     * @param request
     * @return
     */
    ResultVO<Void> checkParam(UserReportRequest request);

    /**
     * 参数错误
     */
    int PARAM_ERROR = 1;
}
