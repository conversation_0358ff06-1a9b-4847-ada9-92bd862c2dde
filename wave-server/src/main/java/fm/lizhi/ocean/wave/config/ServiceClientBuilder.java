package fm.lizhi.ocean.wave.config;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.commons.rome.push.legacy.api.LegacyMessagePushService;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rpc服务消费者配置
 */
@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.hy.chat.api.PpChatService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.pp.social.api.chat.PpChatService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.xm.social.chat.api.PpChatService.class),
}
)
public class ServiceClientBuilder {
    @Bean
    public GuidGenerator guidGenerator() {
        return new GuidGenerator();
    }


    @Bean(name = "hyMessagePushService")
    @ConditionalOnMissingBean
    public LegacyMessagePushService hyMessagePushService() {
        return (LegacyMessagePushService) (new DubboClientBuilder(LegacyMessagePushService.class)).timeoutInMillis(5000).build();
    }
}
