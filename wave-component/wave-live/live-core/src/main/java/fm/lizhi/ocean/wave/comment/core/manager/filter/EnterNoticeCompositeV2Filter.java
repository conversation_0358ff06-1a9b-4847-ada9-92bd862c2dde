package fm.lizhi.ocean.wave.comment.core.manager.filter;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 进场通知过滤器组合器
 * <AUTHOR>
 */
public class EnterNoticeCompositeV2Filter implements EnterNoticeV2Filter {

    /**
     * 评论过滤器列表
     */
    private final List<EnterNoticeV2Filter> filters;

    public EnterNoticeCompositeV2Filter() {
        this.filters = new ArrayList<>();
    }

    /**
     * 添加过滤器
     * @param filter 过滤器
     */
    public void addFilter(EnterNoticeV2Filter filter) {
        filters.add(filter);
    }

    /**
     * 过滤
     * @param enterNoticeEntries 进场消息列表
     * @return 过滤后的进场消息列表
     */
    @Override
    public List<EnterNoticeDTO> filter(List<EnterNoticeDTO> enterNoticeEntries) {
        // 如果没有添加任何过滤器，直接返回所有评论
        if (filters.isEmpty()) {
            return enterNoticeEntries;
        }
        // 否则，只保留至少有一个过滤器允许通过的评论
        return enterNoticeEntries.stream()
                .filter(c -> filters.stream().anyMatch(filter -> filter.filter(Collections.singletonList(c)).size() > 0))
                .collect(Collectors.toList());
    }
}
