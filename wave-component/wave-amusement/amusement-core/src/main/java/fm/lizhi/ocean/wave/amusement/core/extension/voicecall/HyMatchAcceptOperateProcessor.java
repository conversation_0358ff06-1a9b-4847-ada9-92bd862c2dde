package fm.lizhi.ocean.wave.amusement.core.extension.voicecall;

import fm.lizhi.ocean.wave.amusement.core.config.AmusementCommonConfig;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HyMatchAcceptOperateProcessor implements IMatchAcceptOperateProcessor {

    @Autowired
    private AmusementConfig amusementConfig;

    @Override
    public AmusementCommonConfig getCommonConfig() {
        return amusementConfig.getHy();
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
