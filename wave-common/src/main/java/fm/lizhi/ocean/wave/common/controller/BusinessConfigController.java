package fm.lizhi.ocean.wave.common.controller;

import fm.lizhi.amusement.commons.websecurity.annotations.WebSecurityRuleConfig;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.manager.FeedbackManager;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.model.param.FeedbackParam;
import fm.lizhi.ocean.wave.common.vo.BusinessConfigVO;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/17
 */
@Slf4j
@RestController
@RequestMapping("/business/common")
public class BusinessConfigController {
    @Autowired
    private CommonProviderConfig config;

    @Autowired
    private FeedbackManager feedbackManager;

    /**
     * 意见反馈
     *
     * @param feedbackParam
     * @return
     */
    @PostMapping("/feedback")
    @VerifyUserToken(required = false)
    public ResultVO<Void> feedback(@Validated @RequestBody FeedbackParam feedbackParam) {
        return feedbackManager.feedback(feedbackParam);
    }

    /**
     * 获取业务线配置
     *
     * @return 结果
     */
    @GetMapping("/app/configs")
    @WebSecurityRuleConfig(exclude = true)
    public ResultVO<List<BusinessConfigVO>> listBusinessConfig() {
        List<BusinessConfigVO> businessConfigVos = new ArrayList<>();
        List<BusinessConfig> businessConfigs = config.listBusinessConfig();
        for (BusinessConfig businessConfig : businessConfigs) {
            BusinessConfigVO businessConfigVO = new BusinessConfigVO();
            businessConfigVO.setAppId(businessConfig.getAppId());
            businessConfigVO.setAppName(businessConfig.getAppName());
            businessConfigVO.setAppCode(businessConfig.getAppCode());
            businessConfigVO.setAppIcon(businessConfig.getIconUrl());
            businessConfigVO.setCdnUrl(businessConfig.getCdnHost());
            businessConfigVO.setRomaDomain(businessConfig.getRomaDomain());
            // 处理一个特殊字符，不然前端会报错
            businessConfigVO.setReportUrl(businessConfig.getReportUrl().replaceAll("\\u200B", ""));
            businessConfigVO.setPrivacyName(businessConfig.getPrivacyName());
            businessConfigVO.setPrivacyUrl(businessConfig.getPrivacyUrl());
            businessConfigVO.setUserAgreementName(businessConfig.getUserAgreementName());
            businessConfigVO.setUserAgreementUrl(businessConfig.getUserAgreementUrl());
            businessConfigVO.setOperateGuideUrl(businessConfig.getOperateGuideUrl());
            businessConfigVO.setPendantUrl(businessConfig.getPendantUrl());
            businessConfigVO.setWaveCenterUrl(businessConfig.getWaveCenterUrl());
            businessConfigVO.setActivityAction(businessConfig.getActivityAction());
            businessConfigVO.setAppSignPageUrl(businessConfig.getAppSignPageUrl());
            businessConfigVos.add(businessConfigVO);
        }

        return ResultVO.success(businessConfigVos);
    }

    /**
     * 获取的是业务的配置
     *
     * @return
     */
    @GetMapping("/config")
    @WebSecurityRuleConfig(exclude = true)
    public ResultVO<Map<String, Object>> getBusinessConfig() {
        String bizCommonConfig = config.getBizCommonConfig();
        Map<String, Object> res = (Map<String, Object>) JsonUtil.loads(bizCommonConfig, Map.class);
        return ResultVO.success(res);
    }
}
