package fm.lizhi.ocean.wave.comment.core.remote.bean;

import lombok.Data;

/**
 * 进房公告参数
 *
 * <AUTHOR>
 */
@Data
public class EnterNoticesParam {
    /**
     * 结束时间
     */
    private long noticeEndTime;
    /**
     * 开始时间
     */
    private long startTime;
    /**
     * 是否主播
     */
    private Boolean isNj;
    /**
     * 是否过滤
     */
    private Boolean isFilter;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 应用id
     */
    private Integer appId;
    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 评论缓存开关
     */
    private boolean commentCacheSwitch;

    private Long njId;
}
