package fm.lizhi.ocean.wave.live.core.extension.livepolling.biz.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.ILiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.extension.heartbeat.xm.XmLiveHeartbeatProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.LivePollingProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.bean.LivePollingPostBean;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.xm.decorate.call.api.CallService;
import fm.lizhi.xm.decorate.call.protocol.LiveCallProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@Slf4j
@Component
public class XmLivePollingProcessor implements LivePollingProcessor {

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private CallService callService;

    @Autowired
    private XmLiveHeartbeatProcessor factory;

    @Autowired
    private CommonProviderConfig commonProviderConfig;


    @Override
    public ResultVO<Void> postprocessor(LivePollingPostBean param) {

        if (isOldVersion()) {
            factory.reportLiveHeartbeat(param);
        }

        return ResultVO.success();
    }

    /**
     * 是否是旧版本
     */
    private boolean isOldVersion() {
        try {
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            return clientVersion < commonProviderConfig.getMinHeartbeatClientVersion();
        } catch (Exception e) {
            log.error("xm isOldVersion error: ", e);
            return true;
        }
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }

    @Override
    public CommonLiveConfig getLiveCommonConfig() {
        return liveConfig.getXm();
    }

    @Override
    public String getActualChannelId(long liveId, String currentChannelId) {
        Result<LiveCallProto.ResponseGetLineCode> getLineCodeResult = callService.getLineCode(liveId);
        if(getLineCodeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //获取失败直接置null，抛弃这一次的数据
            log.warn("xm getActualChannelId error, liveId={}`currentChannelId={}`rCode={}", liveId, currentChannelId, getLineCodeResult.rCode());
            return null;
        }
        return getLineCodeResult.target().getLineCode();
    }
}
