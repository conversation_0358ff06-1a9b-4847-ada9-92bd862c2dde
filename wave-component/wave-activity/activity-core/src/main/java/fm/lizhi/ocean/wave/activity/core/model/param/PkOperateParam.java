package fm.lizhi.ocean.wave.activity.core.model.param;

import lombok.Data;
import api.activity.constants.WaveLivePkOperateEnum;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class PkOperateParam {

    /**
     * 用户操作枚举
     * @see WaveLivePkOperateEnum
     */
    @NotNull
    private Integer operate;

    /**
     * 直播id
     */
    @NotNull
    @Min(1L)
    private Long liveId;


    /**
     * pk对方的直播id
     */
    private Long targetLiveId;

    /**
     * pkMatchId
     */
    private Long matchId;

    /**
     * pk状态\
     * @see api.activity.constants.WaveLivePkStatusEnum
     */
    private Integer status;

    /**
     * 系统邀请ID
     */
    private Long systemInvitedId;
}
