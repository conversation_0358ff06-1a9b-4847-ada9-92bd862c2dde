package fm.lizhi.ocean.wave.comment.core.remote.bean;

import lombok.Data;

import java.util.List;

/**
 * 简单用户对象
 * <AUTHOR>
 */
@Data
public class SimpleUser {
    /**
     * 用户id
     */
    private long userId;
    /**
     * 用户昵称
     */
    private String name;
    /**
     * 用户头像
     */
    private String portrait;
    /**
     * 用户性别 0男 1女
     */
    private int gender;

    /**
     * 贵宾卡标识（西米独有）
     */
    private List<String> roomVipUrls;

    /**
     * 贵宾 昵称的颜色 （西米独有）
     */
    private List<Long> nameColorsList;

    /**    ximi独有
     *     MONTH_ROOM_VIP(1, "月贵宾"),
     *     YEAR_ROOM_VIP(2, "年贵宾"),
     */
    private Integer userRoomVipStatus;
}
