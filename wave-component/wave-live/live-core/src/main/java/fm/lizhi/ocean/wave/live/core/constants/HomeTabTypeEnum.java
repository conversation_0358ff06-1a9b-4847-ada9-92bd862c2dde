package fm.lizhi.ocean.wave.live.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 首页tab类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum HomeTabTypeEnum {
    /**
     * 签约列表
     */
    SIGN_LIVE(1),

    /**
     * 关注列表
     */
    FOLLOW_USER_LIVE(2),

    /**
     * 官方频道
     */
    OFFICIAL_LIVE(3),

    /**
     * 推荐列表
     */
    RECOMMEND_LIVE(4);

    private int type;

    public static HomeTabTypeEnum getByType(int type) {
        for (HomeTabTypeEnum tabTypeEnum : HomeTabTypeEnum.values()) {
            if (tabTypeEnum.getType() == type) {
                return tabTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否存在改类型
     *
     * @param type tab类型
     * @return true: 存在，false: 不存在
     */
    public static boolean isExistType(int type) {
        return getByType(type) != null;
    }
}
