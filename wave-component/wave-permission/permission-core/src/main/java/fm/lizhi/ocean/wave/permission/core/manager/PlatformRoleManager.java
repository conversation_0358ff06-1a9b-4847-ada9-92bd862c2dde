package fm.lizhi.ocean.wave.permission.core.manager;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.param.GetLiveModeRequestParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveModeResult;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.util.EnvUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.constants.PermissionCodeEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineLive;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineRequest;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineUser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.StatementEngine;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.StatementFacade;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.StatementResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.EngineConstants;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.ResourceType;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.StatementFactory;
import fm.lizhi.ocean.wave.permission.core.model.vo.PermissionRoleVO;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PermissionBean;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PlatformRoleBean;
import fm.lizhi.ocean.wave.platform.api.permission.bean.StatementDslResMappingBean;
import fm.lizhi.ocean.wave.platform.api.permission.constants.PermissionTypeEnum;
import fm.lizhi.ocean.wave.platform.api.permission.constants.StatementResourceTypeEnum;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPermissionByRole;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPlatformRole;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetStatementDslList;
import fm.lizhi.ocean.wave.platform.api.permission.service.PermissionService;
import fm.lizhi.ocean.wave.platform.api.permission.service.PlatformRoleService;
import fm.lizhi.ocean.wave.platform.api.permission.service.StatementService;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 房间角色管理类
 * @author: guoyibin
 * @create: 2023/05/19 19:26
 */
@Slf4j
@Component
public class PlatformRoleManager {

    @Autowired
    private LiveService liveService;
    @Autowired
    private StatementFactory statementFactory;
    @Autowired
    private StatementEngine statementEngine;
    @Autowired
    private StatementService statementService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PlatformRoleService platformRoleService;
    @Autowired
    private ProcessorV2Factory processorFactory;

    /**
     * 判定用户是否拥有对应的角色权限
     *
     * @param permissionCheckParam
     * @return
     */
    public boolean havePermission(PermissionCheckParam permissionCheckParam) {
        if (permissionCheckParam.getPlatformRoleId() == PlatformRoleEnum.ROLE_USER.getRoleId()) {
            return false;
        }

        ExecutorContext executorContext = genExecutorContext(permissionCheckParam);
        if (log.isDebugEnabled()) {
            log.debug("executorContext={}", JsonUtil.dumps(executorContext));
        }

        //查询角色的策略
        String roleId = String.valueOf(permissionCheckParam.getPlatformRoleId());
        Result<List<StatementDslResMappingBean>> roleStatementResult = statementService.getStatementDslList(new RequestGetStatementDslList()
                .setResourceType(StatementResourceTypeEnum.ROLE)
                .setAppId(executorContext.getRequest().getAppId())
                .setResourceCodes(Lists.newArrayList(roleId)));
        if (RpcResult.isFail(roleStatementResult) || CollectionUtils.isEmpty(roleStatementResult.target())) {
            return false;
        }

        //执行角色策略
        StatementDslResMappingBean resMappingBean = roleStatementResult.target().get(0);
        StatementFacade statementFacade = statementFactory.createStatement(resMappingBean.getStatementDsl());

        //执行角色策略
        StatementResult roleResult = statementEngine.execute(statementFacade, executorContext);
        List<String> roleIdStrs = roleResult.mergeResources(ResourceType.ROLE);
        if (log.isDebugEnabled()) {
            log.debug("roleResult={}, roleIdStrs={}", JsonUtil.dumps(roleResult), JsonUtil.dumps(roleIdStrs));
        }

        return roleIdStrs.contains(roleId);
    }

    /**
     * 查询用户权限
     * <p>
     *     流程
     *     <a href="https://lizhi2021.feishu.cn/wiki/KOfhwtn2siJ0cWkPL3McCZQQnGb#share-IJkodhL8AoQIsFxJe6IcmRsancb">流程设计</a>
     * </p>
     * @param permissionCheckParam
     * @return
     */
    public ResultVO<RolePermissionVO> getRolePermission(PermissionCheckParam permissionCheckParam) {
        ExecutorContext executorContext = genExecutorContext(permissionCheckParam);
        if (log.isDebugEnabled()) {
            log.debug("executorContext={}", JsonUtil.dumps(executorContext));
        }

        //查询用户角色
        List<Integer> roleIds = getUserRoleIds(executorContext);
        if (CollectionUtils.isEmpty(roleIds)) {
            return ResultVO.failure(PermissionCodeEnum.HAVE_ROOM_NO_ROLE_ERROR);
        }

        //查询角色关联权限
        List<PermissionBean> permissionBeans = getRoleRefPermission(roleIds);
        if (CollectionUtils.isEmpty(permissionBeans)) {
            return ResultVO.failure(PermissionCodeEnum.HAVE_NO_PERMISSION);
        }

        //执行权限关联策略
        List<String> passPermissions = executePermissionStatement(permissionBeans, executorContext);
        if (CollectionUtils.isEmpty(passPermissions)) {
            return ResultVO.failure(PermissionCodeEnum.HAVE_NO_PERMISSION);
        }

        //构建结果
        Map<String, Integer> permissionTypeMap = permissionBeans.stream().collect(Collectors.toMap(PermissionBean::getPermissionCode, PermissionBean::getPermissionType, (k1, k2) -> k1));
        List<String> operations = new ArrayList<>();
        List<String> modules = new ArrayList<>();
        for (String permissionCode : passPermissions) {
            Integer permissionType = permissionTypeMap.getOrDefault(permissionCode, -1);
            if (PermissionTypeEnum.MODULE.getValue() == permissionType) {
                modules.add(permissionCode);
            } else if (PermissionTypeEnum.OPERATION.getValue() == permissionType) {
                operations.add(permissionCode);
            }
        }

        RolePermissionVO vo = new RolePermissionVO()
                .setRoles(buildRoles(roleIds))
                .setModules(modules)
                .setOperations(operations);
        if (log.isDebugEnabled()) {
            log.debug("getRolePermission result={}", JsonUtil.dumps(vo));
        }
        return ResultVO.success(vo);
    }

    /**
     * 构建角色列表
     * @param roleIds
     * @return
     */
    private List<PermissionRoleVO> buildRoles(List<Integer> roleIds){

        IRoomRoleProcessor processor = processorFactory.getProcessor(IRoomRoleProcessor.class);
        List<Integer> filterIds = processor.roleVoListFilter(roleIds);

        Result<List<PlatformRoleBean>> roleNameResult = platformRoleService.getPlatformRole(new RequestGetPlatformRole().setRoleIds(filterIds));
        Map<Integer, String> roleNameMap = new HashMap<>();
        if (RpcResult.isSuccess(roleNameResult)) {
            roleNameMap = roleNameResult.target().stream()
                    .collect(Collectors.toMap(PlatformRoleBean::getId, PlatformRoleBean::getRoleName));
        }

        List<PermissionRoleVO> roles = new ArrayList<>();
        for (Integer roleId : filterIds) {
            PermissionRoleVO roleVO = new PermissionRoleVO();
            roleVO.setRoleId(roleId);
            roleVO.setRoleName(roleNameMap.getOrDefault(roleId, ""));
            roles.add(roleVO);
        }
        return roles;
    }

    /**
     * 执行权限关联的策略
     * @param permissionBeans
     * @param executorContext
     * @return
     */
    private List<String> executePermissionStatement(List<PermissionBean> permissionBeans, ExecutorContext executorContext){
        List<String> permissionCodes = permissionBeans.stream().map(PermissionBean::getPermissionCode).distinct()
                .collect(Collectors.toList());
        Result<List<StatementDslResMappingBean>> permissionDslResult = statementService.getStatementDslList(new RequestGetStatementDslList()
                .setResourceType(StatementResourceTypeEnum.PERMISSION)
                .setResourceCodes(permissionCodes)
                .setAppId(executorContext.getRequest().getAppId())
        );
        if (RpcResult.isFail(permissionDslResult)) {
            return Collections.emptyList();
        }

        //执行权限策略 只执行有关联策略的权限 没有关联策略的权限默认通过
        List<String> refPermissionCodes = permissionDslResult.target().stream().flatMap(v -> v.getResourceCode().stream()).collect(Collectors.toList());
        List<String> passPermissions = new ArrayList<>();
        for (String refPermission : permissionCodes) {
            if (!refPermissionCodes.contains(refPermission)) {
                // 没有关联的策略，直接通过
                passPermissions.add(refPermission);
            }
        }

        //执行策略
        List<String> permissionDslList = permissionDslResult.target().stream().map(StatementDslResMappingBean::getStatementDsl).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(permissionDslList)) {
            List<StatementFacade> permissionStatementList = statementFactory.createStatement(permissionDslList);

            StatementResult permissionEngineResult = statementEngine.execute(permissionStatementList, executorContext);
            List<String> enginePermissionCodes = permissionEngineResult.mergeResources(ResourceType.PERMISSION);
            if (log.isDebugEnabled()) {
                log.debug("permissionEngineResult={},enginePermissionCodes={}", JsonUtil.dumps(permissionEngineResult), JsonUtil.dumps(enginePermissionCodes));
            }
            passPermissions.addAll(enginePermissionCodes);
        }

        return passPermissions;
    }

    /**
     * 查询角色关联权限列表
     * @param roleIds
     * @return
     */
    private List<PermissionBean> getRoleRefPermission(List<Integer> roleIds){
        Result<List<PermissionBean>> permissionResult = permissionService.getPermissionByRole(new RequestGetPermissionByRole()
                .setRoleIds(roleIds)
                .setAppId(ContextUtils.getBusinessEvnEnum().getAppId())
        );
        if (RpcResult.isFail(permissionResult) || CollectionUtils.isEmpty(permissionResult.target())) {
            return Collections.emptyList();
        }

        return permissionResult.target();
    }

    /**
     * 查询用户角色列表
     * @param executorContext
     * @return
     */
    private List<Integer> getUserRoleIds(ExecutorContext executorContext){
        //查询所有角色策略
        Result<List<StatementDslResMappingBean>> roleStatementResult = statementService.getStatementDslList(new RequestGetStatementDslList()
                .setResourceType(StatementResourceTypeEnum.ROLE)
                .setAppId(executorContext.getRequest().getAppId())
        );
        if (RpcResult.isFail(roleStatementResult)) {
            return Collections.emptyList();
        }

        //执行角色策略
        List<String> roleDsls = roleStatementResult.target().stream().map(StatementDslResMappingBean::getStatementDsl).collect(Collectors.toList());
        log.debug("roleDslsSize={}", roleDsls.size());
        List<StatementFacade> roleStatementList = statementFactory.createStatement(roleDsls);

        StatementResult roleResult = statementEngine.execute(roleStatementList, executorContext);
        List<String> roleIdStrs = roleResult.mergeResources(ResourceType.ROLE);
        if (log.isDebugEnabled()) {
            log.debug("roleResult={}, roleIdsStrs={}", JsonUtil.dumps(roleResult), JsonUtil.dumps(roleIdStrs));
        }

        //授予普通用户角色
        List<Integer> roleIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(roleIdStrs)) {
            roleIds.add(PlatformRoleEnum.ROLE_USER.getRoleId());
        } else {
            roleIds.addAll(roleIdStrs.stream().map(Integer::parseInt).collect(Collectors.toList()));
        }

        return roleIds;
    }

    /**
     * 构建执行上下文
     * @param permissionCheckParam
     * @return
     */
    private ExecutorContext genExecutorContext(PermissionCheckParam permissionCheckParam){
        ExecutorContext.ExecutorContextBuilder builder = ExecutorContext.builder();

        EngineUser.EngineUserBuilder userBuilder = EngineUser.builder()
                .userId(permissionCheckParam.getUserId());
        builder.user(userBuilder.build());

        EngineRequest.EngineRequestBuilder requestBuilder = EngineRequest.builder()
                .requestTime(System.currentTimeMillis())
                .clientVersion(ContextUtils.getContext().getHeader().getClientVersion())
                .appId(ContextUtils.getBusinessEvnEnum().getAppId());
        if (EnvUtils.isPro()) {
            requestBuilder.env(EngineConstants.ENV_PRO);
        } else if (EnvUtils.isPre()) {
            requestBuilder.env(EngineConstants.ENV_PRE);
        }
        builder.request(requestBuilder.build());

        EngineLive.EngineLiveBuilder liveBuilder = EngineLive.builder()
                .njId(permissionCheckParam.getNjId())
                .roomId(permissionCheckParam.getRoomId());
        //liveId
        if (permissionCheckParam.getNjId() != null && permissionCheckParam.getNjId() > 0) {
            Result<GetLiveResult> liveResult = liveService.getLastLiveNoCacheByUserId(permissionCheckParam.getNjId());
            if (RpcResult.isSuccess(liveResult) && liveResult.target() != null) {
                Long liveId = liveResult.target().getLive().getId();
                liveBuilder.liveId(liveId);
                //直播模式
                LiveModeEnum liveModeEnum = getLiveMode(liveId);
                liveBuilder.liveModel(liveModeEnum.name());
            }
        }
        builder.live(liveBuilder.build());

        return builder.build();
    }

    /**
     * 查询直播模式
     * @param liveId
     * @return
     */
    private LiveModeEnum getLiveMode(Long liveId) {
        Result<GetLiveModeResult> modeResultResult = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(liveId).build());
        if(modeResultResult.rCode()!=GeneralRCode.GENERAL_RCODE_SUCCESS){
            log.warn("getLiveMode error liveId={},rCode={}",liveId,modeResultResult.rCode());
            return LiveModeEnum.UNKNOWN;
        }
        return modeResultResult.target().getLiveModeEnum();
    }
}
