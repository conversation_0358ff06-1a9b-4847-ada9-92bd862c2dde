package fm.lizhi.ocean.wave.im.core.remote.service.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.social.api.CloseFriendRelationService;
import fm.lizhi.hy.social.protocol.CloseFriendRelationServiceProto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.im.core.remote.service.ICloseFriendRelationServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HyCloseFriendRelationServiceRemote implements ICloseFriendRelationServiceRemote {

    @Autowired
    CloseFriendRelationService closeFriendRelationService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    @Override
    public Result<Void> sendPokeMsg(Long senderId, Long receiverId) {
        Result<CloseFriendRelationServiceProto.ResponseSendPokeMsgEachOther> result = closeFriendRelationService.sendPokeMsgEachOther(senderId, receiverId);
        if (result.rCode()!= GeneralRCode.GENERAL_RCODE_SUCCESS){
            log.warn("sendPokeMsgEachOther fail,senderId={},receiverId={}",senderId,receiverId);
            return new Result<>(ICloseFriendRelationServiceRemote.SEND_POKE_MSG_FAIL,null);
        }
        return new Result<>(ICloseFriendRelationServiceRemote.SEND_POKE_MSG_SUCCESS,null);

    }
}
