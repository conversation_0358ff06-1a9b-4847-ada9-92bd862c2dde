package fm.lizhi.ocean.wave.live.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.ExitLiveProcessor;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.bean.ExitLivePostBean;
import fm.lizhi.ocean.wave.live.core.extension.exitlive.bean.ExitLivePreBean;
import fm.lizhi.ocean.wave.live.core.facade.LiveAmusementFacade;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveRoomBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveModeRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.param.LeavePopularGroupParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveModeRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveRoomServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.live.core.remote.service.IPopularityServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 退房事件管理器
 * @author: guoyibin
 * @create: 2023/05/25 20:58
 */
@Slf4j
@Component
public class LeaveLiveManager {

    @MyAutowired
    private IPopularityServiceRemote popularityServiceRemote;

    @Autowired
    private LiveAmusementFacade liveAmusementFacade;

    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;

    @MyAutowired
    private ILiveRoomServiceRemote liveRoomServiceRemote;

    @Autowired
    private ProcessorFactory processorFactory;

    public Boolean leaveLive(long liveId) {
        String deviceId = ContextUtils.getContext().getHeader().getDeviceId();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getContext().getHeader().getAppId();
        String clientIp = ContextUtils.getContext().getHeader().getIp();

        Result<GetLiveRemoteResult> getLiveRemoteResultResult = liveServiceRemote.getLive(GetLiveRemoteParam.builder().liveId(liveId).build());
        if(getLiveRemoteResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("get live info failed, liveId:{}, rCode:{}", liveId, getLiveRemoteResultResult.rCode());
            return false;
        }
        LiveBean liveBean = getLiveRemoteResultResult.target().getLiveBean();

        Result<GetLiveRoomResult> getLiveRoomResultResult = liveRoomServiceRemote.getLiveRoom(liveBean.getLiveRoomId());
        if(getLiveRoomResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("get live room info failed, liveRoomId:{}, rCode:{}", liveBean.getLiveRoomId(), getLiveRoomResultResult.rCode());
            return false;
        }
        LiveRoomBean liveRoomBean = getLiveRoomResultResult.target().getLiveRoomBean();

        ExitLiveProcessor exitLiveProcessor = processorFactory.getProcessor(appId, ExitLiveProcessor.class);

        exitLiveProcessor.exitLivePre(ExitLivePreBean.builder().liveBean(liveBean).liveRoomBean(liveRoomBean).clientIp(clientIp).userId(userId).deviceId(deviceId).build());

        Result<Void> leavePopularityResult = popularityServiceRemote.leavePopularityGroup(LeavePopularGroupParam.builder().liveId(liveId)
                .userId(userId).deviceId(deviceId).appId(appId).clientId(clientIp).build());
        // 退出人气组失败需要继续往下处理，不中断
        if(leavePopularityResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("leave popularity group failed, liveId:{}, userId:{}, deviceId:{}, appId:{}, clientIp:{}, rCode:{}",
                    liveId, userId, deviceId, appId, clientIp, leavePopularityResult.rCode());
        }

        //清理娱乐模式信息
        clearAmuseInfo(liveId, userId);

        exitLiveProcessor.exitLivePost(ExitLivePostBean.builder().liveBean(liveBean).liveRoomBean(getLiveRoomResultResult.target().getLiveRoomBean())
                .appId(appId).ip(ContextUtils.getContext().getHeader().getIp()).clientVersion(ContextUtils.getContext().getHeader().getClientVersion())
                .userId(userId).deviceId(deviceId).build());

        return true;
    }

    /**
     * 清理娱乐模式信息
     * @param liveId
     * @param userId
     */
    private void clearAmuseInfo(long liveId, long userId) {
        Result<GetLiveModeRemoteResult> getLiveModeResult = liveServiceRemote.getLiveMode(GetLiveModeRemoteParam.builder().liveId(liveId).build());
        if(getLiveModeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("clearAmuseInfo, get live mode error, liveId:{}, rCode:{}", liveId, getLiveModeResult.rCode());
            return;
        }

        if(!LiveModeEnum.NORMAL_AMUSEMENT.equals(getLiveModeResult.target().getLiveModeEnum())) {
            log.warn("clearAmuseInfo, live is not amusement mode, liveId:{}", liveId);
            return;
        }
        // 清除用户娱乐模式的相关信息(排麦或是上座信息)
        liveAmusementFacade.removeUserAmusementInfo(liveId, userId);
    }
}
