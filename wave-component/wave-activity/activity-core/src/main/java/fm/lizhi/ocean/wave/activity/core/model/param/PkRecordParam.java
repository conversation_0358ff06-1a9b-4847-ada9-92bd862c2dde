package fm.lizhi.ocean.wave.activity.core.model.param;

import lombok.Data;
import org.springframework.lang.NonNull;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@Data
public class PkRecordParam {

    @NonNull
    @Min(0)
    private int pageNo;

    @NonNull
    @Max(100)
    private int pageSize;

    @NonNull
    @Min(0L)
    private Long liveId;
}
