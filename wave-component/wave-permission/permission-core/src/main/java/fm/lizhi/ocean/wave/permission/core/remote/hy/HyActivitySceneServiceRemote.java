package fm.lizhi.ocean.wave.permission.core.remote.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.idl.activityScene.api.ActivitySceneIDLService;
import fm.lizhi.hy.idl.activityScene.dto.request.CheckPcPermissionReq;
import fm.lizhi.hy.idl.activityScene.dto.response.CheckPcPermissionResp;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.permission.core.remote.IActivitySceneServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class HyActivitySceneServiceRemote extends RemoteServiceInvokeFacade implements IActivitySceneServiceRemote {
    @Autowired
    private ActivitySceneIDLService activitySceneIDLService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    @Override
    public boolean checkPermission(long liveId, long userId) {
        CheckPcPermissionReq request = CheckPcPermissionReq.builder().liveId(liveId).userId(userId).build();
        try{
            Result<CheckPcPermissionResp> result = activitySceneIDLService.checkPcPermission(request);
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && Objects.nonNull(result.target())) {
                return Boolean.TRUE.equals(result.target().getHasPermission());
            }
        } catch (Exception e) {
            log.error("checkPermission fail, userId:{} , liveId:{} , exception: ", userId, liveId, e);
        }
        return false;
    }
}
