package fm.lizhi.ocean.wave.user.core.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.disaster.annotation.KafkaBusinessBind;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.user.core.manager.LoginManager;
import fm.lizhi.ocean.wave.user.core.manager.UserPushManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/13 18:29
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "public-kafka250-bootstrap-server")
@KafkaBusinessBind(bindBusiness = BusinessEvnEnum.XIMI)
@SubscriptionOn(enable = "${xm.kafka.consumer.enable}")
public class UserXmKafkaConsumer {

    @Autowired
    private LoginManager loginManager;
    @Autowired
    private UserPushManager userPushManager;

    /**
     * 审核封禁消息消费者
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_commons_ximi_api",
            group = "lz_ocean_wave_user_ban_ximi_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleBanCommentMsg(String body) {
        log.info("xm handleBanCommentMsg, body={}", body);
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            JSONObject action = JSON.parseObject(msg);
            //审核封禁西米用户 type = 3
            int type = action.getIntValue("type");
            if (type != 3) {
                return;
            }
            long userId = action.getLongValue("userId");
            if (userId <= 0) {
                log.warn("invalid param, message={}", msg);
                return;
            }
            Result<String> result = loginManager.getLoginTokenByUserId(userId);
            if (RpcResult.isFail(result) || StringUtils.isBlank(result.target())) {
                return;
            }

            String loginToken = result.target();

            // 这里维护下上下文, 泛化调用使用到的context
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            loginManager.loginOut(loginToken);
            userPushManager.pushKickOut(loginToken, 2, BusinessEvnEnum.XIMI.getAppId());
            log.info("handleBanCommentMsg. userId={}, loginToken={}", userId, loginToken);
        } catch (Exception e) {
            log.error("handleBanCommentMsg error, msg:{}, orgMsg:{}", msg, body, e);
        } finally {
            ContextUtils.clearContext();
        }

    }

}
