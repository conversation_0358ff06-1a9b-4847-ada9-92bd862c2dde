package fm.lizhi.ocean.wave.user.core.extension.usertab.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.comment.api.SingService;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.core.extension.usertab.UserTabProcess;
import fm.lizhi.ocean.wave.user.core.manager.strategy.UserTabGetContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 黑叶用户tab处理
 *
 * <AUTHOR>
 */
@Component
public class HyUserTabProcess implements UserTabProcess {

    @Autowired
    private SingService singService;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean isSupportMusicWorkTab(UserTabGetContext context) {
        return false;
    }

    @Override
    public boolean isSupportSongListTab(UserTabGetContext context) {
        Result<Boolean> result = singService.getIsSignSingerRoomCache(context.getTargetUserId());
        if (RpcResult.isFail(result)) {
            return false;
        }

        return result.target();
    }
}
