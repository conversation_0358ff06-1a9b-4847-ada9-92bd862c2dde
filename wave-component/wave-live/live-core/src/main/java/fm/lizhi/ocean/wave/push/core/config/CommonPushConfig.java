package fm.lizhi.ocean.wave.push.core.config;

/**
 * 公共推送配置
 */
public interface CommonPushConfig {

    /**
     * 飘屏开关
     */
    public boolean isNoticeSwitch();

    /**
     * 主播可以看到飘屏开关
     */
    public boolean isNoticeGiftAnchorSwitch();

    /**
     * 查询飘屏最大条数
     */
    public Integer getNoticeMaxCount();

    /**
     * 获取飘屏请求间隔
     */
    public Integer getBroadcastRequestInterval();

}
