package fm.lizhi.ocean.wave.user.core.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public enum RelatedUserEnum {
    FOLLOW(1, "关注"),
    FANS(2, "粉丝"),
    MUTUAL_FOLLOW(3, "相互关注"),

    ;

    private int relation;
    private String desc;

    RelatedUserEnum(int relation, String desc) {
        this.relation = relation;
        this.desc = desc;
    }

    private static Map<Integer, RelatedUserEnum> map = new HashMap<>();

    static {
        Arrays.stream(RelatedUserEnum.values())
                .forEach(object -> map.put(object.getRelation(), object));
    }

    public static RelatedUserEnum from(int value) {
        return map.getOrDefault(value, RelatedUserEnum.FOLLOW);
    }

    public static boolean isRelated(int relation) {
        return Arrays.stream(RelatedUserEnum.values())
                .anyMatch(object -> object.getRelation() == relation);
    }

    public int getRelation() {
        return relation;
    }

    public String getDesc() {
        return desc;
    }
}
